﻿using System;
using Gateway.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities;
using Portal.Gateway.Contracts.Entities.Dto.NotificationManagement.Notification;
using Portal.Gateway.Contracts.Entities.Dto.NotificationManagement.Notification.Requests;
using Portal.Gateway.Contracts.Entities.Dto.NotificationManagement.Notification.Responses;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Extensions;
using Portal.Gateway.Contracts.Services;
using Portal.Gateway.Contracts.Settings.AppSettings;
using Portal.Gateway.Contracts.UnitOfWork;
using Portal.Gateway.Entity.Context;
using Portal.Gateway.Entity.Entities.Portal;
using Portal.Gateway.Resources;
using System.Linq;
using System.Threading.Tasks;
using Portal.Gateway.Contracts.Entities.Dto;
using System.Collections.Generic;
using Gateway.Firebase;
using Elastic.Clients.Elasticsearch.TextStructure;
using Gateway.Firebase.Dto;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Application;
using Serilog;

namespace Portal.Gateway.Services
{
    public class NotificationManagementService : BaseService, INotificationManagementService
    {
        private readonly IUnitOfWork<PortalDbContext> _unitOfWork;
        private readonly IFirebaseRepository _fireBaseRepository;
        private static readonly Serilog.ILogger Logger = Log.ForContext<NotificationManagementService>();

        public NotificationManagementService(IOptions<AppSettings> appSettings, IUnitOfWork<PortalDbContext> unitOfWork, IFirebaseRepository fireBaseRepository) : base(appSettings)
        {
            _unitOfWork = unitOfWork;
            _fireBaseRepository = fireBaseRepository;
        }

        public async Task<Pagination<GetPaginatedNotificationListResponseDto>> GetPaginatedNotificationListAsync(GetPaginatedNotificationListRequestDto request)
        {
            var paginationResult = new Pagination<GetPaginatedNotificationListResponseDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryNotification = _unitOfWork.GetRepository<PushNotification>().Entities
                .Include(i => i.PushNotificationHistories)
                .Where(p => p.IsActive)
;
            if (request.NationalityId.IsNumericAndGreaterThenZero())
                queryNotification = queryNotification.Where(s => s.PushNotificationHistories.Any(r => r.IsActive && !r.IsDeleted && r.NationalityId == request.NationalityId));

            if (request.LocationId.IsNumericAndGreaterThenZero())
                queryNotification = queryNotification.Where(s => s.PushNotificationHistories.Any(r => r.IsActive && !r.IsDeleted && r.LocationId == request.LocationId));

            if (request.StartDate.HasValue)
                queryNotification = queryNotification.Where(p => p.SendAt >= request.StartDate.Value);

            if (request.EndDate.HasValue)
                queryNotification = queryNotification.Where(p => p.SendAt < request.EndDate.Value.AddDays(1));

            if (request.StatusId.IsNumericAndGreaterThenZero())
                queryNotification = queryNotification.Where(p => p.StatusId == request.StatusId);

            paginationResult.TotalItemCount = queryNotification.Count();

            var notificationList = await queryNotification
                .OrderByDescending(s => s.Id)
                .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                .Take(request.Pagination.PageSize)
                .ToListAsync();

            var newNotifications = new GetPaginatedNotificationListResponseDto() { Notifications = [] };

            var countries = await _unitOfWork.GetRepository<Country>().Entities.ToListAsync();

            foreach (var newNotification in notificationList.Select(item => new NotificationResponseDto
                     {
                         EncryptedId = item.Id.ToEncrypt(),
                         NotificationNumber = item.Id.ToApplicationNumber(),
                         NotificationTitle = item.Title,
                         StatusId = item.StatusId,
                         SendTime = item.SendAt?.ToString("dd/MM/yyyy HH:mm"),
                         Location = item.LastSentLocationId != null ? GetCountryNameByLanguageId(request.LanguageId,countries.FirstOrDefault(s => s.Id == item.LastSentLocationId)) : null,
                         Nationality = item.LastSentNationalityId != null ? GetCountryNameByLanguageId(request.LanguageId,countries.FirstOrDefault(s => s.Id == item.LastSentNationalityId)) : null,
                     }))
            {
                newNotifications.Notifications.Add(newNotification);
            }

            #region Filter

            if (!string.IsNullOrEmpty(request.GenericFilter))
            {
                newNotifications.Notifications = newNotifications.Notifications.Where(s =>
                        s.NotificationTitle.Contains(request.GenericFilter) ||
                        s.NotificationNumber.Contains(request.GenericFilter) ||
                        s.Location.Contains(request.GenericFilter) ||
                        s.Nationality.Contains(request.GenericFilter))
                    .ToList();

                paginationResult.TotalItemCount = newNotifications.Notifications.Count;
            }

            #endregion

            paginationResult.Items.Add(newNotifications);

            return paginationResult;
        }

        public async Task<GetNotificationDetailResponseDto> GetNotificationDetailAsync(int id, int languageId)
        {
            var countries = await _unitOfWork.GetRepository<Country>().Entities.ToListAsync();

            var existingNotification = await _unitOfWork.GetRepository<PushNotification>().Entities
                .Include(i => i.PushNotificationTranslations)
                .Where(p => p.Id == id)
                .FirstOrDefaultAsync();

            if (existingNotification == null)
                throw new NoFoundDataPortalException(nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue());

            var user = await _unitOfWork.GetRepository<User>().Entities
                .Where(s => s.IsActive && !s.IsDeleted && s.Id == existingNotification.CreatedBy).FirstOrDefaultAsync();

            var result = new GetNotificationDetailResponseDto
            {
                Notification = new NotificationResponseDto()
                {
                    Location = existingNotification.LastSentLocationId != null ? GetCountryNameByLanguageId(languageId, countries.FirstOrDefault(s => s.Id == existingNotification.LastSentLocationId)) : null,
                    Nationality = existingNotification.LastSentNationalityId != null ? GetCountryNameByLanguageId(languageId, countries.FirstOrDefault(s => s.Id == existingNotification.LastSentNationalityId)) : null,
                    NationalityId = existingNotification.LastSentNationalityId,
                    LocationId = existingNotification.LastSentLocationId,
                    EncryptedId = existingNotification.Id.ToEncrypt(),
                    NotificationNumber = existingNotification.Id.ToApplicationNumber(),
                    StatusId = existingNotification.StatusId,
                    NotificationTitle = existingNotification.Title,
                    SendTime = existingNotification.SendAt?.ToString("dd/MM/yyyy HH:mm"),
                    ScheduledAt = existingNotification.ScheduledAt,
                    CreatedAt = existingNotification.CreatedAt,
                    CreatedBy = $"{user.Name} {user.Surname}",
                    SendAt = existingNotification.SendAt,
                    Translations = existingNotification.PushNotificationTranslations.Select(s => new NotificationTranslationResponseDto()
                    {
                        Id = s.Id,
                        Name = s.Content,
                        LanguageId = s.LanguageId,
                        IsActive = s.IsActive,
                    }).ToList()
                }
            };

            return result;
        }

        public async Task<NotificationResponseDto> GetScheduledNotificationsForJobAsync()
        {
            bool success = false;
            var attemp = 0;
            PushNotification getPushNotificationLog = null;
            var statusesToCheck = new[]
{
                (byte)PushNotificationJobStatus.FailOnSending,
                (byte)PushNotificationJobStatus.Exception,
            };

            while (!success && attemp < 10)
            {
                getPushNotificationLog = await _unitOfWork.GetRepository<PushNotification>().Entities
                    .Include(i => i.PushNotificationTranslations)
                 .Where(p =>
                    (p.JobStatus == (byte)PushNotificationJobStatus.Initial) ||
                    (statusesToCheck.Contains(p.JobStatus) && p.NextAttemptDateTime.HasValue && p.NextAttemptDateTime <= DateTime.UtcNow))
                 .OrderBy(s => s.Id)
                 .FirstOrDefaultAsync();

                if (getPushNotificationLog == null)
                {
                    return null;
                }

                try
                {
                    getPushNotificationLog.JobStatus = (byte)InsuranceCancelOrderStatus.OperationStarted;
                    _unitOfWork.GetRepository<PushNotification>().Update(getPushNotificationLog);
                    await _unitOfWork.SaveChangesAsync();
                    success = true;
                }
                catch (DbUpdateConcurrencyException ex)
                {
                    Logger.Warning(ex, "Concurrency exception occurred while fetching CancelInsuranceOrder. Retrying...");
                    attemp++;
                }
            }

            if (!success)
                return null;

            return new NotificationResponseDto()
            {
                Id = getPushNotificationLog.Id,
                LocationId = getPushNotificationLog.LastSentLocationId,
                NationalityId = getPushNotificationLog.LastSentNationalityId,
                CreatedAt = getPushNotificationLog.CreatedAt,
                StatusId = getPushNotificationLog.StatusId,
                NotificationTitle = getPushNotificationLog.Title,
                ScheduledAt = getPushNotificationLog.ScheduledAt,
                Translations = getPushNotificationLog.PushNotificationTranslations.Select(s => new NotificationTranslationResponseDto()
                {
                    Id = s.Id,
                    Name = s.Content,
                    IsActive = s.IsActive,
                    LanguageId = s.LanguageId
                }).ToList()
            };
        }

        public async Task<DeleteResponseDto> DeleteNotificationAsync(DeleteRequestDto request)
        {
            var existingNotification = await _unitOfWork.GetRepository<PushNotification>().Entities
                .Where(p => !p.IsDeleted && p.Id == request.Id)
                .FirstOrDefaultAsync();

            if (existingNotification == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Branch).ToSiteResourcesValue()})");

            existingNotification.IsDeleted = true;
            existingNotification.DeletedBy = request.UserAuditId;
            existingNotification.DeletedAt = DateTime.Now;
            existingNotification.StatusId = (int)NotificationStatusType.Deleted;

            _unitOfWork.GetRepository<PushNotification>().Update(existingNotification);
            await _unitOfWork.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        public async Task<AddResponseDto> AddNotificationAsync(AddNotificationRequestDto request)
        {
            var notification = await _unitOfWork.GetRepository<PushNotification>().Entities
                                   .Where(p => !p.IsDeleted && p.IsActive && p.Title == request.NotificationTitle)
                                   .FirstOrDefaultAsync();


            if (notification != null)
            {
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Branch).ToSiteResourcesValue(request.LanguageId)})");
            }

            var notificationEntity = new PushNotification()
            {
                Title = request.NotificationTitle,
                CreatedAt = DateTime.Now,
                CreatedBy = request.UserAuditId,
                IsActive = true,
                IsDeleted = false,
                UserId = request.UserAuditId,
                StatusId = (int)NotificationStatusType.Draft,
            };

            await _unitOfWork.GetRepository<PushNotification>().AddAsync(notificationEntity);
            await _unitOfWork.SaveChangesAsync();

            var notificationTranslations = request.Translations.Select(s => new PushNotificationTranslation()
            {
                IsDeleted = false,
                IsActive = s.IsActive,
                Content = s.Name,
                CreatedAt = DateTime.Now,
                CreatedBy = request.UserAuditId,
                LanguageId = s.LanguageId,
                PushNotificationId = notificationEntity.Id
            }).ToList();

            await _unitOfWork.GetRepository<PushNotificationTranslation>().AddRangeAsync(notificationTranslations);
            await _unitOfWork.SaveChangesAsync();

            return new AddResponseDto { Id = notificationEntity.Id };
        }

        public async Task<UpdateResponseDto> UpdateNotificationAsync(UpdateNotificationRequestDto request)
        {
            var notification = await _unitOfWork.GetRepository<PushNotification>().Entities
                .Include(i => i.PushNotificationTranslations)
                .Where(p => !p.IsDeleted && p.IsActive && p.Id == request.Id)
                .FirstOrDefaultAsync();

            if (notification == null)
            {
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_TypeNotFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Notification).ToSiteResourcesValue(request.LanguageId)})");
            }

            notification.Title = request.NotificationTitle;
            notification.UpdatedBy = request.UserAuditId;
            notification.UpdatedAt = DateTime.Now;

            _unitOfWork.GetRepository<PushNotification>().Update(notification);
            await _unitOfWork.SaveChangesAsync();

            foreach (var translationData in notification.PushNotificationTranslations)
            {
                var translation = request.Translations.Find(s => s.LanguageId == translationData.LanguageId);

                if (translation != null)
                {
                    translationData.Content = translation.Name;
                    translationData.IsActive = translation.IsActive;
                    _unitOfWork.GetRepository<PushNotificationTranslation>().Update(translationData);
                    await _unitOfWork.SaveChangesAsync();
                }
            }

            return new UpdateResponseDto { Result = true};
        }

        public async Task<UpdateResponseDto> SendNotificationAsync(SendNotificationRequestDto request)
        {
            var notification = await _unitOfWork.GetRepository<PushNotification>().Entities
                .Include(i => i.PushNotificationTranslations)
                .Where(p => !p.IsDeleted && p.IsActive && p.Id == request.Id)
                .FirstOrDefaultAsync();

            if (notification == null)
            {
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_TypeNotFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Notification).ToSiteResourcesValue(request.LanguageId)})");
            }

            var topics = new List<string>();

            if (request.IsSendNow)
            {
                string locationIso2 = null;
                int? nationalityId = null;

                if (request.LocationId.IsNumericAndGreaterThenZero())
                {
                    notification.LastSentLocationId = request.LocationId;

                    var location = await _unitOfWork.GetRepository<Country>().Entities
                        .Where(s => s.Id == request.LocationId).FirstOrDefaultAsync();

                    if (location != null)
                    {
                        locationIso2 = location.ISO2;
                    }
                }

                if (request.NationalityId.IsNumericAndGreaterThenZero())
                {
                    notification.LastSentNationalityId = request.NationalityId;
                    nationalityId = request.NationalityId;
                }

                topics.Add(
                         (locationIso2, nationalityId) switch
                         {
                             (not null, null) => $"{locationIso2}_all",
                             (null, not null) => $"{nationalityId}_all",
                             (not null, not null) => $"{locationIso2}_{nationalityId}",
                             _ => "all"
                         }
                     );

                var customersToSend = await _unitOfWork.GetRepository<CustomerUserTopic>().Entities
                    .Where(s => s.IsActive && !s.IsDeleted && topics.Contains(s.Topic)).Select(s => new {Id = s.CustomerUserId , Topic = s.Topic})
                    .ToListAsync();

                foreach (var topic in topics)
                {
                    var customers = customersToSend.Where(s => s.Topic == topic).ToList();

                    foreach (var translation in request.Translations.Where(s => s.IsActive && !string.IsNullOrEmpty(s.Name)))
                    {
                        await _fireBaseRepository.SendMessageToTopic(new SendMessageToTopicRequest()
                        {
                            Topic = topic,
                            Message = translation.Name,
                            Title = notification.Title
                        });

                        foreach (var customer in customers)
                        {
                            var history = new PushNotificationHistory()
                            {
                                IsActive = true,
                                IsDeleted = false,
                                CreatedAt = DateTime.Now,
                                CreatedBy = request.UserAuditId,
                                LocationId = request.LocationId ?? 0,
                                NationalityId = request.NationalityId ?? 0,
                                PushNotificationId = notification.Id,
                                CustomerUserId = customer.Id,
                                Text = translation.Name,
                            };

                            await _unitOfWork.GetRepository<PushNotificationHistory>().AddAsync(history);
                            await _unitOfWork.SaveChangesAsync();
                        }
                    }
                }

                notification.StatusId = (int)NotificationStatusType.Sent;
                notification.SendBy = request.UserAuditId;
                notification.SendAt = DateTime.Now;
                 _unitOfWork.GetRepository<PushNotification>().Update(notification);
                await _unitOfWork.SaveChangesAsync();
            }

            if (request.IsSendScheduled)
            {
                notification.StatusId = (int)NotificationStatusType.Scheduled;
                notification.JobStatus = (int)PushNotificationJobStatus.Initial;
                notification.ScheduledAt = request.ScheduleTime;
                _unitOfWork.GetRepository<PushNotification>().Update(notification);
                await _unitOfWork.SaveChangesAsync();
            }

            return new UpdateResponseDto()
            {
                Result = true
            };
        }

        public async Task<UpdateResponseDto> UpdateScheduledPushNotification(NotificationResponseDto request)
        {
            var notification = await _unitOfWork.GetRepository<PushNotification>().Entities
                .FirstOrDefaultAsync(s => s.Id == request.Id);

            if (notification != null)
            {
                notification.JobStatus = request.JobStatus;
                notification.NextAttemptDateTime = request.NextAttemptDateTime;
                notification.JobRetryCount = request.JobRetryCount;

                if (notification.JobStatus == (byte)PushNotificationJobStatus.OperationCompleted)
                {
                    notification.StatusId = (byte)NotificationStatusType.Sent;
                    notification.SendAt = DateTime.Now;
                    notification.SendBy = notification.CreatedBy;

                    _unitOfWork.GetRepository<PushNotification>().Update(notification);

                    await _unitOfWork.SaveChangesAsync();
                }


                _unitOfWork.GetRepository<PushNotification>().Update(notification);

                await _unitOfWork.SaveChangesAsync();
            }


            return new UpdateResponseDto()
            {
                Result = true
            };
        }

        #region Private Methods

        private static string GetCountryNameByLanguageId(int languageId, Country country)
        {
            return languageId switch
            {
                (int)Language.Turkish => country.NameTr,
                _ => country.Name
            };
        }

        #endregion


    }
}
