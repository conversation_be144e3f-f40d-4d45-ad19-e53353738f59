﻿using FirebaseAdmin.Messaging;
using Gateway.Core.Pagination;
using Gateway.Extensions;
using Gateway.External.Application.Customer;
using Gateway.External.Application.Enums;
using Gateway.External.Application.IdentityServer;
using Gateway.External.Application.IdentityServer.Dto;
using Gateway.External.Application.Lookup;
using Gateway.External.Application.User.Dto;
using Gateway.External.Application.User.Dto.Request;
using Gateway.External.Application.User.Dto.Response;
using Gateway.External.Application.User.Validator;
using Gateway.External.Core.Context;
using Gateway.External.Entity.Entities.CustomerUser;
using Gateway.External.Persistence;
using Gateway.External.Resources;
using Gateway.Firebase;
using Gateway.Firebase.Dto;
using Gateway.Http;
using Gateway.Redis;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Gateway.External.Application.User
{
    public class UserService : IUserService
    {
        private static readonly ILogger Logger = Log.ForContext<UserService>();
        private readonly IContext _context;
        private readonly IRedisClient _redisClient;
        private readonly IValidationService _validationService;
        private readonly IConfiguration _configuration;
        private readonly IFirebaseRepository _firebaseRepository;
        private readonly ApiDbContext _dbContext;
        public IdentityServerEndpointRouting IsRouting;
        public WebHeaderCollection Header;

        public UserService(IContext context, IRedisClient redisClient, IValidationService validationService, IConfiguration configuration, ApiDbContext dbContext, IFirebaseRepository firebaseRepository)
        {
            _context = context;
            _redisClient = redisClient;
            _validationService = validationService;
            _configuration = configuration;
            _dbContext = dbContext;
            _firebaseRepository = firebaseRepository;

            Header = new WebHeaderCollection();
            IsRouting = new IdentityServerEndpointRouting();
        }

        public async Task<UserLogoutResult> UserLogout(UserLogoutRequest request)
        {
            try
            {
                var key = await _redisClient.GetAsync<List<TokenBlacklistDto>>($"token-blacklist:{request.Context.ChannelTypeId}");

                if (key == null || !key.Any())
                {
                    await _redisClient.AddAsync($"token-blacklist:{request.Context.ChannelTypeId}", new List<TokenBlacklistDto> { new()
                    {
                        Token = _context.Identity.Token,
                        CreatedAt = DateTime.Now
                    }});
                }
                else
                {
                    key.Add(new TokenBlacklistDto
                    {
                        Token = _context.Identity.Token,
                        CreatedAt = DateTime.Now
                    });

                    await _redisClient.AddAsync($"token-blacklist:{request.Context.ChannelTypeId}", key);
                }

                if (request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.Mobile)
                {
                    var existingUser = await _dbContext.CustomerUser
                        .Include(i => i.CustomerUserDevices)
                        .Where(s => s.Email.Equals(request.Context.Identity.Email))
                        .FirstOrDefaultAsync();

                    var existingDevice = existingUser?.CustomerUserDevices
                        .FirstOrDefault(s => s.DeviceId == request.DeviceId);

                    if (existingDevice != null)
                    {
                        existingDevice.IsActive = false;
                        existingDevice.UpdatedAt = DateTime.Now;
                        _dbContext.CustomerUserDevice.Update(existingDevice);
                        await _dbContext.SaveChangesAsync();
                    }
                }

                return new UserLogoutResult
                {
                    Status = UserStatus.Successful,
                    Message = ServiceResources.SUCCESS,
                };
            }
            catch (Exception exc)
            {
                Logger.Error(exc, $@"Exception:{exc.Message}:{DateTime.Now}");

                return new UserLogoutResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<UserRegisterResult> UserRegister(UserRegisterRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UserRegisterValidator), request);

            if (!validationResult.IsValid)
                return new UserRegisterResult
                {
                    Status = UserStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            await using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var isExistingUser = await _dbContext.CustomerUser
                    .Where(r => r.Email.Equals(request.Email)).AsNoTracking()
                    .AnyAsync();

                if (isExistingUser)
                    return new UserRegisterResult
                    {
                        Status = UserStatus.AlreadyExist,
                        Message = ServiceResources.USER_ALREADY_REGISTERED,
                    };

                var customerUser = new Entity.Entities.CustomerUser.CustomerUser()
                {
                    Email = request.Email,
                    Name = request.Name,
                    Surname = request.Surname,
                    IsActive = true,
                    IsDeleted = false,
                    NationalityId = request.NationalityId,
                    CreatedBy = request.Email,
                    CreatedAt = DateTime.Now
                };

                await _dbContext.CustomerUser.AddAsync(customerUser);

                await _dbContext.SaveChangesAsync();

                #region IS

                var iSResponse = await IdentityServerPublicRegister(request.Email, request.Password, null);

                if (iSResponse.Status != "SUCCESS")
                {
                    await transaction.RollbackAsync();

                    return new UserRegisterResult
                    {
                        Status = UserStatus.ExternalServiceError,
                        Message = iSResponse.Message,
                        ValidationMessages = iSResponse.ValidationMessages
                    };
                }

                #endregion

                await transaction.CommitAsync();

                return new UserRegisterResult
                {
                    Status = UserStatus.Created,
                    Message = ServiceResources.RESOURCE_CREATED,
                    Id = customerUser.Id
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");
                await transaction.RollbackAsync();

                return new UserRegisterResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetUserProfileResult> GetUserProfile(GetUserProfileRequest request)
        {
            try
            {
                var existingUser = await (
                        from cu in _dbContext.CustomerUser
                        where cu.Email.Equals(request.Context.Identity.Email)
                        join co in _dbContext.Country on cu.NationalityId equals co.Id into countryGroup
                        from c in countryGroup.DefaultIfEmpty()
                        select new
                        {
                            CustomerUser = cu,
                            Country = c
                        })
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingUser == null)
                    return new GetUserProfileResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var userDto = new CustomerUserDto()
                {
                    Name = existingUser.CustomerUser.Name,
                    Surname = existingUser.CustomerUser.Surname,
                    Email = existingUser.CustomerUser.Email,
                    Nationality = existingUser.Country != null ? new LookupValue()
                    {
                        Id = existingUser.Country.Id.ToString(),
                        DisplayValue = GetCountryName(existingUser.Country, _context.LanguageId)
                    } : null,
                    Settings = new CustomerUserSettingsDto()
                    {
                        IsAllowedForEmail = existingUser.CustomerUser.IsAllowedForEmail,
                        IsAllowedForNotification = existingUser.CustomerUser.IsAllowedForNotification,
                        IsAllowedForSms = existingUser.CustomerUser.IsAllowedForSms,
                    }
                };

                return new GetUserProfileResult()
                {
                    Status = UserStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    Data = userDto
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetUserProfileResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<UpdateUserProfileResult> UpdateUserProfile(UpdateUserProfileRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateUserProfileValidator), request);

            if (!validationResult.IsValid)
                return new UpdateUserProfileResult
                {
                    Status = UserStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var existingUser = await _dbContext.CustomerUser
                    .Where(r => r.Email.Equals(request.Context.Identity.Email)).AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingUser == null)
                    return new UpdateUserProfileResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var customerDevice = await _dbContext.CustomerUserDevice
                       .Where(r => r.CustomerUserId == existingUser.Id && r.IsActive && !r.IsDeleted).OrderByDescending(s => s.Id)
                       .FirstOrDefaultAsync();

                if (customerDevice == null)
                    return new UpdateUserProfileResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var country = await _dbContext.Country
                    .Where(s => s.Id == customerDevice.LocationId)
                    .FirstOrDefaultAsync();

                var isNationalityUpdated = existingUser.NationalityId != request.NationalityId;

                if (isNationalityUpdated)
                {
                    await UnsubscribeFromTopics(country, existingUser, customerDevice.RegistryToken);
                }

                existingUser.Name = request.Name;
                existingUser.Surname = request.Surname;
                existingUser.NationalityId = request.NationalityId;
                existingUser.UpdatedAt = DateTime.Now;
                existingUser.UpdatedBy = request.Context.Identity.Email;

                if (isNationalityUpdated)
                {
                    await SubscribeToTopics(country, existingUser, customerDevice.RegistryToken, customerDevice.CustomerUserId);
                }

                _dbContext.CustomerUser.Update(existingUser);

                await _dbContext.SaveChangesAsync();

                return new UpdateUserProfileResult
                {
                    Status = UserStatus.Successful,
                    Message = ServiceResources.RESOURCE_UPDATED,
                    Id = existingUser.Id
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new UpdateUserProfileResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<AddUpdateUserDeviceEntryResult> AddUpdateUserDeviceEntry(AddUpdateUserDeviceEntryRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateUserDeviceEntryValidator), request);

            if (!validationResult.IsValid)
                return new AddUpdateUserDeviceEntryResult
                {
                    Status = UserStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var existingUser = await _dbContext.CustomerUser
                    .Where(r => r.Email.Equals(request.Context.Identity.Email)).AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingUser == null)
                    return new AddUpdateUserDeviceEntryResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var locationCountry = await _dbContext.Country.Where(s => s.ISO2 == request.Location.ToUpper()).FirstOrDefaultAsync();

                if (locationCountry == null)
                    return new AddUpdateUserDeviceEntryResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.COUNTRY_NOT_FOUND,
                    };

                var customerDevice = await _dbContext.CustomerUserDevice
                    .Where(r => r.CustomerUserId == existingUser.Id && r.DeviceId == request.DeviceId)
                    .FirstOrDefaultAsync();

                if (customerDevice == null)
                {
                    await CreateNewDeviceAsync(existingUser, request, locationCountry);
                }
                else
                {
                    await UpdateExistingDeviceAsync(customerDevice, existingUser, request, locationCountry);
                }

                return new AddUpdateUserDeviceEntryResult
                {
                    Status = UserStatus.Successful,
                    Message = ServiceResources.SUCCESS,
                    Id = existingUser.Id
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new AddUpdateUserDeviceEntryResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetUserPushNotificationsResult> GetUserPushNotifications(GetUserPushNotificationsRequest request)
        {
            try
            {
                var existingUser = await _dbContext.CustomerUser
                    .Where(r => r.Email.Equals(request.Context.Identity.Email)).AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingUser == null)
                    return new GetUserPushNotificationsResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var userHistory = await _dbContext.PushNotification
                    .Include(i => i.PushNotificationHistories)
                    .Where(s => s.PushNotificationHistories.Any(r => r.CustomerUserId == existingUser.Id) && s.IsActive && !s.IsDeleted && s.StatusId == (int)Enums.Enums.NotificationStatusType.Sent).ToListAsync();

                var paginationResult = PagedResultsFactory.CreatePagedResult(
                    userHistory.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                    request.Pagination.OrderBy, request.Pagination.Ascending);

                return new GetUserPushNotificationsResult
                {
                    Status = UserStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    PushNotifications = paginationResult.Results.Select(s => new PushNotificationDto
                    {
                        Id = s.Id,
                        Title = s.Title,
                        Status = GetLookupValue((int)LookupType.PushNotificationStatus,s.StatusId),
                        LastSendAt = s.SendAt?.ToString("dd/MM/yyyy HH:mm"),
                        CreatedAt = s.CreatedAt?.ToString("dd/MM/yyyy HH:mm"),
                        Histories = s.PushNotificationHistories.Where(s => s.CustomerUserId == existingUser.Id).OrderBy(s => s.Id).Select(h => new PushNotificationHistoryDto()
                        {
                            Id = h.Id,
                            LocationId = h.LocationId,
                            NationalityId = h.NationalityId,
                            Text = h.Text,
                            CustomerUserId = h.CustomerUserId,
                            SendAt = h.CreatedAt?.ToString("dd/MM/yyyy HH:mm")
                        }).ToList()
                    }).ToList(),
                    TotalNumberOfPages = paginationResult.TotalNumberOfPages,
                    TotalNumberOfRecords = paginationResult.TotalNumberOfRecords
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetUserPushNotificationsResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<UpdateUserSettingsResult> UpdateUserSettings(UpdateUserSettingsRequest request)
        {
            try
            {
                var existingUser = await _dbContext.CustomerUser
                    .Where(r => r.Email.Equals(request.Context.Identity.Email)).AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingUser == null)
                    return new UpdateUserSettingsResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var customerDevice = await _dbContext.CustomerUserDevice
                     .Where(r => r.CustomerUserId == existingUser.Id && r.IsActive && !r.IsDeleted).OrderByDescending(s => s.Id)
                     .FirstOrDefaultAsync();

                if (customerDevice == null)
                    return new UpdateUserSettingsResult
                    {
                        Status = UserStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var country = await _dbContext.Country
                    .Where(s => s.Id == customerDevice.LocationId)
                    .FirstOrDefaultAsync();

                var customerTopics = await _dbContext.CustomerUserTopic
                .Where(s => s.IsActive && !s.IsDeleted && s.CustomerUserId == existingUser.Id)
                .ToListAsync();

                if (!request.IsAllowedSendNotification)
                {
                    await UnsubscribeFromTopics(country, existingUser, customerDevice.RegistryToken);

                    foreach (var topic in customerTopics)
                    {
                        topic.IsDeleted = true;
                        topic.IsActive = false;
                        topic.DeletedAt = DateTime.Now;
                        _dbContext.CustomerUserTopic.Update(topic);
                    }

                    await _dbContext.SaveChangesAsync();
                }
                else
                {
                    if(customerTopics.Count == 0) 
                    {
                        await SubscribeToTopics(country, existingUser, customerDevice.RegistryToken, customerDevice.CustomerUserId);
                    }
                }

                existingUser.IsAllowedForEmail = request.IsAllowedSendEmail;
                existingUser.IsAllowedForNotification = request.IsAllowedSendNotification;
                existingUser.IsAllowedForSms = request.IsAllowedSendSms;
                existingUser.UpdatedAt = DateTime.Now;
                existingUser.UpdatedBy = request.Context.Identity.Email;

                _dbContext.CustomerUser.Update(existingUser);

                await _dbContext.SaveChangesAsync();

                return new UpdateUserSettingsResult
                {
                    Status = UserStatus.Successful,
                    Message = ServiceResources.RESOURCE_UPDATED,
                    Id = existingUser.Id
                };

            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new UpdateUserSettingsResult
                {
                    Status = UserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        #region Private Methods

        private static LookupValue GetLookupValue(int enumType, int? value)
        {
            return EnumExtensions.GetEnumAsDictionary(LookupTypeFactory.GetInstance(enumType))
                .Select(x => new LookupValue { Id = x.Key.ToString(), DisplayValue = x.Value })
                .FirstOrDefault(p => p.Id == value.ToString());
        }

        // Helper methods
        private async Task CreateNewDeviceAsync(CustomerUser existingUser, AddUpdateUserDeviceEntryRequest request, Entity.Entities.Country.Country locationCountry)
        {
            var newDevice = new CustomerUserDevice()
            {
                CustomerUserId = existingUser.Id,
                DeviceId = request.DeviceId,
                LocationId = locationCountry.Id,
                RegistryToken = request.RegistryToken,
                IsActive = true,
                IsDeleted = false,
                CreatedBy = request.Context.Identity.Email,
                CreatedAt = DateTime.Now
            };

            await _dbContext.CustomerUserDevice.AddAsync(newDevice);
            await _dbContext.SaveChangesAsync();

            await SubscribeToTopics(locationCountry, existingUser, request.RegistryToken,existingUser.Id);
        }

        private async Task UpdateExistingDeviceAsync(CustomerUserDevice customerDevice, CustomerUser existingUser, AddUpdateUserDeviceEntryRequest request, Entity.Entities.Country.Country locationCountry)
        {
            if (customerDevice.LocationId != locationCountry.Id)
            {
                var existingLocation = await _dbContext.Country
                    .Where(s => s.Id == customerDevice.LocationId)
                    .FirstOrDefaultAsync();

                await UnsubscribeFromTopics(existingLocation, existingUser, request.RegistryToken);
            }

            customerDevice.LocationId = locationCountry.Id;
            customerDevice.RegistryToken = request.RegistryToken;

            await SubscribeToTopics(locationCountry, existingUser, request.RegistryToken, customerDevice.CustomerUserId);

            _dbContext.CustomerUserDevice.Update(customerDevice);
            await _dbContext.SaveChangesAsync();
        }

        private async Task SubscribeToTopics(Entity.Entities.Country.Country country, CustomerUser user, string registryToken, int userId)
        {
            var topics = new List<string>()
            {
                $"{country.ISO2}_{user.NationalityId}",
                $"{country.ISO2}_all",
                $"{user.NationalityId}_all",
                $"all",
            };

            foreach (var topic in topics)
            {
                await _firebaseRepository.SubscribeToTopic(new SubscribeToTopicRequest()
                {
                    RegistryToken = registryToken,
                    Topic = topic
                });
            }

            var customerTopics = await _dbContext.CustomerUserTopic
                .Where(s => s.IsActive && !s.IsDeleted && s.CustomerUserId == userId)
                .ToListAsync();

            foreach (var topic in topics.Where(topic => customerTopics.All(s => s.Topic != topic)))
            {
                await _dbContext.CustomerUserTopic.AddAsync(new CustomerUserTopic()
                {
                    IsActive = true,
                    IsDeleted = false,
                    CustomerUserId = userId,
                    Topic = topic,
                    CreatedAt = DateTime.Now,
                });
            }

            var topicsToRemove = customerTopics.Where(ct => !topics.Contains(ct.Topic)).ToList();
            foreach (var item in topicsToRemove)
            {
                item.IsDeleted = true;
                item.IsActive = false;
                item.DeletedAt = DateTime.Now;
                _dbContext.CustomerUserTopic.Update(item);
            }

            await _dbContext.SaveChangesAsync();
        }

        private async Task UnsubscribeFromTopics(Entity.Entities.Country.Country country, CustomerUser user, string registryToken)
        {
            var topics = new List<string>()
                {
                    $"{country.ISO2}_{user.NationalityId}",
                    $"{country.ISO2}_all",
                    $"{user.NationalityId}_all",
                    $"all"
                };

            foreach (var topic in topics)
            {
                await _firebaseRepository.UnSubscribeToTopic(new UnSubscribeToTopicRequest()
                {
                    RegistryToken = registryToken,
                    Topic = topic
                });
            }
        }

        private async Task<IdentityServerPublicRegisterResult> IdentityServerPublicRegister(string email, string password, List<RoleAssignDto> roles)
        {
            var baseAddress = _configuration.GetValue<string>("GatewayMobileIdentityServer:BaseAddress");
            var applicationResourceId = _configuration.GetValue<string>("GatewayMobileIdentityServer:ApplicationResourceId");

            Header.Add("Accept-Language", _context.LanguageId == (int)Enums.Enums.Language.Turkish ? "tr-TR" : "en-US");

            var registerRequest = new IdentityServerExternalRegisterRequest()
            {
                Password = password,
                Email = email,
                ApplicationResourceId = applicationResourceId,
            };

            return await RestHttpClient.Create().Post<IdentityServerPublicRegisterResult>
                (baseAddress + IsRouting.PublicRegister, Header, registerRequest).ConfigureAwait(false);
        }

        private static string GetCountryName(Entity.Entities.Country.Country country, int languageId)
        {
            return languageId switch
            {
                1 => country.NameTr,
                3 => country.NameAr,
                _ => country.Name
            };
        }

        #endregion


    }
}
