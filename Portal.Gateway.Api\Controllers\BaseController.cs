﻿using AutoMapper;
using Gateway.Extensions;
using Gateway.Logger.Core.Models;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Portal.Gateway.Api.ActionFilters;
using Portal.Gateway.ApiModel;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Extensions;
using Portal.Gateway.Contracts.Settings.AppSettings;
using Portal.Gateway.Resources;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Portal.Gateway.Api.Controllers
{
    [TypeFilter(typeof(Authentication))]
    public class BaseController<TController> : Controller
    {
        private static readonly Serilog.ILogger _logger = Serilog.Log.ForContext<TController>();

        protected BaseController(IOptions<AppSettings> appSettings, IWebHostEnvironment hostingEnvironment, ILogger<TController> logger, IMapper mapper)
        {
            HostingEnvironment = hostingEnvironment;
            Logger = logger;
            AppSettings = appSettings.Value;
            Mapper = mapper;
        }

        protected IWebHostEnvironment HostingEnvironment { get; }

        protected ILogger<TController> Logger { get; }

        protected AppSettings AppSettings { get; }

        protected IMapper Mapper;

        [FromHeader(Name = "correlationId")]
        private Guid CorrelationId => Guid.TryParse(Request.Headers["correlationId"].ToString(), out var correlationId) ? correlationId : Guid.NewGuid();

        [FromHeader(Name = "languageId")]
        public int LanguageId => Enum.TryParse(Request.Headers["languageId"].ToString(), out Language language) ? (int)language : (int)Language.Turkish;

        [FromHeader(Name = "corporateId")]
        private string CorporateId => Request.Headers["corporateId"].ToString();

        [FromHeader(Name = "UserId")]
        public int UserAuditId => Convert.ToInt32(Request.Headers["UserId"]);

        public static Task ValidateRequestAsync(IRequest request)
        {
            if (request == null)
                throw new NullRequestPortalException();

            var validationResults = new List<ValidationResult>();

            if (!Validator.TryValidateObject(request, new System.ComponentModel.DataAnnotations.ValidationContext(request), validationResults, true))
                throw new ValidationFailedPortalException(string.Join(" | ", validationResults.Select(q => q.ErrorMessage)));

            return Task.CompletedTask;
        }

        public async Task<ApiResponse<TDestination>> Process<TDestination, TSource>(IRequest request, Func<Task<TSource>> func) where TDestination : class
        {
            _logger.Debug($"(Process) Request: {JsonConvert.SerializeObject(request)}");

            request?.SetCorrelationId(CorrelationId);
            request?.SetLanguageId(LanguageId);
            request?.SetCorporateId(CorporateId);
            request?.SetUserAuditId(UserAuditId);

            var response = new ApiResponse<TDestination>();

            try
            {
                await ValidateRequestAsync(request).ConfigureAwait(false);

                var result = await func().ConfigureAwait(false);

                response.Data = Mapper.Map<TDestination>(result);
                response.IsSuccess = true;
            }
            catch (PortalException exception)
            {
                response.Code = exception.ErrorCode;
                response.Message = exception.ErrorMessage;
                response.IsSuccess = false;
            }
            catch (Exception exception)
            {
                _logger.ForContext(LogConstants.Request, request?.ToJson(), destructureObjects: true).Error(exception, "Api Controller Error");

                response.Code = exception.GetType().Name;
                response.Message = $"{nameof(SiteResources.ServerError).ToSiteResourcesValue(LanguageId)}";
                response.IsSuccess = false;
            }

            return response;
        }

        public async Task<SelectListApiResponse> CreateEnumSelectList(string enumType)
        {
            var languageId = Enum.TryParse(Request.Headers["languageId"].ToString(), out Language language) ? (int)language : (int)Language.Turkish;

            var type = System.AppDomain.CurrentDomain.GetAssemblies()
                        .SelectMany(x => x.GetTypes())
                        .First(x => x.Name == enumType);

            return new SelectListApiResponse()
            {
                List = Contracts.Extensions.AttributeExtension
                            .LocalizedResourceValue
                            .Value(type, languageId)
                            .Select(x => new SelectListApiResponse.SelectListItem { Value = x.Key, Name = x.Value.ToTitleCase() })
            };
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            base.OnActionExecuting(context);

            string cultureName = "en-US";

            if (Request.Headers.TryGetValue("languageId", out var languageIdStr) && Enum.TryParse(languageIdStr.ToString(), out Language language))
            {
                cultureName = language switch
                {
                    Language.English => "en-US",
                    _ => "tr-TR"
                };
            }

            var cultureInfo = new CultureInfo(cultureName);
            Thread.CurrentThread.CurrentCulture = cultureInfo;
            Thread.CurrentThread.CurrentUICulture = cultureInfo;
        }
    }
}