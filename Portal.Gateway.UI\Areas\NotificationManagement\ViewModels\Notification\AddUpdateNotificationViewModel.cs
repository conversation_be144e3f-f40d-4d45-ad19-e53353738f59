﻿using FluentValidation;
using Portal.Gateway.Resources;
using System.Collections.Generic;

namespace Portal.Gateway.UI.Areas.NotificationManagement.ViewModels.Notification
{
    public class AddUpdateNotificationViewModel
    {
        public string EncryptedId { get; set; }
        public string NotificationTitle { get; set; }
        public List<NotificationTranslationViewModel> Translations { get; set; }
    }

    public class AddUpdateNotificationViewModelValidator : AbstractValidator<AddUpdateNotificationViewModel>
    {
        public AddUpdateNotificationViewModelValidator()
        {
            RuleFor(x => x.NotificationTitle)
                .NotEmpty().NotNull().WithMessage(SiteResources.RequiredField);
        }
    }
}
