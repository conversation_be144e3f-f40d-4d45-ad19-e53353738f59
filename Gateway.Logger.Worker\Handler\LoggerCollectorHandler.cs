﻿using Gateway.Logger.Worker.Provider;
using System.Threading.Tasks;
using Gateway.Logger.Worker.Handler.Dto;
using Gateway.Logger.Worker.Provider.Dto;

namespace Gateway.Logger.Worker.Handler
{
    public class LoggerCollectorHandler : ILoggerCollectorHandler
    {
        private readonly ILoggerProvider _loggerProvider;

        #region ctor

        public LoggerCollectorHandler(ILoggerProvider loggerProvider)
        {
            _loggerProvider = loggerProvider;
        }

        #endregion

        #region Public Methods

        public async Task CollectLogger(LoggerCollectorRequest request)
        {
            var loggerRequest = new LogRequest
            {
               Timestamp = request.Timestamp,
               Level = request.Level,
               RenderedMessage = request.RenderedMessage,
               Exception = request.Exception,
               Properties =
               {
                   SourceContext = request.Properties.SourceContext,
                   ActionId = request.Properties.ActionId,
                   ActionName = request.Properties.ActionName,
                   Request = request.Properties.Request,
                   RequestId = request.Properties.RequestId,
                   SpanId = request.Properties.SpanId,
                   TraceId = request.Properties.TraceId,
                   ParentId = request.Properties.ParentId,
                   ConnectionId = request.Properties.ConnectionId,
                   Instance = request.Properties.Instance,
                   DateTime = request.Properties.DateTime,
                   ApplicationName = request.Properties.ApplicationName,
                   EnvironmentName = request.Properties.EnvironmentName,
                   ClientIp = request.Properties.ClientIp,
                   UserAgent = request.Properties.UserAgent
               }
            };

            await _loggerProvider.SendLog(loggerRequest);
        }

        #endregion
    }
}