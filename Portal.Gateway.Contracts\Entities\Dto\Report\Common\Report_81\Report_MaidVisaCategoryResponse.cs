﻿using System.Collections.Generic;
using System;

namespace Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_81
{
    public class Report_MaidVisaCategoryResponse
    {
        public IEnumerable<Branch> Branches { get; set; }
        public class Branch
        {
            public string BranchName { get; set; }
            public IEnumerable<Application> Applications { get; set; }

            public class Application
            {
                public string ReferenceNumber { get; set; }

                public string PassportNumber { get; set; }

                public string ApplicantName { get; set; }

                public string ApplicantSurname { get; set; }

                public string ReimbursementSponsorDetail { get; set; }

                public string Nationality { get; set; }

                public int ApplicationStatusId { get; set; }

                public DateTimeOffset ApplicationDate { get; set; }
            }
        }
    }
}
