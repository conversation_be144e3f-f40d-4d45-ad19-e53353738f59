﻿using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Resources;
using System.ComponentModel;

namespace Portal.Gateway.Contracts.Entities.Enums
{
    public enum ClaimLossEntryLogStatusEnum
    {
        Pending = 1,
        Running = 2,
        Completed = 3,
        DocumentNotFound = 8,
        Failed = 9,
    }

    public enum ServiceResultType
    {
        Error = 0,
        Success = 1
    }

    public enum ChannelType
    {
        [LocalizedDescription(nameof(EnumResources.B2B))]
        B2B = 2,
        [LocalizedDescription(nameof(EnumResources.Mobile))]
        Mobile = 3,
        [LocalizedDescription(nameof(EnumResources.B2CWebSite))]
        WebSite = 4
    }

    public enum EsimType
    {
        [LocalizedDescription(nameof(EnumResources.eSim8))]
        EightGB = 1,
        [LocalizedDescription(nameof(EnumResources.eSim20))]
        TwentyGB = 2,
        [LocalizedDescription(nameof(EnumResources.eSim50))]
        FiftyGB = 3
    }

    public enum VisaCategoryTypeEnum
    {
        [LocalizedDescription(nameof(EnumResources.Unspecified))]
        Unspecified = 0,
        [LocalizedDescription(nameof(EnumResources.Touristic))]
        Touristic = 1,
        [LocalizedDescription(nameof(EnumResources.Student))]
        Student = 6,
        [LocalizedDescription(nameof(EnumResources.WorkPermit))]
        WorkPermit = 7,
        [LocalizedDescription(nameof(EnumResources.FamilyUnion))]
        FamilyUnion = 9,
        [LocalizedDescription(nameof(EnumResources.NonApplicationInsurance))]
        NonApplicationInsurance = 14,
        [LocalizedDescription(nameof(EnumResources.EntryBanned))]
        EntryBanned = 15,
        [Description("Tömer")]
        Tomer = 26,
        [LocalizedDescription(nameof(EnumResources.ERASMUS))]
        ERASMUS = 27,
        [LocalizedDescription(nameof(EnumResources.IAESTE))]
        IAESTE = 28,
        [LocalizedDescription(nameof(EnumResources.AIESEC))]
        AIESEC = 29,
        [LocalizedDescription(nameof(EnumResources.TouristicResidencePermit))]
        TouristicResidencePermit = 40,
        [LocalizedDescription(nameof(EnumResources.VisitSpecialResidancePermit))]
        VisitSpecialResidancePermit = 41
    }

    public enum RejectedApplicationFeePolicyType
    {
        OneYear = 1,
        SixMonth = 2,
        ThreeMonth = 3
    }

    public enum ServiceNotificationType
    {
	    StatusChanged,
	    CancelRequested,
	    RefundRequested,
	    NewPreApplication,
	    UpdatePreApplication,
	    ForgetPassword,
	    InsurancePolicy,
	    AddAgencyUser,
	    AgencyFiles,
	    Postpone,
	    ScanSycle,
	    Approve,
	    Resend,
	    Rejection,
	    ConfirmationCode,
	    ReferenceNumber,
        RejectionApproval,
        Esim,
        LdapSyncResult,
        Esim2,
        ReleatedInsuranceFirst,
        ReleatedInsurance,
        RejectionWithPolicyRefund
    }

    public enum PaymentType : byte
    {
        Pos = 1,
        Online = 2,
        Cash = 3
    }

    public enum PhotoBoothFees
    {
        Photo = 18,
        PhotoVip = 46,
        MbsPhoto = 100,
        PlatinumPhoto = 109,
        PhotoIQD = 130,
        PhotoUsd = 131,
        GatewayStaffPhoto =195,
        PhotoDzd = 196,
        PhotoTmt = 204,
        PhotoKwd = 205,
    }

    public enum ApplicationSaleType : byte
    {
        Esim_8 = 1,
        Esim_20 = 2,
        Esim_50 = 3
    }

    public enum ApplicationSaleStatus : byte
    {
        Available = 1,
        Reserved = 2,
        Used = 3
    }

    public enum AdditionalNotificationType
    {
        Approve = 1,
        Rejection = 2,
        WorkPermit = 3,
        ConfirmationCode = 4,
        RejectionApproval = 5,
        Insurance = 6,
        Esim = 7,
        ReleatedInsurance = 8
    }

    public enum NotificationRouteType
    {
        ScanSycle = 1,
        WorkPermit = 2,
        Resend = 3,
        ConfirmationCode = 4,
        RejectionApproval = 5,
        Esim = 6,
        ReleatedInsurance = 7
    }

    public enum ApprovedNotificationContentStatusUpdateType
    {
        Multiple = 1,
        All = 2
    }

    public enum ApprovedNotificationContentLanguage
    {
        Undefined = 0,
        Turkish = 1,
        English = 2,
        Arabic = 3,
        Kurdish = 4,
        French = 5,
        Russian = 6
    }

    public enum ApplicationNotificationType
    {
		Sms = 1,
        Mail = 2
    }

    public enum RejectionApprovalNotificationType
    {
        Sms = 1,
        Mail = 2,
        Supervisor = 3
    }


    public enum Language
    {
        [LocalizedDescription(nameof(EnumResources.Turkish))]
        Turkish = 1,
        [LocalizedDescription(nameof(EnumResources.English))]
        English = 2
    }
    public enum BranchTranslationLanguage
    {
        [LocalizedDescription(nameof(EnumResources.Turkish))]
        Turkish = 1,
        [LocalizedDescription(nameof(EnumResources.English))]
        English = 2,
        [LocalizedDescription(nameof(EnumResources.Arabic))]
        Arabic = 3
    }

    public enum PushNotificationTranslationLanguage
    {
        [LocalizedDescription(nameof(EnumResources.Turkish))]
        Turkish = 1,
        [LocalizedDescription(nameof(EnumResources.English))]
        English = 2,
        [LocalizedDescription(nameof(EnumResources.Arabic))]
        Arabic = 3,
        [LocalizedDescription(nameof(EnumResources.Turkmen))]
        Turkmen = 4,
        [LocalizedDescription(nameof(EnumResources.Russian))]
        Russian = 5,
        [LocalizedDescription(nameof(EnumResources.French))]
        French = 6,
    }

    public enum InquiryTranslationEnum
    {
        [LocalizedDescription(nameof(EnumResources.Turkish))]
        Turkish = 1,
        [LocalizedDescription(nameof(EnumResources.English))]
        English = 2,
    }

    public enum BranchDataTranslationLanguage
    {
        Arabic = 3,
        Turkmen = 4,
        Russian = 5,
        French = 6
    }

    public enum ICRLanguage
    {
        [LocalizedDescription(nameof(EnumResources.Turkish))]
        Turkish = 1,
        [LocalizedDescription(nameof(EnumResources.English))]
        English = 2,
        [LocalizedDescription(nameof(EnumResources.Arabic))]
        Arabic = 3,
        [LocalizedDescription(nameof(EnumResources.Turkmen))]
        Turkmen = 4,
        [LocalizedDescription(nameof(EnumResources.Russian))]
        Russian = 5,
        [LocalizedDescription(nameof(EnumResources.French))]
        French = 6,
        [LocalizedDescription(nameof(EnumResources.EnglishAndArabic))]
        EnglishAndArabic = 7,
	}

    public enum PolicyLanguage
    {
        [LocalizedDescription(nameof(EnumResources.Turkish))]
        Turkish = 10,
        [LocalizedDescription(nameof(EnumResources.English))]
        English = 11,
        [LocalizedDescription(nameof(EnumResources.Arabic))]
        Arabic = 12,
		[LocalizedDescription(nameof(EnumResources.TurkishLongPolicyLanguage))]
		TurkishLongPolicyLanguage = 20,
		[LocalizedDescription(nameof(EnumResources.EnglishLongPolicyLanguage))]
		EnglishLongPolicyLanguage = 21,
		[LocalizedDescription(nameof(EnumResources.ArabicLongPolicyLanguage))]
		ArabicLongPolicyLanguage = 22
	}
	public enum TsPolicyLanguage
	{
		[LocalizedDescription(nameof(EnumResources.Turkish))]
		Turkish = 1
	}
	public enum EntryFormLanguage
    {
        English = 1,
        French = 2,
        Arabic = 3,
        Turkish = 4,
        Russian = 5,
        TkmEnglish = 6,
        TkmTurkish = 7,
        TkmRussian = 8,
        Turkmen = 9
    }

    public enum ExplicitConsentLanguage
    {
        English = 1,
        French = 2,
        Arabic = 3,
        Turkish = 4,
        Russian = 5,
        Turkmen = 6,
        IraqEnglish = 7,
        IraqArabic = 8
    }

	public enum PDFDocumentTypes
	{
		[LocalizedDescription(nameof(EnumResources.Turkish))]
		Turkish = 1,
		[LocalizedDescription(nameof(EnumResources.English))]
		English = 2,
		[LocalizedDescription(nameof(EnumResources.Arabic))]
		Arabic = 3,
		[LocalizedDescription(nameof(EnumResources.Turkmen))]
		Turkmen = 4,
		[LocalizedDescription(nameof(EnumResources.Russian))]
		Russian = 5,
		[LocalizedDescription(nameof(EnumResources.French))]
		French = 6,
		[LocalizedDescription(nameof(EnumResources.EnglishTaxed))]
		EnglishTaxed = 7,
		[LocalizedDescription(nameof(EnumResources.TurkmenTaxed))]
		TurkmenTaxed = 8,
		[LocalizedDescription(nameof(EnumResources.RussianTaxed))]
		RussianTaxed = 9,
		[LocalizedDescription(nameof(EnumResources.FrenchTaxed))]
		FrenchTaxed = 10,
		[LocalizedDescription(nameof(EnumResources.ArabicTaxed))]
		ArabicTaxed = 11,
		[LocalizedDescription(nameof(EnumResources.EnglishAllItemsTaxed))]
		EnglishAllItemsTaxed = 12,
		[LocalizedDescription(nameof(EnumResources.TurkmenAllItemsTaxed))]
		TurkmenAllItemsTaxed = 13,
		[LocalizedDescription(nameof(EnumResources.RussianAllItemsTaxed))]
		RussianAllItemsTaxed = 14,
		[LocalizedDescription(nameof(EnumResources.FrenchAllItemsTaxed))]
		FrenchAllItemsTaxed = 15,
		[LocalizedDescription(nameof(EnumResources.ArabicAllItemsTaxed))]
		ArabicAllItemsTaxed = 16,
		[LocalizedDescription(nameof(EnumResources.English))]
        TkmEnglish = 17,
		[LocalizedDescription(nameof(EnumResources.Turkish))]
        TkmTurkish = 18,
		[LocalizedDescription(nameof(EnumResources.Russian))]
        TkmRussian = 19
    }

	public enum Culture
    {
        [Description("tr-TR")]
        TR = 1,
        [Description("en-US")]
        EN = 2
    }

    public enum RoleType
    {
        Supervisor = 4,        
        BranchManager = 7,
        CountryManager = 8,
        CallCenterManager = 16,
        CallCenterLead = 17
    }

    public enum TokenState
    {
        [LocalizedDescription(nameof(EnumResources.Recalled))]
        Recall = 0,
        [LocalizedDescription(nameof(EnumResources.Created))]
        Created = 1,
        [LocalizedDescription(nameof(EnumResources.InProgress))]
        InProgress = 2,
        [LocalizedDescription(nameof(EnumResources.Assigned))]
        Assign = 3,
        [LocalizedDescription(nameof(EnumResources.Postponed))]
        Postpone = 4,
        [LocalizedDescription(nameof(EnumResources.OnHold))]
        HoldOn = 5,
        [LocalizedDescription(nameof(EnumResources.Cancelled))]
        CancelledByApplicant = 6,
        [LocalizedDescription(nameof(EnumResources.NotCompleted))]
        NotCompleted = 7,
        [LocalizedDescription(nameof(EnumResources.Completed))]
        Completed = 8,
        [LocalizedDescription(nameof(EnumResources.ApplicationCancelled))]
        ApplicationCancelled = 9
    }

    public enum NotCompletedReason : byte
    {
        [LocalizedDescription(nameof(EnumResources.LackofBusinessDocuments))]
        LackOfBusinessDocuments = 1,
        [LocalizedDescription(nameof(EnumResources.NotUnderstandingTheDocuments))]
        NotUnderstandingTheDocuments,
        [LocalizedDescription(nameof(EnumResources.AddressDeclarationCard))]
        AddressDeclarationCard,
        [LocalizedDescription(nameof(EnumResources.LackOfIdentity))]
        LackOfIdentity,
        [LocalizedDescription(nameof(EnumResources.PassportDamage))]
        PassportDamage,
        [LocalizedDescription(nameof(EnumResources.InsufficientValidityOfPassportExpiryDate))]
        InsufficientValidityOfPassportExpiryDate,
        [LocalizedDescription(nameof(EnumResources.LackOfInvitationDueToEntryBan))]
        LackOfInvitationDueToEntryBan,
        [LocalizedDescription(nameof(EnumResources.FingerprintQuality))]
        FingerprintQuality,
        [LocalizedDescription(nameof(EnumResources.InvitationRequirementForApplication))]
        InvitationRequirementForApplication,
        [LocalizedDescription(nameof(EnumResources.HaveValidVisa))]
        HaveValidVisa,
        [LocalizedDescription(nameof(EnumResources.HighVisaFees))]
        HighVisaFees,
        [LocalizedDescription(nameof(EnumResources.LackOfPaperworkFromFamilyMember))]
        LackOfPaperworkFromFamilyMember,
        [LocalizedDescription(nameof(EnumResources.InsufficientAndIncompleteOrFakeBusinessDocuments))]
        InsufficientAndIncompleteOrFakeBusinessDocuments,
        [LocalizedDescription(nameof(EnumResources.PersonNotFoundInOffice))]
        PersonNotFoundInOffice,
        [LocalizedDescription(nameof(EnumResources.LackOfBusinessDocumentAuthentication))]
        LackOfBusinessDocumentAuthentication,
        [LocalizedDescription(nameof(EnumResources.MissingOfWorkPermitDurationInformation))]
        MissingOfWorkPermitDurationInformation,
        [LocalizedDescription(nameof(EnumResources.InsufficientCompanyDocumentsOrSalaryCertificate))]
        InsufficientCompanyDocumentsOrSalaryCertificate,
        [LocalizedDescription(nameof(EnumResources.IssuesWithBankAccount))]
        IssuesWithBankAccount,
        [LocalizedDescription(nameof(EnumResources.LackOfSponsorInformation))]
        LackOfSponsorInformation,
        [LocalizedDescription(nameof(EnumResources.MissingOfBirthCertificateDocumentForTheSponsor))]
        MissingOfBirthCertificateDocumentForTheSponsor,
        [LocalizedDescription(nameof(EnumResources.AirplaneTicketHotelReservation))]
        AirplaneTicketHotelReservation,
        [LocalizedDescription(nameof(EnumResources.PassportRelatedIssues))]
        PassportRelatedIssues,
        [LocalizedDescription(nameof(EnumResources.LackOfInformationOrInsufficientDurationOfResidencyCard))]
        LackOfInformationOrInsufficientDurationOfResidencyCard,
		[LocalizedDescription(nameof(EnumResources.Other))]
        Other = 24,
		[LocalizedDescription(nameof(EnumResources.PassportHolderWhoDoesNotNeedAVisa))]
		PassportHolderWhoDoesNotNeedAVisa,
	}

    public enum DayOfWeek
    {
        [LocalizedDescription(nameof(EnumResources.Monday))]
        Monday = 1,
        [LocalizedDescription(nameof(EnumResources.Tuesday))]
        Tuesday = 2,
        [LocalizedDescription(nameof(EnumResources.Wednesday))]
        Wednesday = 3,
        [LocalizedDescription(nameof(EnumResources.Thursday))]
        Thursday = 4,
        [LocalizedDescription(nameof(EnumResources.Friday))]
        Friday = 5,
        [LocalizedDescription(nameof(EnumResources.Saturday))]
        Saturday = 6,
        [LocalizedDescription(nameof(EnumResources.Sunday))]
        Sunday = 0,
    }

    public enum YesNo
    {
        [LocalizedDescription(nameof(EnumResources.Yes))]
        Yes = 1,
        [LocalizedDescription(nameof(EnumResources.No))]
        No = 0
    }

    public enum OnlineOffline
    {
        [LocalizedDescription(nameof(EnumResources.Online))]
        Online = 1,
        [LocalizedDescription(nameof(EnumResources.Offline))]
        Offline = 2
    }

    public enum ModuleType
    {
        [LocalizedDescription(nameof(EnumResources.Appointment))]
        Appointment = 1,
        [LocalizedDescription(nameof(EnumResources.Biometrics))]
        Biometrics = 2,
        [LocalizedDescription(nameof(EnumResources.DocumentManagement))]
        DocumentManagement = 3,
        [LocalizedDescription(nameof(EnumResources.QueueMatic))]
        QueueMatic = 4,
        [LocalizedDescription(nameof(EnumResources.Visa))]
        Visa = 5
    }

    public enum AgencyCategory
    {
        [LocalizedDescription(nameof(EnumResources.AgencyCategoryCorporate))]
        Corporate = 1,
        [LocalizedDescription(nameof(EnumResources.AgencyCategoryIndividual))]
        Individual = 2
    }

    public enum AgencyStatus
    {
        [LocalizedDescription(nameof(EnumResources.AgencyStatusActive))]
        Active = 1,
        [LocalizedDescription(nameof(EnumResources.AgencyStatusPassive))]
        Passive = 2,
        [LocalizedDescription(nameof(EnumResources.AgencyStatusOnHold))]
        OnHold = 3
    }

    public enum SlotType
    {
        [LocalizedDescription(nameof(EnumResources.Individual))]
        Individual = 1,
        [LocalizedDescription(nameof(EnumResources.Agency))]
        Agency = 2,
        //vfs = 3
        [LocalizedDescription(nameof(EnumResources.CallCenter))]
        CallCenter = 4, //b2c
        //[LocalizedDescription(nameof(EnumResources.B2B))]
        //B2B = 5,
        [LocalizedDescription(nameof(EnumResources.Mobile))]
        Mobile = 6
    }

    public enum CurrencyType
    {
        [LocalizedDescription(nameof(EnumResources.TRY))] //Turkish lira
        TRY = 1,
        [LocalizedDescription(nameof(EnumResources.USD))] //USD
        USD = 2,
        [LocalizedDescription(nameof(EnumResources.EUR))] //Euro
        EUR = 3,
        [LocalizedDescription(nameof(EnumResources.KWD))] //Kuwaiti Dinar
        KWD = 4,
        [LocalizedDescription(nameof(EnumResources.QAR))] //Qatari Rial
        QAR = 5,
        [LocalizedDescription(nameof(EnumResources.IQD))] //Iraqi Dinar
        IQD = 6,
        [LocalizedDescription(nameof(EnumResources.LYD))] //Libyan Dinar
        LYD = 7,
        [LocalizedDescription(nameof(EnumResources.PKR))] //Pakistani Rupee
        PKR = 8,
        [LocalizedDescription(nameof(EnumResources.DZD))] //Algerian Dinar
        DZD = 9,
        [LocalizedDescription(nameof(EnumResources.AED))] //United Arab Emirates Dirham - AED
        AED = 10,
        [LocalizedDescription(nameof(EnumResources.SAR))] //Saudi Riyal
        SAR = 11,
        [LocalizedDescription(nameof(EnumResources.INR))] //Hindistan Rupisi
        INR = 12,
        [LocalizedDescription(nameof(EnumResources.NPR))] //Nepal Rupisi
        NPR = 13,
        [LocalizedDescription(nameof(EnumResources.TMT))] //Türkmenistan Manatı
        TMT = 14,
        [LocalizedDescription(nameof(EnumResources.RUB))] //Rus Rublesi
        RUB = 15,
		[LocalizedDescription(nameof(EnumResources.AZN))] //Azerbaycan Manatı
		AZN = 16
	}

    public enum SapStatusType
    {
        Sent = 1,
        Canceled = 2
    }

    public enum ExtraFeeStaticType
    {
        Vip = 7,
        Sms = 9,
        Photo = 18,
        Photocopy = 19,
        UpstateCargo = 20,
        LocalCargo = 21,
        LocalCargoDiscounted = 22,
        UpstateCargoDiscounted = 23,
        PrintOut = 28
    }

    public enum ExtraFeeType
    {
        [LocalizedDescription(nameof(EnumResources.Single))]
        Single = 1,
        [LocalizedDescription(nameof(EnumResources.Multiple))]
        Multiple = 2
    }

    public enum AutomaticIhbOrderStatus:byte
    {
        Initial = 1,
        OperationStarted = 2,
        OperationCompleted = 3,
        FailOnCreatingIhb = 4,
        FailOnAddingDocument = 5,
        FailOnAddingFile = 6,
        FailOnScanSycle = 7,
        Exception = 8
    }

    public enum InsuranceCancelOrderStatus : byte
    {
        Initial = 1,
        OperationStarted = 2,
        OperationCompleted = 3,
        FailOnCancelInsurance = 4,
        Exception = 5
    }

    public enum PushNotificationJobStatus : byte
    {
        Initial = 1,
        OperationStarted = 2,
        OperationCompleted = 3,
        FailOnSending = 4,
        Exception = 5
    }

    public enum InsuranceCancellationReason : byte
    {
        CancellationOfRejection = 1
    }

    public enum ExtraFeeCategoryType
    {
        [LocalizedDescription(nameof(EnumResources.Other))]
        Other = 1,
        [LocalizedDescription(nameof(EnumResources.Insurance))]
        Insurance = 2,
        [LocalizedDescription(nameof(EnumResources.PCR))]
        PCR = 3,
        [LocalizedDescription(nameof(EnumResources.Application))]
        Application = 4,
        [LocalizedDescription(nameof(EnumResources.Cargo))]
        Cargo = 5,
        [LocalizedDescription(nameof(EnumResources.ServiceFee))]
        ServiceFee = 6,
        [LocalizedDescription(nameof(EnumResources.YSS))]
        YSS = 7,
        [LocalizedDescription(nameof(EnumResources.TS))]
        TS = 8,
        [LocalizedDescription(nameof(EnumResources.RelatedInsurance))]
        RelatedInsurance = 9
	}

    public enum YSSCategoryAgeRange
    {
        [LocalizedDescription(nameof(EnumResources.Zero_Fifteen))]
        Zero_Fifteen = 1,
        [LocalizedDescription(nameof(EnumResources.Sixteen_TwentyFive))]
        Fifteen_TwentyFive = 2,
        [LocalizedDescription(nameof(EnumResources.TwentySix_ThirtyFive))]
        TwentySix_ThirtyFive = 3,
        [LocalizedDescription(nameof(EnumResources.ThirtySix_FourtyFive))]
        ThirtySix_FourtyFive = 4,
        [LocalizedDescription(nameof(EnumResources.FourtySix_Fifty))]
        FourtySix_Fifty = 5, 
        [LocalizedDescription(nameof(EnumResources.FiftyOne_FiftyFive))]
        FiftyOne_FiftyFive = 6, 
        [LocalizedDescription(nameof(EnumResources.FiftySix_Sixty))]
        FiftySix_Sixty = 7,
        [LocalizedDescription(nameof(EnumResources.SixtyOne_SixtyFive))]
        SixtyOne_SixtyFive = 8,
        [LocalizedDescription(nameof(EnumResources.SixtySix_SixtyNine))]
        SixtySix_SixtyNine = 9
    }

    public enum VisaCategoryTypeArabic
    {
        [Description("غير محدد")]
        Unspecified = 0,
        [Description("سياحي")]
        Touristic = 1,
        [Description("علاج")]
        Health = 2,
        [Description("زيارة العائلة والأصدقاء")]
        FamilyAndFriendVisit = 3,
        [Description("عمل")]
        Business = 4,
        [Description("ثقافي رياضي")]
        CulturalSportive = 5,
        [Description("طالب")]
        Student = 6,
        [Description("اذن عمل")]
        WorkPermit = 7,
        [Description("عبور")]
        Transit = 8,
        [Description("لمش شمل العائلة")]
        FamilyUnion = 9,
        [Description("سائق")]
        Driver = 10,
        [Description("موافقة")]
        Consenting = 11,
        [Description("خدمة")]
        Servant = 12,
        [Description("مونتاج")]
        Montage = 13,
        [Description("التأمين غير التطبيقي")]
        NonApplicationInsurance = 14,
        [Description("منع الدخول")]
        EntryBanned = 15,
        [Description("اذن عمل معتمد")]
        ApprovedWorkPermit = 16,
        [Description("نقل التأشيرة")]
        VisaTransfer = 17,
        [Description("معرض مؤتمر")]
        ConferenceFair = 18,
        [Description("تطبيق وزارة الصحة")]
        MinistryOfHealthApplication = 19,
        [Description("مرافق")]
		Companion = 20,
		[Description("مرافق مريض")]
		PatientCompanion = 21,
		[Description("مرافق طالب")]
		StudentCompanion = 22,
		[Description("مرافق رخصة عمل")]
		WorkPermitCompanion = 23,
        [Description("الفيزا الالكترونية")]
        EVisa = 24,
        [Description("معرض")]
        Exhibition = 25,
        [Description("Tömer")]
        Tomer = 26,
		[Description("طالب ERASMUS")]
		ERASMUS = 27,
		[Description("طالب IAESTE")]
		IAESTE = 28,
		[Description("طالب AIESEC")]

		AIESEC = 29,
        [Description("تأشيرة الرحل الرقمية")]
        DigitalNomad = 30,
        [Description("Short Term Others")]
        ShortTermOthers = 31,
        [Description("Long Term")]
        LongTerm = 32,
        [Description("Partner Approved")]
        PartnerApproved = 33,
        [Description("Consulate Embassy Approved")]
        ConsulateEmbassyApproved = 34,
        [Description("Interview")]
        Interview = 35,
        [Description("Ciblage")]
        Ciblage = 36,
        [Description("Refugee")]
        Refugee = 37,
        [Description("Orange Carpet")]
        OrangeCarpet = 38,
        [Description("Blue Carpet")]
        BlueCarpet = 39,
		[Description("Touristic Residence Permit")]
		TouristicResidencePermit = 40,
		[Description("Visit - Special Residence Permit")]
		VisitSpecialResidancePermit = 41
	}

    public enum VisaCategoryTypeTurkmen
    {
        [Description("Belli däl")]
        Unspecified = 0,
        [Description("Syýahatçylyk")]
        Touristic = 1,
        [Description("Saglyk")]
        Health = 2,
        [Description("Maşgala we dost görmek")]
        FamilyAndFriendVisit = 3,
        [Description("Işewürlik")]
        Business = 4,
        [Description("Medeni sport")]
        CulturalSportive = 5,
        [Description("Talyp")]
        Student = 6,
        [Description("Iş Rugsady")]
        WorkPermit = 7,
        [Description("Tranzit")]
        Transit = 8,
        [Description("Maşgala bileleşigi")]
        FamilyUnion = 9,
        [Description("Sürüji")]
        Driver = 10,
        [Description("Razylyk")]
        Consenting = 11,
        [Description("Hyzmatkär")]
        Servant = 12,
        [Description("Montaage")]
        Montage = 13,
        [Description("Programma ätiýaçlandyryşy")]
        NonApplicationInsurance = 14,
        [Description("Giriş gadagan")]
        EntryBanned = 15,
        [Description("Makullanan iş rugsady")]
        ApprovedWorkPermit = 16,
        [Description("Wiza geçirmek")]
        VisaTransfer = 17,
        [Description("Maslahat ýarmarkasy")]
        ConferenceFair = 18,
        [Description("Saglygy Goraýyş Ministrligi")]
        MinistryOfHealthApplication = 19,
        [Description("Ýoldaşlyk")]
        Companion = 20,
        [Description("Talyp Ýoldaşlyk")]
        PatientCompanion = 21,
        [Description("Näsaga Ýoldaşlyk Etmek")]
        StudentCompanion = 22,
        [Description("İşlemek Üçin Rugsat Ýoldaşlyk Etmek")]
        WorkPermitCompanion = 23,
        [Description("Elektron wiza")]
        EVisa = 24,
        [Description("Sergi")]
        Exhibition = 25,
        [Description("Tömer")]
        Tomer = 26,
		[Description("ERASMUS Talyp")]
		ERASMUS = 27,
		[Description("IAESTE Talyp")]
		IAESTE = 28,
		[Description("AIESEC Talyp")]
		AIESEC = 29,
        [Description("Sanly Çarwa Wizasy")]
        DigitalNomad = 30,
        [Description("Short Term Others")]
        ShortTermOthers = 31,
        [Description("Long Term")]
        LongTerm = 32,
        [Description("Partner Approved")]
        PartnerApproved = 33,
        [Description("Consulate Embassy Approved")]
        ConsulateEmbassyApproved = 34,
        [Description("Interview")]
        Interview = 35,
        [Description("Ciblage")]
        Ciblage = 36,
        [Description("Refugee")]
        Refugee = 37,
        [Description("Orange Carpet")]
        OrangeCarpet = 38,
        [Description("Blue Carpet")]
        BlueCarpet = 39,
		[Description("Syýahatçylyk Ýaşaýyş Rugsady")]
		TouristicResidencePermit = 40,
		[Description("Sapar-Ýörite Ýaşaýyş Rugsady")]
		VisitSpecialResidancePermit = 41
	}

    public enum VisaCategoryTypeRussian
    {
        [Description("Неопределенные")]
        Unspecified = 0,
        [Description("Туристический")]
        Touristic = 1,
        [Description("Здоровье")]
        Health = 2,
        [Description("СемьяИДругПосетить")]
        FamilyAndFriendVisit = 3,
        [Description("Бизнес")]
        Business = 4,
        [Description("Культурный Спортивный")]
        CulturalSportive = 5,
        [Description("Ученик")]
        Student = 6,
        [Description("Разрешение на работу")]
        WorkPermit = 7,
        [Description("Транзит")]
        Transit = 8,
        [Description("Семейный союз")]
        FamilyUnion = 9,
        [Description("Водитель")]
        Driver = 10,
        [Description("Согласие")]
        Consenting = 11,
        [Description("Слуга")]
        Servant = 12,
        [Description("Монтаж")]
        Montage = 13,
        [Description("Страхование от неприменения")]
        NonApplicationInsurance = 14,
        [Description("Вход запрещен")]
        EntryBanned = 15,
        [Description("Утвержденное разрешение на работу")]
        ApprovedWorkPermit = 16,
        [Description("Виза Трансфер")]
        VisaTransfer = 17,
        [Description("Конференц-ярмарка")]
        ConferenceFair = 18,
        [Description("заявление министерства здравоохранения")]
        MinistryOfHealthApplication = 19,
        [Description("Cопровождение")]
        Companion = 20,
        [Description("Сопровождение пациента")]
        PatientCompanion = 21,
        [Description("Студенческое сопровождение")]
        StudentCompanion = 22,
        [Description("Cопровождение разрешение на работу")]
        WorkPermitCompanion = 23,
        [Description("Электронная виза")]
        EVisa = 24,
        [Description("Выставка")]
        Exhibition = 25,
        [Description("Tömer")]
        Tomer = 26,
		[Description("ERASMUS Студент(ка)")]
		ERASMUS = 27,
		[Description("IAESTE Студент(ка)")]
		IAESTE = 28,
		[Description("AIESEC Студент(ка)")]
		AIESEC = 29,
        [Description("цифровой кочевник")]
        DigitalNomad = 30,
        [Description("Short Term Others")]
        ShortTermOthers = 31,
        [Description("Long Term")]
        LongTerm = 32,
        [Description("Partner Approved")]
        PartnerApproved = 33,
        [Description("Consulate Embassy Approved")]
        ConsulateEmbassyApproved = 34,
        [Description("Interview")]
        Interview = 35,
        [Description("Ciblage")]
        Ciblage = 36,
        [Description("Refugee")]
        Refugee = 37,
        [Description("Orange Carpet")]
        OrangeCarpet = 38,
        [Description("Blue Carpet")]
        BlueCarpet = 39,
		[Description("Туристический вид на жительство")]
		TouristicResidencePermit = 40,
		[Description("Визит-Специальный вид на жительство")]
		VisitSpecialResidancePermit = 41
	}

    public enum VisaCategoryTypeFrench
    {
        [Description("Non Spécifié")]
        Unspecified = 0,
        [Description("Touristique")]
        Touristic = 1,
        [Description("Santé")]
        Health = 2,
        [Description("Visite Familiale/ Visite d'Amis")]
        FamilyAndFriendVisit = 3,
        [Description("Affaire")]
        Business = 4,
        [Description("Culturel Sportif")]
        CulturalSportive = 5,
        [Description("Etudiant")]
        Student = 6,
        [Description("Permis de Travail")]
        WorkPermit = 7,
        [Description("Transit")]
        Transit = 8,
        [Description("Regroupement Familiale")]
        FamilyUnion = 9,
        [Description("Chauffeur")]
        Driver = 10,
        [Description("Approbation")]
        Consenting = 11,
        [Description("Agent")]
        Servant = 12,
        [Description("Montage")]
        Montage = 13,
        [Description("Sana Assurance de Demandeur")]
        NonApplicationInsurance = 14,
        [Description("Entrée Interdite")]
        EntryBanned = 15,
        [Description("Permis de Travail Apprové")]
        ApprovedWorkPermit = 16,
        [Description("Visa de transfert")]
        VisaTransfer = 17,
        [Description("Foire conférence")]
        ConferenceFair = 18,
        [Description("Demande du ministère de la santé")]
        MinistryOfHealthApplication = 19,
        [Description("Accompagnement")]
        Companion = 20,
        [Description("Accompagnement du patient")]
        PatientCompanion = 21,
        [Description("Accompagnement d'étudiant.")]
        StudentCompanion = 22,
        [Description("Accompagnement de permis de travail")]
        WorkPermitCompanion = 23,
        [Description("Visa électronique")]
        EVisa = 24,
        [Description("L’exposition")]
        Exhibition = 25,
        [Description("Tömer")]
        Tomer = 26,
		[Description("ERASMUS Étudiant(e)")]
		ERASMUS = 27,
		[Description("IAESTE Étudiant(e)")]
		IAESTE = 28,
		[Description("AIESEC Étudiant(e)")]
		AIESEC = 29,
        [Description("Visa de nomade digitale")]
        DigitalNomad = 30,
        [Description("Short Term Others")]
        ShortTermOthers = 31,
        [Description("Long Term")]
        LongTerm = 32,
        [Description("Partner Approved")]
        PartnerApproved = 33,
        [Description("Consulate Embassy Approved")]
        ConsulateEmbassyApproved = 34,
        [Description("Interview")]
        Interview = 35,
        [Description("Ciblage")]
        Ciblage = 36,
        [Description("Refugee")]
        Refugee = 37,
        [Description("Orange Carpet")]
        OrangeCarpet = 38,
        [Description("Blue Carpet")]
        BlueCarpet = 39,
		[Description("Touristic Residence Permit")]
		TouristicResidencePermit = 40,
		[Description("Visit- Special Residence Permit")]
		VisitSpecialResidancePermit = 41
	}

    public enum VisaDurationType
    {
        [LocalizedDescription(nameof(EnumResources.OneMonth))]
        OneMonth = 1,
        [LocalizedDescription(nameof(EnumResources.TwoMonths))]
        TwoMonths = 2,
        [LocalizedDescription(nameof(EnumResources.ThreeMonths))]
        ThreeMonths = 3,
        [LocalizedDescription(nameof(EnumResources.FourMonths))]
        FourMonths = 4,
        [LocalizedDescription(nameof(EnumResources.FiveMonths))]
        FiveMonths = 5,
        [LocalizedDescription(nameof(EnumResources.SixMonths))]
        SixMonths = 6,
        [LocalizedDescription(nameof(EnumResources.SevenMonths))]
        SevenMonths = 7,
        [LocalizedDescription(nameof(EnumResources.EightMonths))]
        EightMonths = 8,
        [LocalizedDescription(nameof(EnumResources.NineMonths))]
        NineMonths = 9,
        [LocalizedDescription(nameof(EnumResources.TenMonths))]
        TenMonths = 10,
        [LocalizedDescription(nameof(EnumResources.ElevenMonths))]
        ElevenMonths = 11,
        [LocalizedDescription(nameof(EnumResources.OneYear))]
        OneYear = 12,
        [LocalizedDescription(nameof(EnumResources.TwoYears))]
        TwoYears = 13,
        [LocalizedDescription(nameof(EnumResources.ThreeYears))]
        ThreeYears = 14,
        [LocalizedDescription(nameof(EnumResources.FourYears))]
        FourYears = 15,
        [LocalizedDescription(nameof(EnumResources.FiveYears))]
        FiveYears = 16,
        [LocalizedDescription(nameof(EnumResources.NLL))]
        NLL = 17,
        [LocalizedDescription(nameof(EnumResources.IR))]
        IR = 18,
        [LocalizedDescription(nameof(EnumResources.YGY))]
        YGY = 19,
        [LocalizedDescription(nameof(EnumResources.Other))]
        Other = 20,
        [LocalizedDescription(nameof(EnumResources.NLL_Unreal_Document))]
        NLL_Unreal_Document = 21,
	}

    public enum VisaDurationOtherType
    {
        [LocalizedDescription(nameof(EnumResources.Year))]
        Year = 1,
        [LocalizedDescription(nameof(EnumResources.Month))]
        Month = 2,
        [LocalizedDescription(nameof(EnumResources.Day))]
        Day = 3,
    }

	public enum PolicyPeriodDurationUnit
	{
		[LocalizedDescription(nameof(EnumResources.OneMonth))]
		OneMonth = 1,
		[LocalizedDescription(nameof(EnumResources.ThreeMonths))]
		ThreeMonths = 2,
		[LocalizedDescription(nameof(EnumResources.SixMonths))]
		SixMonths = 3,
		[LocalizedDescription(nameof(EnumResources.OneYear))]
		OneYear = 4,
		[LocalizedDescription(nameof(EnumResources.TwoYears))]
		TwoYears = 5,
		[LocalizedDescription(nameof(EnumResources.ThreeYears))]
		ThreeYears = 6,
		[LocalizedDescription(nameof(EnumResources.FourYears))]
		FourYears = 7,
		[LocalizedDescription(nameof(EnumResources.FiveYears))]
		FiveYears = 8,
	}

	public enum NumberOfEntryType
    {
        [LocalizedDescription(nameof(EnumResources.Multiple))]
        Multiple = 1,
        [LocalizedDescription(nameof(EnumResources.Single))]
        Single = 2,
        [LocalizedDescription(nameof(EnumResources.Double))]
        Double = 3
    }

    public enum VisaIsUsedYear
    {
        [LocalizedDescription(nameof(EnumResources.Ikibinonalti))]
        Ikibinonalti = 1,
        [LocalizedDescription(nameof(EnumResources.Ikibinonyedi))]
        Ikibinonyedi = 2,
        [LocalizedDescription(nameof(EnumResources.Ikibinonsekiz))]
        Ikibinonsekiz = 3,
        [LocalizedDescription(nameof(EnumResources.Ikibinondokuz))]
        Ikibinondokuz = 4,
        [LocalizedDescription(nameof(EnumResources.Ikibinyirmi))]
        Ikibinyirmi = 5,
        [LocalizedDescription(nameof(EnumResources.Ikibinyirmibir))]
        Ikibinyirmibir = 6,
        [LocalizedDescription(nameof(EnumResources.Ikibinyirmiiki))]
        Ikibinyirmiiki = 7,
        [LocalizedDescription(nameof(EnumResources.Ikibinyirmiuc))]
        Ikibinyirmiuc = 8,
        [LocalizedDescription(nameof(EnumResources.Ikibinyirmidort))]
        Ikibinyirmidort = 9,
        [LocalizedDescription(nameof(EnumResources.Ikibinyirmibes))]
        Ikibinyirmibes = 10,
        [LocalizedDescription(nameof(EnumResources.Ikibinyirmialti))]
        Ikibinyirmialti = 11,
    }
    public enum ApplicationFormElement
    {
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementTotalYearInCountry))]
        TotalYearInCountry = 1,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementReimbursementType))]
        ReimbursementType = 2,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementReimbursementSponsorDetail))]
        ReimbursementSponsorDetail = 3,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementJob))]
        Job = 4,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementCompanyName))]
        CompanyName = 5,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementTotalYearInCompany))]
        TotalYearInCompany = 6,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementMonthlySalary))]
        MonthlySalary = 7,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementMonthlySalaryCurrency))]
        MonthlySalaryCurrency = 8,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementBankBalance))]
        BankBalance = 9,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementBankBalanceCurrency))]
        BankBalanceCurrency = 10,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementCityName))]
        CityName = 11,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementAccomodationDetail))]
        AccomodationDetail = 12,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementRelativeLocation))]
        RelativeLocation = 13,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementPersonTravelWith))]
        PersonTravelWith = 14,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementApplicantsMartialStatus))]
        ApplicantsMartialStatus = 15,
        [LocalizedDescription(nameof(EnumResources.ApplicationFormElementPassportExpiryDate))]
        PassportExpiryDate = 16,
        [LocalizedDescription(nameof(EnumResources.ApplicationTogether))]
		ApplicationTogether = 17
    }

    public enum ApplicantType
    {
        [LocalizedDescription(nameof(EnumResources.ApplicantTypeIndividual))]
        Individual = 1,
        [LocalizedDescription(nameof(EnumResources.ApplicantTypeFamily))]
        Family = 2,
        [LocalizedDescription(nameof(EnumResources.ApplicantTypeGroup))]
        Group = 3,
        [LocalizedDescription(nameof(EnumResources.ApplicantTypeRepresentative))]
        Representative = 5
    }

    public enum ConfirmationCodeType
    {
        SmsCode = 1,
        MailCode = 2,
	}

	public enum ConfirmationType
    {
        [LocalizedDescription(nameof(EnumResources.Approved))]
        Approved = 1,
        [LocalizedDescription(nameof(EnumResources.NotApproved))]
        NotApproved = 2,
        [LocalizedDescription(nameof(EnumResources.SmsSendingError))]
        SmsSendingError = 3,
        [LocalizedDescription(nameof(EnumResources.MailSendingError))]
        MailSendingError = 4,
    }

    public enum ApplicantIndividualRelationship
    {
        [LocalizedDescription(nameof(EnumResources.HimselfHerself))]
        HimselfHerself = 99
    }

    public enum ApplicantFamilyRelationship
    {
        [LocalizedDescription(nameof(EnumResources.HusbandOrWife))]
        HusbandOrWife = 1,
        [LocalizedDescription(nameof(EnumResources.Child))]
        Child = 2
    }

    public enum ApplicantGroupRelationship
    {
        [LocalizedDescription(nameof(EnumResources.Accompaniment))]
        Accompaniment = 6,
        [LocalizedDescription(nameof(EnumResources.Donor))]
        Donor = 7
    }

    public enum LocalAuthorityStatus
    {
        [LocalizedDescription(nameof(EnumResources.Accepted))]
        Accepted = 1,
        [LocalizedDescription(nameof(EnumResources.Rejected))]
        Rejected = 2,
        [LocalizedDescription(nameof(EnumResources.AdditionalTime))]
        AdditionalTime = 3
    }

    public enum WaitingTimeForDocumentStatus
    {
        [LocalizedDescription(nameof(EnumResources.After24Hours))]
        After24Hours = 1,
        [LocalizedDescription(nameof(EnumResources.After48Hours))]
        After48Hours = 2,
        [LocalizedDescription(nameof(EnumResources.After72Hours))]
        After72Hours = 3,
        [LocalizedDescription(nameof(EnumResources.After96Hours))]
        After96Hours = 4
    }

    public enum ApplicationType
    {
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeNormal))]
        Normal = 1,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeFree))]
        Free = 2,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationInsurance))]
        NonApplicationInsurance = 3,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPcr))]
        NonApplicationPcr = 4,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquois))]
        Turquois = 5,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquoisPremium))]
        TurquoisPremium = 6,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquoisGratis))]
        TurquoisGratis = 7,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPrintOut))]
        NonApplicationPrintOut = 8,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPhotocopy))]
        NonApplicationPhotocopy = 9,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPhotograph))]
        NonApplicationPhotograph = 10,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplication))]
        NonApplication = 11,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquoisWithGratisNote))]
        TurquoisWithGratisNote = 12,
        [LocalizedDescription(nameof(EnumResources.NonApplicationRelatedInsurance))]
        NonApplicationRelatedInsurance = 14
    }

    public enum AdditionalServiceType
    {
		//[LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationInsurance))]
		//NonApplicationInsurance = 1,
		//[LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPhotograph))]
		//NonApplicationPhotograph = 2,
		//[LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPrintOut))]
		//NonApplicationPrintOut = 3,
	    [LocalizedDescription(nameof(EnumResources.YSS))]
	    YSS = 4,
        [LocalizedDescription(nameof(EnumResources.TS))]
        TS = 5
    }

	public enum VehicleType
	{
		[LocalizedDescription(nameof(EnumResources.SpecialCar))]
		SpecialCar = 1,
		[LocalizedDescription(nameof(EnumResources.Van))]
		Van = 2
	}
	public enum ApplicationPassportStatus
    {
        [LocalizedDescription(nameof(EnumResources.ApplicationWithPassport))]
        WithPassport = 1,
        [LocalizedDescription(nameof(EnumResources.ApplicationWithoutPassport))]
        WithoutPassport = 2
    }

    public enum Title
    {
        [LocalizedDescription(nameof(EnumResources.Mr))]
        Mr = 1,
        [LocalizedDescription(nameof(EnumResources.Mrs))]
        Mrs = 2,
        [LocalizedDescription(nameof(EnumResources.Ms))]
        Ms = 3
    }

    public enum InfoDeskTypes
    {
        [LocalizedDescription(nameof(EnumResources.ByAppointment))]
        Appointment = 1,
        [LocalizedDescription(nameof(EnumResources.WalkIn))]
        WalkIn = 2,
        [LocalizedDescription(nameof(EnumResources.ReBiometry))]
        ReBiometry = 3,
        [LocalizedDescription(nameof(EnumResources.PassportDelivery))]
        PassportDelivery = 4
    }

    public enum AppointmentStatus
    {
        [LocalizedDescription(nameof(EnumResources.ByAppointment))]
        Appointment = 1,
        [LocalizedDescription(nameof(EnumResources.WalkIn))]
        WalkIn = 2,
    }

    public enum QueueApplicantRelationalType
    {
        Main = 1,
        Sub = 2,
        Individual = 3
    }

    public enum QueueMaticStatusTypes
    {
        [LocalizedDescription(nameof(EnumResources.ProcessNotStarted))]
        Created = 1,
        [LocalizedDescription(nameof(EnumResources.InProgress))]
        InProgress = 2,
        [LocalizedDescription(nameof(EnumResources.PutOnHold))]
        PutOnHold = 3,
        [LocalizedDescription(nameof(EnumResources.WaitingForAdditionalDocuments))]
        WaitingForAdditionalDocument = 4,
        [LocalizedDescription(nameof(EnumResources.Completed))]
        Completed = 5
    }

    public enum Gender
    {
        [LocalizedDescription(nameof(EnumResources.Male))]
        Male = 1,
        [LocalizedDescription(nameof(EnumResources.Female))]
        Female = 2
    }

    public enum MaritalStatus
    {
        [LocalizedDescription(nameof(EnumResources.MaritalStatusSingle))]
        Single = 1,
        [LocalizedDescription(nameof(EnumResources.MaritalStatusMarried))]
        Married = 2,
        [LocalizedDescription(nameof(EnumResources.MaritalStatusDivorced))]
        Divorced = 3,
        [LocalizedDescription(nameof(EnumResources.MaritalStatusWidowWidower))]
        WidowWidower = 4
    }

    public enum ProviderType
    {
        [LocalizedDescription(nameof(EnumResources.Unico))]
        [InsuranceAttribute(true)]
        Unico = 1,
        [LocalizedDescription(nameof(EnumResources.Emaa))]
        [InsuranceAttribute(true)]
        Emaa = 3,
        [LocalizedDescription(nameof(EnumResources.EmaaTs))]
		[InsuranceAttribute(true)]
		EmaaTs = 4,
        [LocalizedDescription(nameof(EnumResources.EmaaRi))]
        [InsuranceAttribute(true)]
        EmaaRi = 5
	}
    public enum RejectedPolicyType
    {
        [LocalizedDescription(nameof(EnumResources.ThreeMonths))]
        [InsuranceAttribute(true)]
        ThreeMonths = 1,
        [LocalizedDescription(nameof(EnumResources.SixMonths))]
        [InsuranceAttribute(true)]
        SixMonths = 2,
        [LocalizedDescription(nameof(EnumResources.OneYear))]
        [InsuranceAttribute(true)]
        OneYear = 3
    }
    public enum QmsCompanyType
    {
        [LocalizedDescription(nameof(EnumResources.Gateway))]
        Gateway = 1,
        [LocalizedDescription(nameof(EnumResources.Vfs))]
        Vfs = 2,
        [LocalizedDescription(nameof(EnumResources.MinistryTurkey))]
        MinistryTurkey = 3,
        [LocalizedDescription(nameof(EnumResources.GatewayInternationalVfs))]
        GatewayInternationalVfs = 4,
        [LocalizedDescription(nameof(EnumResources.GatewayManagementVfs))]
        GatewayManagementVfs = 5,
    }

    public enum CargoProviderType
    {
        [LocalizedDescription(nameof(EnumResources.UPS))]        
        UPS = 1,
        [LocalizedDescription(nameof(EnumResources.LastMile))]
        LastMile = 2
    }

    public enum SmsProviderType
    {
        TurkTelekom = 1,
        Horisen = 2,
        Clickatell = 3,
        Sinch = 4,
		[LocalizedDescription(nameof(EnumResources.InfoBip_IRQ))]
		InfoBip_IRQ = 5,
		[LocalizedDescription(nameof(EnumResources.InfoBip_TKM))]
		InfoBip_TKM = 6,
		[LocalizedDescription(nameof(EnumResources.InfoBip_KSA))]
		InfoBip_KSA = 7,
		[LocalizedDescription(nameof(EnumResources.InfoBip_RUS))]
		InfoBip_RUS = 8,
		[LocalizedDescription(nameof(EnumResources.InfoBip_KWT))]
		InfoBip_KWT = 9,
		[LocalizedDescription(nameof(EnumResources.InfoBip_UAE))]
		InfoBip_UAE = 10,
		[LocalizedDescription(nameof(EnumResources.InfoBip_IND))]
		InfoBip_IND = 11,
        [LocalizedDescription(nameof(EnumResources.InfoBip_ALG))]
        InfoBip_ALG = 12,
        [LocalizedDescription(nameof(EnumResources.InfoBip_LIB))]
        InfoBip_LIB = 13,
    }

    public enum EmailProviderType
    {
        SendGrid = 1,
        SmtpOffice365 = 2,
    }

    public enum ReimbursementType
    {
        [LocalizedDescription(nameof(EnumResources.ReimbursementTypeHimselfHerself))]
        HimselfHerself = 1,
        [LocalizedDescription(nameof(EnumResources.ReimbursementTypeNoFinancialDocument))]
        NoFinancialDocument = 2,
        [LocalizedDescription(nameof(EnumResources.ReimbursementTypeSponsor))]
        Sponsor = 3
    }

    public enum VisaEntryType
    {
        [LocalizedDescription(nameof(EnumResources.VisaEntryTypeSingle))]
        Single = 1,
        [LocalizedDescription(nameof(EnumResources.VisaEntryTypeMultiple))]
        Multiple = 2
    }

    public enum PreApplicationInsuranceType
    {
        [LocalizedDescription(nameof(EnumResources.HealthInsurance))]
        HealthInsurance = 1,
        [LocalizedDescription(nameof(EnumResources.TrafficInsurance))]
        TrafficInsurance = 2
    }

    public enum InsuranceTypeForApplicationCategorization
    {
        [LocalizedDescription(nameof(EnumResources.TravelHealth))]
        TravelHealth = 1,
        [LocalizedDescription(nameof(EnumResources.RelatedInsurance))]
        RelatedInsurance = 2
    }

    public enum GoogleTranslateLanguages
    {
        [Description("en-US")]
        English = 1,
        [Description("tr-TR")]
        Turkish = 2,
        [Description("ar-AR")]
        Arabic = 3
    }

    public enum PhotoBoothStatus
    {
        [LocalizedDescription(nameof(EnumResources.NotOrdered))]
        NotOrdered = 0,
        [LocalizedDescription(nameof(EnumResources.Ordered))]
        Ordered = 1,
        [LocalizedDescription(nameof(EnumResources.Used))]
        Used = 2,
        [LocalizedDescription(nameof(EnumResources.Expired))]
        Expired = 3,
        [LocalizedDescription(nameof(EnumResources.Deleted))]
        Deleted = 4,
		[LocalizedDescription(nameof(EnumResources.BadShot))]
		BadShot = 5, 
        [LocalizedDescription(nameof(EnumResources.ServiceError))]
		ServiceError = 6

	}

    public enum ApplicationCancellationType
    {
        [LocalizedDescription(nameof(EnumResources.ApplicationCancellationTypePartialRefund))]
        PartialRefund = 1,
        [LocalizedDescription(nameof(EnumResources.ApplicationCancellationTypeCancellation))]
        Cancellation = 2
    }

    public enum RelatedInsuranceApplicationCancellationType
    {
        [LocalizedDescription(nameof(EnumResources.AllApplications))]
        AllApplications = 1,
        [LocalizedDescription(nameof(EnumResources.OnlyNormalApplications))]
        OnlyNormalApplications = 2
    }

    public enum ApplicationCancellationReason
    {
        [LocalizedDescription(nameof(EnumResources.IncorrectEntryIcr))]
        IncorrectEntryIcr = 1,
        [LocalizedDescription(nameof(EnumResources.PersonalLoginError))]
        PersonalLoginError = 2,
        [LocalizedDescription(nameof(EnumResources.IncompletedApplication))]
        IncompletedApplication = 3,
        [LocalizedDescription(nameof(EnumResources.ApplicantRequest))]
        ApplicantRequest = 4,
        [LocalizedDescription(nameof(EnumResources.SystemError))]
        SystemError = 5,
        [LocalizedDescription(nameof(EnumResources.InsuranceError))]
        InsuranceError = 6,
        [LocalizedDescription(nameof(EnumResources.Other))]
        Other = 7,
        [LocalizedDescription(nameof(EnumResources.NegativeIHB))]
        NegativeIhb = 8,
        [LocalizedDescription(nameof(EnumResources.NotAcceptedByTheEmbassy))]
        NotAcceptedByTheEmbassy = 9
	}

    public enum ApplicationCancellationStatus
    {
        [LocalizedDescription(nameof(EnumResources.Pending))]
        Pending = 0,
        [LocalizedDescription(nameof(EnumResources.Rejected))]
        Rejected = 1,
        [LocalizedDescription(nameof(EnumResources.Approved))]
        Approved = 2
    }

    public enum ApplicationStatus
    {
        [LocalizedDescription(nameof(EnumResources.ApplicationStatusActive))]
        Active = 1,
        [LocalizedDescription(nameof(EnumResources.ApplicationStatusCancelled))]
        Cancelled = 2,
        [LocalizedDescription(nameof(EnumResources.ApplicationStatusPartiallyRefunded))]
        PartiallyRefunded = 3
    }

    public enum RelatedInsuranceApplicationStatus
    {
        [LocalizedDescription(nameof(EnumResources.Active))]
        Active = 1,
        [LocalizedDescription(nameof(EnumResources.Passive))]
        Passive = 2
    }

    public enum RelatedInsuranceStatus
    {
        [LocalizedDescription(nameof(EnumResources.Cancelled))]
        Cancelled = 1,
        [LocalizedDescription(nameof(EnumResources.Insured))]
        Insured = 2,
        [LocalizedDescription(nameof(EnumResources.OnHold))]
        OnHold = 3
    }
    public enum IncorrectApplicationStatusReason
    {
        [LocalizedDescription(nameof(EnumResources.WithdrawalByApplicant))]
        WithdrawalByApplicant = 1,
        [LocalizedDescription(nameof(EnumResources.ConsulateDecision))]
        ConsulateDecision = 2,
        [LocalizedDescription(nameof(EnumResources.ConsulateCanceledRefund))]
        ConsulateCanceledRefund = 3,
        [LocalizedDescription(nameof(EnumResources.OfficeWaitingForMissingDocuments))]
        OfficeWaitingForMissingDocuments = 4,
        [LocalizedDescription(nameof(EnumResources.Other))]
        Other = 5
    }

    public enum QmsApplicationStatus
    {
        [LocalizedDescription(nameof(EnumResources.ApplicationDone))]
        ApplicationDone = 1,
        [LocalizedDescription(nameof(EnumResources.ApplicationNotReceived))]
        ApplicationNotReceived = 2,
        [LocalizedDescription(nameof(EnumResources.Postponed))]
        Postponed = 3,
	    [LocalizedDescription(nameof(EnumResources.PassportDelivery))]
	    PassportDelivery = 4
	}

    public enum QmsApplicationCancellationStatus
    {
        [LocalizedDescription(nameof(EnumResources.Active))]
        Active = 1,
        [LocalizedDescription(nameof(EnumResources.ApplicationCancelled))]
        ApplicationCancelled = 2,
        [LocalizedDescription(nameof(EnumResources.ApplicationNotReceived))]
        ApplicationNotReceived = 3
    }

    public enum ApplicationStatusTrackingView
    {
        [LocalizedDescription(nameof(EnumResources.ApplicationTaken))]
        ApplicationTaken,
        [LocalizedDescription(nameof(EnumResources.ApplicationUnderEvaluation))]
        ApplicationUnderEvaluation,
        [LocalizedDescription(nameof(EnumResources.HandDeliveredToTheApplicant))]
        HandDeliveredToTheApplicant,
        [LocalizedDescription(nameof(EnumResources.DeliveredToCargo))]
        DeliveredToCargo,
        [LocalizedDescription(nameof(EnumResources.ReceivedAtVisaCenter))]
        ReceivedAtVisaCenter,
        [LocalizedDescription(nameof(EnumResources.FileWithdrewAccordingtoCustomerRequest))]
        FileWithdrewAccordingtoCustomerRequest,
        [LocalizedDescription(nameof(EnumResources.ReceivedAtMainVisaApplicationCenter))]
        ReceivedAtMainVisaApplicationCenter,
        [LocalizedDescription(nameof(EnumResources.SentFromMainVisaApplicationCenterToVisaApplicationCenter))]
        SentFromMainVisaApplicationCenterToVisaApplicationCenter,
        [LocalizedDescription(nameof(EnumResources.SentToMainVisaApplicationCenter))]
        SentToMainVisaApplicationCenter,
        [LocalizedDescription(nameof(EnumResources.SentFromEmbassyToMainVisaApplicationCenter))]
        SentFromEmbassyToMainVisaApplicationCenter,
        [LocalizedDescription(nameof(EnumResources.CourierReturnedPassportToOffice))]
        CourierReturnedPassportToOffice
    }

    public enum ApplicationStatusType
    {
        [LocalizedDescription(nameof(EnumResources.ApplicationTaken))] 
        ApplicationTaken = 1,
        [LocalizedDescription(nameof(EnumResources.DataDone))] 
        DataDone = 2,
        [LocalizedDescription(nameof(EnumResources.SendToEmbassy))]
        SendToEmbassy = 3,
        [LocalizedDescription(nameof(EnumResources.ReceivedAtEmbassy))] // Equivalent of PendingApproval in db
        ReceivedAtEmbassy = 4,
        [LocalizedDescription(nameof(EnumResources.Istizan))]
        Istizan = 5,
        [LocalizedDescription(nameof(EnumResources.IstizanDelvieredToApplicant))]
        IstizanDelvieredToApplicant = 6,
        [LocalizedDescription(nameof(EnumResources.IstizanOutscanToOffice))]
        IstizanOutscanToOffice = 7,
        [LocalizedDescription(nameof(EnumResources.SendToEmbassyForFinalApproval))] // Equivalent of PendingApproval in db
        SendToEmbassyForFinalApproval = 8,
        [LocalizedDescription(nameof(EnumResources.SentCenterDueToMissingDocuments))]
        SentCenterDueToMissingDocuments = 9,
        [LocalizedDescription(nameof(EnumResources.AdditionalDocumentSentToEmbassy))] // Equivalent of PendingApproval in db
        AdditionalDocumentsSentToEmbassy = 11,
        [LocalizedDescription(nameof(EnumResources.AdditionalDocumentsReceivedAtEmbassy))] // Equivalent of PendingApproval in db
        AdditionalDocumentsReceivedAtEmbassy = 12,
        [LocalizedDescription(nameof(EnumResources.SentToVACFromEmbassy))] 
        SentToVACFromEmbassy = 13,
        [LocalizedDescription(nameof(EnumResources.ReceivedAtVAC))] // Equivalent of PassportsWaitingToBeDelivered in db
        ReceivedAtVAC = 14,
        [LocalizedDescription(nameof(EnumResources.DeliveredToApplicant))] // Equivalent of PassportsDelivered in db
        DeliveredToApplicant = 15,
        [LocalizedDescription(nameof(EnumResources.Rejection))] // Equivalent of NLL in db
        Rejection = 16,
        [LocalizedDescription(nameof(EnumResources.RejectionRefundDone))]
        RejectionRefundDone = 17,
        [LocalizedDescription(nameof(EnumResources.OutscanToCourrier))] // Equivalent of PassportsDelivered in db
        OutscanToCourrier = 18,
        [LocalizedDescription(nameof(EnumResources.PcrIsDone))]
        PcrIsDone = 19,
        [LocalizedDescription(nameof(EnumResources.PcrCanceled))]
        PcrCanceled = 20,
        [LocalizedDescription(nameof(EnumResources.ReBiometry))]
        ReBiometry = 21,
        [LocalizedDescription(nameof(EnumResources.BiometricEnrollmentDone))]
        BiometricEnrollmentDone = 22,
        [LocalizedDescription(nameof(EnumResources.HandDeliveredToApplicantAtEmbassy))] // Equivalent of PassportsDelivered in db
        HandDeliveredToApplicantAtEmbassy = 23,
        [LocalizedDescription(nameof(EnumResources.RejectionWithCountryEntryBanned))] // Equivalent of YGY-R in db
        RejectionWithCountryEntryBanned = 24,
        [LocalizedDescription(nameof(EnumResources.RejectedPassportDeliveredToCourier))] // Equivalent of PassportsDelivered in db
        RejectedPassportDeliveredToCourier = 25,
        [LocalizedDescription(nameof(EnumResources.IstizanRejection))] // Equivalent of IR in db
        IstizanRejection = 26,
        [LocalizedDescription(nameof(EnumResources.Cancelled))]
        CancelledByApplicant = 29,
        [LocalizedDescription(nameof(EnumResources.RecievedAtVisaCenter))]
        RecievedAtVisaCenter = 31,
        [LocalizedDescription(nameof(EnumResources.RecievedAtVacForKuwait))]
        RecievedAtVacForKuwait = 32,
        [LocalizedDescription(nameof(EnumResources.UnrealDocument))] // Equivalent of NLL (Unreal Document) in db
        UnrealDocument = 33,
        [LocalizedDescription(nameof(EnumResources.WaitingApproval))]
        WaitingApproval = 34,
        [LocalizedDescription(nameof(EnumResources.FileWithdrewAccordingtoCustomerRequest))]
        FileWithdrewAccordingtoCustomerRequest = 35,
        [LocalizedDescription(nameof(EnumResources.WaitingForIHB))]
        WaitingForIHB = 41,
        [LocalizedDescription(nameof(EnumResources.IHBUploaded))]
        IHBUploaded = 42,
        [LocalizedDescription(nameof(EnumResources.ReceivedAtOC))]
        ReceivedAtOC = 43,
        [LocalizedDescription(nameof(EnumResources.OutScanFromOCToVAC))]
        OutScanFromOCToVAC = 44,
        [LocalizedDescription(nameof(EnumResources.OutScanFromVACToOC))]
        OutScanFromVACToOC = 45,
        [LocalizedDescription(nameof(EnumResources.OutScanFromEmbassyToOC))]
        OutScanFromEmbassyToOC = 46,
        [LocalizedDescription(nameof(EnumResources.OutScanFromOCToEmbassy))]
        OutScanFromOCToEmbassy = 47,
        [LocalizedDescription(nameof(EnumResources.CourierReturnedPassportToOffice))]
        CourierReturnedPassportToOffice = 52,
    }

	public enum OldVisaDecisionType
	{
		[LocalizedDescription(nameof(EnumResources.Resen))]
		Resen = 1,
		[LocalizedDescription(nameof(EnumResources.Istizan))]
		Istizan = 2,
	}

	public enum Months
    {
        [LocalizedDescription(nameof(EnumResources.January))]
        January = 1,
        [LocalizedDescription(nameof(EnumResources.Febraury))]
        Febraury = 2,
        [LocalizedDescription(nameof(EnumResources.March))]
        March = 3,
        [LocalizedDescription(nameof(EnumResources.April))]
        April = 4,
        [LocalizedDescription(nameof(EnumResources.May))]
        May = 5,
        [LocalizedDescription(nameof(EnumResources.June))]
        June = 6,
        [LocalizedDescription(nameof(EnumResources.July))]
        July = 7,
        [LocalizedDescription(nameof(EnumResources.August))]
        August = 8,
        [LocalizedDescription(nameof(EnumResources.September))]
        September = 9,
        [LocalizedDescription(nameof(EnumResources.October))]
        October = 10,
        [LocalizedDescription(nameof(EnumResources.November))]
        November = 11,
        [LocalizedDescription(nameof(EnumResources.December))]
        December = 12
    }

    public enum IcrRelationType
    {
        Gateway = 0,
        Vfs = 1
    }

    public enum ApplicationFileType
    {
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeOther))]
        Other = 0,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypePassport))]
        Passport = 1,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeFlightBooking))]
        FlightBooking = 2,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeHotelReservation))]
        HotelReservation = 3,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeIncome))]
        Income = 4,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeHealthInsurance))]
        HealthInsurance = 5,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeSupportDocument))]
        SupportDocument = 6,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeBiometricPhoto))]
        BiometricPhoto = 7,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeDamageEntryStamp))]
        DamageEntryStamp = 8,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeDamageMedicalReport))]
        DamageMedicalReport = 9,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeDamageExpenseBill))]
        DamageExpenseBill = 10,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeDamageStatement))]
        DamageStatement = 11,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeRejectionPassport))]
        RejectionPassport = 12,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeRejectionDataPage))]
        RejectionDataPage = 13,
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeRejectionReturnStatement))]
        RejectionReturnStatement = 14,
        [LocalizedDescription(nameof(EnumResources.ExtraFile))]
        ExtraFile = 15,
        [LocalizedDescription(nameof(EnumResources.IHB))]
        IHB = 16,
        [LocalizedDescription(nameof(EnumResources.Permit))]
        Permit = 17
    }

    public enum DigitalSignatureDocument : byte
    {
        [LocalizedDescription(nameof(EnumResources.TurkeyEntryInformationForm))]
        TurkeyEntryInformationForm = 21,
        [LocalizedDescription(nameof(EnumResources.CargoReceipt))]
        CargoReceipt = 22,
        [LocalizedDescription(nameof(EnumResources.GatewayExplicitConsent))]
        GatewayExplicitConsent = 23,
        [LocalizedDescription(nameof(EnumResources.EmaaExplicitConsent))]
        EmaaExplicitConsent = 24,
        [LocalizedDescription(nameof(EnumResources.ApplicationPage))]
        ApplicationPage = 25,
        [LocalizedDescription(nameof(EnumResources.AllItemsTaxedIcr))]
        AllItemsTaxedIcr = 26,
        [LocalizedDescription(nameof(EnumResources.ICR))]
        Icr = 27,
        [LocalizedDescription(nameof(EnumResources.TaxedICR))]
        TaxedICR = 28,
    }

    public enum DigitalSignatureDocumentLanguage : byte
    {
        [LocalizedDescription(nameof(EnumResources.English))]
        English = 1,
        [LocalizedDescription(nameof(EnumResources.French))]
        French = 2,
        [LocalizedDescription(nameof(EnumResources.Arabic))]
        Arabic = 3,
        [LocalizedDescription(nameof(EnumResources.Turkish))]
        Turkish = 4,
        [LocalizedDescription(nameof(EnumResources.Russian))]
        Russian = 5,
        [LocalizedDescription(nameof(EnumResources.Turkmen))]
        Turkmen = 6
    }

    public enum NotificationStatusType : byte
    {
        [LocalizedDescription(nameof(EnumResources.Draft))]
        Draft = 1,
        [LocalizedDescription(nameof(EnumResources.Sent))]
        Sent = 2,
        [LocalizedDescription(nameof(EnumResources.Scheduled))]
        Scheduled = 3,
        [LocalizedDescription(nameof(EnumResources.Deleted))]
        Deleted = 4
    }

    public enum MinorApplicantParentType : byte
    {
        Mother = 1,
        Father = 2,
        Mother_And_Father = 3,
        Legal_Guardian = 4
    }

    public enum BasicGuidelineType
    {
        [LocalizedDescription(nameof(EnumResources.ArabicFrenchEnglish))]
        ArabicFrenchEnglish = 1,
        [LocalizedDescription(nameof(EnumResources.KurdishArabic))]
        KurdishArabic = 2,
        [LocalizedDescription(nameof(EnumResources.ArabicEnglish))]
        ArabicEnglish = 3,
        [LocalizedDescription(nameof(EnumResources.TurkmenRussian))]
        TurkmenRussian = 4
    }

    public enum AgencyTypeFileType
    {
        [LocalizedDescription(nameof(EnumResources.AgencyTypeFileTypeCertificate))]
        Certificate = 1,
        [LocalizedDescription(nameof(EnumResources.AgencyTypeFileTypeOther))]
        Other = 2,
    }

    public enum AgencyUserFileType
    {
        [LocalizedDescription(nameof(EnumResources.AuthorizationDocument))]
        AuthorizationDocument = 1
    }

    public enum BranchApplicationCountryFileType
    {
        [LocalizedDescription(nameof(EnumResources.ApplicationFileTypeOther))]
        Other = 0,
        [LocalizedDescription(nameof(EnumResources.BranchApplicationCountryFileTypeDescription))]
        Description = 1,
        [LocalizedDescription(nameof(EnumResources.BranchApplicationCountryFileTypeRequirements))]
        Requirements = 2,
        [LocalizedDescription(nameof(EnumResources.BranchApplicationCountryFileTypeInformation))]
        Information = 3
    }

    public enum DepartmentStatus
    {
        [LocalizedDescription(nameof(EnumResources.Other))]
        Other = 0,
        [LocalizedDescription(nameof(EnumResources.Submission))]
        Submission = 1,
        [LocalizedDescription(nameof(EnumResources.Biometrics))]
        Biometrics = 2,
    }

    public enum DepartmentSubmission
    {
        Application = 1,
        VIP = 2,
        Application2 = 9
    }

    public enum AppointmentManagementControllers
    {
        [Description("PreApplication")]
        PreApplication = 1,
        [Description("InfoDesk")]
        InfoDesk = 2
    }

    public enum AppointmentManagementMenuItems
    {
        [Description("QMS")]
        QMS = 1
    }

    public enum ActivationStatusType
    {
        [LocalizedDescription(nameof(EnumResources.Active))]
        Active = 1,
        [LocalizedDescription(nameof(EnumResources.Pending))]
        Pending = 2,
        [LocalizedDescription(nameof(EnumResources.Declined))]
        Declined = 3,
        [LocalizedDescription(nameof(EnumResources.Passive))]
        Passive = 4
    }

    public enum VasType
    {
        [LocalizedDescription(nameof(EnumResources.PrimeTime))]
        PrimeTime = 1,
        [LocalizedDescription(nameof(EnumResources.VIP))]
        Vip = 2,
        [LocalizedDescription(nameof(EnumResources.Platinum))]
        Platinum = 3,
        [LocalizedDescription(nameof(EnumResources.MBS))]
        Mbs = 4,
        [LocalizedDescription(nameof(EnumResources.DocumentEditingService))]
        DocumentEditingService = 5,
        [LocalizedDescription(nameof(EnumResources.FlexibleAppointment))]
        FlexibleAppointment = 6,
        [LocalizedDescription(nameof(EnumResources.Cfh))]
        Cfh = 8
    }

    public enum VasTypeFilter
    {
        [LocalizedDescription(nameof(EnumResources.PrimeTime))]
        PrimeTime = 1,
        [LocalizedDescription(nameof(EnumResources.VIP))]
        Vip = 2,
        [LocalizedDescription(nameof(EnumResources.Platinum))]
        Platinum = 3,
        [LocalizedDescription(nameof(EnumResources.MBS))]
        Mbs = 4,
        [LocalizedDescription(nameof(EnumResources.DocumentEditing))]
        DocumentEditing = 5,
        [LocalizedDescription(nameof(EnumResources.FlexibleAppointment))]
        FlexibleAppointment = 6,
        [LocalizedDescription(nameof(EnumResources.NormalApplications))]
        NormalApplication = 7,
        [LocalizedDescription(nameof(EnumResources.Cfh))]
        Cfh = 8
    }
    public enum CommissionType
    {
        [LocalizedDescription(nameof(EnumResources.Sale))]
        Sale = 1,
        [LocalizedDescription(nameof(EnumResources.Purchase))]
        Purchase = 2
    }

    public enum DocumentType //DMS
    {
        Application = 1,
        BranchApplicationCountry = 2,
        Agency = 3,
        Ministry = 4,
        AgencyUser = 5,
        PreApplicationApplicant = 6
    }

    public enum PasswordChallengeStatus
    {
        Valid = 1,
        NewPasswordRequired = 2,
        PasswordForgotten = 3
    }

    public enum TLVFormatQRCodeCountries
    {
        SaudiArabia = 152
    }

    public enum BankType
    {
        [LocalizedDescription(nameof(EnumResources.Garanti))]
        Garanti = 1,
        [LocalizedDescription(nameof(EnumResources.Asseco))]
        Asseco = 2
    }

    public enum InventoryType
    {
        [LocalizedDescription(nameof(EnumResources.FingerprintReader))]
        FingerprintReader = 1,
        [LocalizedDescription(nameof(EnumResources.Camera))]
        Camera = 2,
        [LocalizedDescription(nameof(EnumResources.Scanner))]
        Scanner = 3,
        [LocalizedDescription(nameof(EnumResources.ApplicationService))]
        ApplicationService = 4,
        [LocalizedDescription(nameof(EnumResources.SignaturePad))]
        SignaturePad = 5,
        [LocalizedDescription(nameof(EnumResources.FaceAnalysisService))]
        FaceAnalysisService = 6,
        [LocalizedDescription(nameof(EnumResources.BiomeTRLightCameraMotion))]
        BiomeTRLightCameraMotion = 7,
        [LocalizedDescription(nameof(EnumResources.Other))]
        Other = 8
    }

    public enum InventoryStatus
    {
        [LocalizedDescription(nameof(EnumResources.Active))]
        Active = 1,
        [LocalizedDescription(nameof(EnumResources.Passive))]
        Passive = 2,
        [LocalizedDescription(nameof(EnumResources.Faulty))]
        Faulty = 3,
        [LocalizedDescription(nameof(EnumResources.Repairing))]
        Repairing = 4
    }

    public enum QMSScreenType
    {
        [LocalizedDescription(nameof(EnumResources.CentralScreen))]
        CentralScreen = 1,
        [LocalizedDescription(nameof(EnumResources.SingleScreen))]
        SingleScreen = 2,
        [LocalizedDescription(nameof(EnumResources.HubScreen))]
        HubScreen = 3
    }

    public enum QMSContentType
    {
        [LocalizedDescription(nameof(EnumResources.Small))]
        Small = 1,
        [LocalizedDescription(nameof(EnumResources.Standard))]
        Standard = 2,
        [LocalizedDescription(nameof(EnumResources.Wide))]
        Wide = 3
    }

    public enum QMSLastHeartbeatTime : byte
    {
        [LocalizedDescription(nameof(EnumResources.Online))]
        Online = 1,
        [LocalizedDescription(nameof(EnumResources.Offline))]
        Offline = 2
    }

    public enum QMSWhiteListBiometricData
    {
        [LocalizedDescription(nameof(EnumResources.Exempt))]
        Exempt = 1,
        [LocalizedDescription(nameof(EnumResources.NotExempt))]
        NotExempt = 2
    }

    public enum QMSWhiteListDocumentExemption
    {
        [LocalizedDescription(nameof(EnumResources.Exempt))]
        Exempt = 1,
        [LocalizedDescription(nameof(EnumResources.BasicDocument))]
        BasicDocument = 2,
        [LocalizedDescription(nameof(EnumResources.Other))]
        Other = 3
    }

    public enum YesNoQuestion
    {
        [LocalizedDescription(nameof(EnumResources.Yes))]
        Yes = 1,
        [LocalizedDescription(nameof(EnumResources.No))]
        No = 2,
        [LocalizedDescription(nameof(EnumResources.Unspecified))]
        Unspecified = 3
    }

    public enum BranchInsuranceRefundSettingType
    {
        [LocalizedDescription(nameof(EnumResources.NoRestrictionPolicyRefund))]
        NoRestrictionPolicyRefund = 1,
        [LocalizedDescription(nameof(EnumResources.ThreeMonthInsurance))]
        ThreeMonthInsurance = 2,
        [LocalizedDescription(nameof(EnumResources.SixMonthInsurance))]
        SixMonthInsurance = 3,
        [LocalizedDescription(nameof(EnumResources.OneYearInsurance))]
        OneYearInsurance = 4
    }

    #region PerformanceManagementSystem

    public enum PMSDataGroup
    {
        [LocalizedDescription(nameof(EnumResources.Operation))]
        Operation = 1,
        [LocalizedDescription(nameof(EnumResources.Vas))]
        Vas = 2,
        [LocalizedDescription(nameof(EnumResources.Mistakes))]
        Mistakes = 3,
        [LocalizedDescription(nameof(EnumResources.Warnings))]
        Warnings = 4,
        [LocalizedDescription(nameof(EnumResources.ThanksComplaints))]
        ThanksComplaints = 5,
        [LocalizedDescription(nameof(EnumResources.ManagerEvaluation))]
        ManagerEvaluation = 6
    }

    public enum PMSOperationDataType
    {
        [LocalizedDescription(nameof(EnumResources.Submission))]
        ApplicationTaken = 1,
        [LocalizedDescription(nameof(EnumResources.Data))]
        DataDone = 2,
        [LocalizedDescription(nameof(EnumResources.Scan))]
        ReceivedAtEmbassy = 3,
        [LocalizedDescription(nameof(EnumResources.PassportDelivery))]
        PassportDelivery = 4,
        [LocalizedDescription(nameof(EnumResources.Biometri))]
        Biometri = 5,
        [LocalizedDescription(nameof(EnumResources.Info))]
        Info = 6,
        [LocalizedDescription(nameof(EnumResources.Redata))]
        Redata = 7,
        [LocalizedDescription(nameof(EnumResources.Multitask))]
        Multitask = 8,
        [LocalizedDescription(nameof(EnumResources.Check))]
        Check = 9,
        [LocalizedDescription(nameof(EnumResources.Cashier))]
        Cashier = 10
    }

    public enum PMSMistakesDataType
    {
        [LocalizedDescription(nameof(EnumResources.Signature))]
        Signature = 1,
        [LocalizedDescription(nameof(EnumResources.MissingDocument))]
        MissingDocument = 2,
        [LocalizedDescription(nameof(EnumResources.MissingInformation))]
        MissingInformation = 3,
        [LocalizedDescription(nameof(EnumResources.WrongVisa))]
        WrongVisa = 4,
        [LocalizedDescription(nameof(EnumResources.Redata))]
        Redata = 5,
        [LocalizedDescription(nameof(EnumResources.Other))]
        Other = 6
    }

    public enum PMSVASDataType
    {
        [LocalizedDescription(nameof(EnumResources.VIP))]
        VIP = 1,
        [LocalizedDescription(nameof(EnumResources.Photocopy))]
        Photocopy = 2,
        [LocalizedDescription(nameof(EnumResources.PrintOut))]
        PrintOut = 3,
        [LocalizedDescription(nameof(EnumResources.FormFilling))]
        FormFilling = 4,
        [LocalizedDescription(nameof(EnumResources.eSim8))]
        eSim8 = 5,
        [LocalizedDescription(nameof(EnumResources.eSim20))]
        eSim20 = 6,
        [LocalizedDescription(nameof(EnumResources.eSim50))]
        eSim50 = 7,
        [LocalizedDescription(nameof(EnumResources.Photo))]
        Photo = 8,
        [LocalizedDescription(nameof(EnumResources.Courier))]
        Courier = 9,
        [LocalizedDescription(nameof(EnumResources.MobilBiometrics))]
        MobilBiometrics = 10,
        [LocalizedDescription(nameof(EnumResources.RelatedInsurance))]
        RelatedInsurance = 11
    }

    public enum PMSWarningsDataType
    {
        [LocalizedDescription(nameof(EnumResources.WrittenWarning))]
        WrittenWarning = 1,
        [LocalizedDescription(nameof(EnumResources.VerbalWarning))]
        VerbalWarning = 2
    }

    public enum PMSThanksComplaintsDataType
    {
        [LocalizedDescription(nameof(EnumResources.ThanksByEmbassyConsulate))]
        ThanksByEmbassyConsulate = 1,
        [LocalizedDescription(nameof(EnumResources.ThanksByCustomer))]
        ThanksByCustomer = 2,
        [LocalizedDescription(nameof(EnumResources.ComplaintsByEmbassyConsulate))]
        ComplaintsByEmbassyConsulate = 3,
        [LocalizedDescription(nameof(EnumResources.ComplaintsByCustomer))]
        ComplaintsByCustomer = 4
    }

    public enum PMSManagerEvaluationDataType
    {
        [LocalizedDescription(nameof(EnumResources.EvaluationScore))]
        EvaluationScore = 1
    }
    #endregion

    #region Dashboard

    public enum DashboardRangeTypes
    {
        Daily = 1,
        Weekly = 2,
        Monthly = 3,
        Quarter = 4
    }

    public enum DashboardTypes
    {
        [Description("Chairman")]
        Chairman = 1,
        [Description("OfficeManager")]
        OfficeManager = 2,
        [Description("Officer")]
        Officer = 3,
        [Description("Consular")]
        Consular = 4,
        [Description("Ministry")]
        Ministry = 5
    }

    public enum DashboardApplicationChangeTypes
    {
        [LocalizedDescription(nameof(EnumResources.TypeChanged))]
        TypeChanged = 1,
        [LocalizedDescription(nameof(EnumResources.Cancelled))]
        Cancelled = 2,
        [LocalizedDescription(nameof(EnumResources.Refunded))]
        Refunded = 3
    }

    // TODO: AZALTILACAK
    public enum DashboardFromBeginningStatusType
    {
        [LocalizedDescription(nameof(EnumResources.Istizan))] // Equivalent of PendingApproval in db
        Istizan = 5,

        [LocalizedDescription(nameof(EnumResources.DeliveredToApplicant))] // Equivalent of PassportsDelivered in db
        DeliveredToApplicant = 15,
        [LocalizedDescription(nameof(EnumResources.RejectionRefundDone))] // Equivalent of PassportsDelivered in db && Rejection
        RejectionRefundDone = 17,
        [LocalizedDescription(nameof(EnumResources.OutscanToCourrier))] // Equivalent of PassportsDelivered in db
        OutscanToCourrier = 18,
        [LocalizedDescription(nameof(EnumResources.HandDeliveredToApplicantAtEmbassy))] // Equivalent of PassportsDelivered in db
        HandDeliveredToApplicantAtEmbassy = 23,
        [LocalizedDescription(nameof(EnumResources.RejectionWithCountryEntryBanned))] // Equivalent of Rejection in db
        RejectionWithCountryEntryBanned = 24,
        [LocalizedDescription(nameof(EnumResources.RejectedPassportDeliveredToCourier))] // Equivalent of PassportsDelivered in db && Rejection
        RejectedPassportDeliveredToCourier = 25,
    }

    #endregion

    #region Report

    public enum QmsReportType
    {
        [LocalizedDescription(nameof(EnumResources.ActionReport))]
        ActionReport = 1,
        [LocalizedDescription(nameof(EnumResources.QmsAllBranchesWhiteListReport))]
        AllBranchWhiteListReport = 2,
        [LocalizedDescription(nameof(EnumResources.QmsCompletedReport))]
        CompletedReport = 3,
        [LocalizedDescription(nameof(EnumResources.DepartmentReport))]
        DepartmentReport = 4,
        [LocalizedDescription(nameof(EnumResources.PersonalReport))]
        PersonnelReport = 5,
        [LocalizedDescription(nameof(EnumResources.QmsProcessTimeReport))]
        ProcessTimeReport = 6,
        [LocalizedDescription(nameof(EnumResources.QmsNotCompletedReport))]
        NotCompletedReport = 7,
        [LocalizedDescription(nameof(EnumResources.QmsNoShowReport))]
        NoShowReport = 8,
        [LocalizedDescription(nameof(EnumResources.QmsTatReport))]
        TatReport = 9,
        [LocalizedDescription(nameof(EnumResources.TokenReport))]
        TokenReport = 10,
        [LocalizedDescription(nameof(EnumResources.QmsVIPReport))]
        QmsVIPReport = 11,
        [LocalizedDescription(nameof(EnumResources.QmsReport))]
        QmsReport = 12
    }

    public enum ReportType
    {
        [LocalizedDescription(nameof(EnumResources.ReportTypeAllStaffApplicationsByBranch))]
        AllStaffApplicationsByBranch = 1,
        [LocalizedDescription(nameof(EnumResources.ReportTypeStaffExtraFeeSales))]
        StaffExtraFeeSales = 2,
        [LocalizedDescription(nameof(EnumResources.ReportTypeScanCycle))]
        ScanCycle = 3,
        [LocalizedDescription(nameof(EnumResources.ReportTypeAllApplications))]
        AllApplications = 4,
        [LocalizedDescription(nameof(EnumResources.ReportTypeDailyBalance))]
        DailyBalance = 5,
        [LocalizedDescription(nameof(EnumResources.ReportTypeDeletedApplications))]
        DeletedApplications = 6,
        [LocalizedDescription(nameof(EnumResources.ReportTypeFreeApplications))]
        FreeApplications = 7,
        [LocalizedDescription(nameof(EnumResources.ReportTypePartiallyRefundedApplications))]
        PartiallyRefundedApplications = 8,
        //[LocalizedDescription(nameof(EnumResources.ReportTypePcrStatus))]
        //PcrStatus = 9,
        //[LocalizedDescription(nameof(EnumResources.ReportTypePcrDaily))]
        //PcrDaily = 10,
        [LocalizedDescription(nameof(EnumResources.ReportTypePhotobooth))]
        Photobooth = 11,
        [LocalizedDescription(nameof(EnumResources.ReportTypeInsuranceApplications))]
        InsuranceApplications = 12,
        [LocalizedDescription(nameof(EnumResources.ReportTypeInsurance))]
        Insurance = 13,
        //[LocalizedDescription(nameof(EnumResources.ReportTypeCancelledInsurance))]
        //CancelledInsurance = 14,
        [LocalizedDescription(nameof(EnumResources.ReportCashReport_1))]
        CashReport_1 = 15,
        [LocalizedDescription(nameof(EnumResources.ReportCashReport_2))]
        CashReport_2 = 16,
        [LocalizedDescription(nameof(EnumResources.ReportNonApplicationInsurance))]
        NonApplicationInsurance = 17,
        //[LocalizedDescription(nameof(EnumResources.ReportRejectionStatus))]
        //RejectionStatus = 18,
        [LocalizedDescription(nameof(EnumResources.ReportTypeDetail))]
        Detail = 19,
        [LocalizedDescription(nameof(EnumResources.ReportDeliveredToCargo))]
        DeliveredToCargo = 20,
        [LocalizedDescription(nameof(EnumResources.ReportCargo))]
        Cargo = 21,
        [LocalizedDescription(nameof(EnumResources.ReportCancelCompletedApplications))]
        CancelCompletedApplications = 22,
        [LocalizedDescription(nameof(EnumResources.ReportExtraFees))]
        ExtraFees = 23,
        //[LocalizedDescription(nameof(EnumResources.ReportPCRCancellation))]
        //PCRCancellation = 24,
        [LocalizedDescription(nameof(EnumResources.ReportInsuranceCancellation))]
        InsuranceCancellation = 26,
        //[LocalizedDescription(nameof(EnumResources.ReportVisaRejection))]
        //VisaRejection = 27,
        [LocalizedDescription(nameof(EnumResources.ReportAllBranchesInsurance))]
        AllBranchesInsurance = 28,
        [LocalizedDescription(nameof(EnumResources.ReportAllBranchesDailyInsurance))]
        AllBranchesDailyInsurance = 29,
        //[LocalizedDescription(nameof(EnumResources.ReportPCRPayment))]
        //PCRPayment = 30,
        //[LocalizedDescription(nameof(EnumResources.ReportPCRGeneral))]
        //PCRGeneral = 31,
        //[LocalizedDescription(nameof(EnumResources.ReportInsuranceCancellationForRefund))]
        //InsuranceCancellationForRefund = 32,
        [LocalizedDescription(nameof(EnumResources.ReportInsuranceDetail))]
        InsuranceDetail = 33,
        [LocalizedDescription(nameof(EnumResources.UnInsuredApplications))]
        UnInsuredApplications = 34,
        [LocalizedDescription(nameof(EnumResources.ReportConsular))]
        Consular = 35,
        //[LocalizedDescription(nameof(EnumResources.ReportPCRCompanyPayment))]
        //PCRCompanyPayment = 36,
        [LocalizedDescription(nameof(EnumResources.ReportCargoCompanyPayment))]
        CargoCompanyPayment = 37,
        //[LocalizedDescription(nameof(EnumResources.ReportQMS))]
        //QMSReport = 38,
        //[LocalizedDescription(nameof(EnumResources.ReportQMSTimeline))]
        //QMSTimelineReport = 39,
        //[LocalizedDescription(nameof(EnumResources.ReportQMSPersonal))]
        //QMSPersonalReport = 40,
        [LocalizedDescription(nameof(EnumResources.ApplicationReportOfRejectedPassports))]
        ApplicationReportOfRejectedPassports = 41,
        [LocalizedDescription(nameof(EnumResources.VisaDecisionReport))]
        ApplicationVisaDecisionReport = 42,
        [LocalizedDescription(nameof(EnumResources.ReportOldCashReport_1))]
        OldCashReport_1 = 43,
        [LocalizedDescription(nameof(EnumResources.ReportOldCashReport_2))]
        OldCashReport_2 = 44,
        [LocalizedDescription(nameof(EnumResources.ReportIndiaCargo))]
        IndiaCargo = 45,
        [LocalizedDescription(nameof(EnumResources.ReportUpdateApplication))]
        UpdateApplication = 46,
        [LocalizedDescription(nameof(EnumResources.CountryBasedApplicationReport))]
        CountryBasedApplicationReport = 47,
        [LocalizedDescription(nameof(EnumResources.PostApplicationCommunicationReport))]
        PostApplicationCommunicationReport = 48,
        [LocalizedDescription(nameof(EnumResources.TurkmenistanConsulateReport))]
        TurkmenistanConsulateReport = 49,
        [LocalizedDescription(nameof(EnumResources.FinalStatusReport))]
        FinalStatusReport = 50,
        [LocalizedDescription(nameof(EnumResources.ApplicationDetailedVisaDecisionReport))]
        ApplicationDetailedVisaDecisionReport = 51,
        //[LocalizedDescription(nameof(EnumResources.ReportQMSDepartment))]
        //ReportQMSDepartment = 52,
        //[LocalizedDescription(nameof(EnumResources.ReportQMS))]
        //ReportQMS = 53,
        //[LocalizedDescription(nameof(EnumResources.ReportQmsAction))]
        //ReportQMSAction = 54,
        //[LocalizedDescription(nameof(EnumResources.ReportQMSPersonal))]
        //ReportQMSPersonal = 55,
        [LocalizedDescription(nameof(EnumResources.TurkmenistanApplicationStatisticsReport))]
        TurkmenistanApplicationStatisticsReport = 56,
        [LocalizedDescription(nameof(EnumResources.IncorrectApplicationStatusReport))]
        IncorrectApplicationStatusReport = 57,
        [LocalizedDescription(nameof(EnumResources.ForeignHealthInsuranceReport))]
        ForeignHealthInsuranceReport = 58,
        [LocalizedDescription(nameof(EnumResources.PendingFileInOfficeReport))]
        PendingFileInOfficeReport = 59,
		[LocalizedDescription(nameof(EnumResources.UpdateInsuranceReport))]
		UpdateInsuranceReport = 60,
        [LocalizedDescription(nameof(EnumResources.OutscanToEmbassyReport))]
        OutscanToEmbassy = 61,
        [LocalizedDescription(nameof(EnumResources.WorkPermitReferenceNumberReport))]
        WorkPermitReferenceNumberReport = 62,
        [LocalizedDescription(nameof(EnumResources.TrafficInsuranceReport))]
        TrafficInsuranceReport = 63,
        [LocalizedDescription(nameof(EnumResources.DeliveredToCourierReport))]
        DeliveredToCourierReport = 64,
        [LocalizedDescription(nameof(EnumResources.InsuranceDetailSingleSheet))]
        InsuranceDetailSingleSheet = 65,
        [LocalizedDescription(nameof(EnumResources.CourierCheckReport))]
        CourierCheckReport = 66,
        [LocalizedDescription(nameof(EnumResources.AllApplicationsSingleSheet))]
        AllApplicationsSingleSheet = 67,
        [LocalizedDescription(nameof(EnumResources.IndiaAllApplicationsReport))]
        IndiaAllApplications = 68,
        [LocalizedDescription(nameof(EnumResources.ReportUnifiedCashReport_1))]
        UnifiedCashReport_1 = 69,
        [LocalizedDescription(nameof(EnumResources.ReportUnifiedCashReport_2))]
        UnifiedCashReport_2 = 70,
        [LocalizedDescription(nameof(EnumResources.MediationReport))]
        MediationReport = 71,
        [LocalizedDescription(nameof(EnumResources.MailReportBefore))]
        MailReportBefore = 72,
        [LocalizedDescription(nameof(EnumResources.MailReportAfter))]
        MailReportAfter = 73,
        [LocalizedDescription(nameof(EnumResources.ReceivedAtEmbassyReport))]
		ReceivedAtEmbassyReport = 74,
        [LocalizedDescription(nameof(EnumResources.TurkmenistanStatisticReport))]
        TurkmenistanStatisticReport = 75,
		[LocalizedDescription(nameof(EnumResources.RejectedReport))]
		RejectedReport = 76,
		[LocalizedDescription(nameof(EnumResources.ReportCashReport_3))]
		CashReport_3 = 77,
        [LocalizedDescription(nameof(EnumResources.RelatedInsuranceReport))]
        RelatedInsurance = 78, 
        [LocalizedDescription(nameof(EnumResources.TurkmenistanApplicationTrackingReport))]
        TurkmenistanApplicationTrackingReport = 79,
        [LocalizedDescription(nameof(EnumResources.ReportFreeInsurance))]
        ReportFreeInsurance = 80,
        [LocalizedDescription(nameof(EnumResources.ReportMaidVisaCategory))]
        ReportMaidVisaCategory = 81
    }

    public enum ExternalReportType
    {
        [LocalizedDescription(nameof(EnumResources.ReportQMSDepartment))]
        ReportQMSDepartment = 52,
        [LocalizedDescription(nameof(EnumResources.ReportQMS))]
        ReportQMS = 53,
        [LocalizedDescription(nameof(EnumResources.ReportQmsAction))]
        ReportQMSAction = 54,
        [LocalizedDescription(nameof(EnumResources.ReportQMSPersonal))]
        ReportQMSPersonal = 55,
        [LocalizedDescription(nameof(EnumResources.DeliveredToCourierReport))]
        ReportCargoDeliveredToCourier = 64,
        [LocalizedDescription(nameof(EnumResources.CourierCheckReport))]
        CourierCheckReport = 66,

        [LocalizedDescription(nameof(EnumResources.AllApplicationsSingleSheet))]
        AllApplicationsSingleSheet = 67,
    }

    // Used for restricted authorized personal report page
    public enum NonAuthorizedReportType
    {
        [LocalizedDescription(nameof(EnumResources.ReportTypeAllStaffApplicationsByBranch))]
        AllStaffApplicationsByBranch = 1,
        [LocalizedDescription(nameof(EnumResources.ReportTypeStaffExtraFeeSales))]
        StaffExtraFeeSales = 2,
        [LocalizedDescription(nameof(EnumResources.ReportTypeScanCycle))]
        ScanCycle = 3,
        [LocalizedDescription(nameof(EnumResources.ReportTypeAllApplications))]
        AllApplications = 4,
        [LocalizedDescription(nameof(EnumResources.ReportTypeDailyBalance))]
        DailyBalance = 5,
        [LocalizedDescription(nameof(EnumResources.ReportTypeDeletedApplications))]
        DeletedApplications = 6,
        [LocalizedDescription(nameof(EnumResources.ReportTypeFreeApplications))]
        FreeApplications = 7,
        [LocalizedDescription(nameof(EnumResources.ReportTypePartiallyRefundedApplications))]
        PartiallyRefundedApplications = 8,
        //[LocalizedDescription(nameof(EnumResources.ReportTypePcrStatus))]
        //PcrStatus = 9,
        //[LocalizedDescription(nameof(EnumResources.ReportTypePcrDaily))]
        //PcrDaily = 10,
        [LocalizedDescription(nameof(EnumResources.ReportTypePhotobooth))]
        Photobooth = 11,
        [LocalizedDescription(nameof(EnumResources.ReportTypeInsuranceApplications))]
        InsuranceApplications = 12,
        [LocalizedDescription(nameof(EnumResources.ReportTypeInsurance))]
        Insurance = 13,
        [LocalizedDescription(nameof(EnumResources.ReportCashReport_1))]
        CashReport_1 = 15,
        [LocalizedDescription(nameof(EnumResources.ReportCashReport_2))]
        CashReport_2 = 16,
        [LocalizedDescription(nameof(EnumResources.ReportNonApplicationInsurance))]
        NonApplicationInsurance = 17,
        //[LocalizedDescription(nameof(EnumResources.ReportRejectionStatus))]
        //RejectionStatus = 18,
        [LocalizedDescription(nameof(EnumResources.ReportTypeDetail))]
        Detail = 19,
        [LocalizedDescription(nameof(EnumResources.ReportDeliveredToCargo))]
        DeliveredToCargo = 20,
        [LocalizedDescription(nameof(EnumResources.ReportCargo))]
        Cargo = 21,
        [LocalizedDescription(nameof(EnumResources.ReportCancelCompletedApplications))]
        CancelCompletedApplications = 22,
        [LocalizedDescription(nameof(EnumResources.ReportExtraFees))]
        ExtraFees = 23,
        //[LocalizedDescription(nameof(EnumResources.ReportPCRCancellation))]
        //PCRCancellation = 24,
        [LocalizedDescription(nameof(EnumResources.ReportInsuranceCancellation))]
        InsuranceCancellation = 26,
        //[LocalizedDescription(nameof(EnumResources.ReportVisaRejection))]
        //VisaRejection = 27,
        //[LocalizedDescription(nameof(EnumResources.ReportPCRGeneral))]
        //PCRGeneral = 31,
        //[LocalizedDescription(nameof(EnumResources.ReportInsuranceCancellationForRefund))]
        //InsuranceCancellationForRefund = 32,
        [LocalizedDescription(nameof(EnumResources.ReportInsuranceDetail))]
        InsuranceDetail = 33,
        [LocalizedDescription(nameof(EnumResources.UnInsuredApplications))]
        UnInsuredApplications = 34,
        [LocalizedDescription(nameof(EnumResources.ReportConsular))]
        Consular = 35,
        [LocalizedDescription(nameof(EnumResources.ApplicationReportOfRejectedPassports))]
        ApplicationReportOfRejectedPassports = 41,
        [LocalizedDescription(nameof(EnumResources.VisaDecisionReport))]
        ApplicationVisaDecisionReport = 42,
        //[LocalizedDescription(nameof(EnumResources.ReportOldCashReport_1))]
        //OldCashReport_1 = 43,
        //[LocalizedDescription(nameof(EnumResources.ReportOldCashReport_2))]
        //OldCashReport_2 = 44,
        [LocalizedDescription(nameof(EnumResources.ReportIndiaCargo))]
        IndiaCargo = 45,
        [LocalizedDescription(nameof(EnumResources.ReportUpdateApplication))]
        UpdateApplication = 46,
        [LocalizedDescription(nameof(EnumResources.PostApplicationCommunicationReport))]
        PostApplicationCommunicationReport = 48,
        [LocalizedDescription(nameof(EnumResources.TurkmenistanConsulateReport))]
        TurkmenistanConsulateReport = 49,
        [LocalizedDescription(nameof(EnumResources.FinalStatusReport))]
        FinalStatusReport = 50,
        [LocalizedDescription(nameof(EnumResources.ApplicationDetailedVisaDecisionReport))]
        ApplicationDetailedVisaDecisionReport = 51,
        //[LocalizedDescription(nameof(EnumResources.QmsPersonnelTracking))]
        //ReportQMSDepartment = 52,
        //[LocalizedDescription(nameof(EnumResources.ReportQMS))]
        //ReportQMS = 53,
        //[LocalizedDescription(nameof(EnumResources.ReportQmsAction))]
        //ReportQMSAction = 54,
        //[LocalizedDescription(nameof(EnumResources.ReportQMSPersonal))]
        //ReportQMSPersonal = 55,
        [LocalizedDescription(nameof(EnumResources.TurkmenistanApplicationStatisticsReport))]
        TurkmenistanApplicationStatisticsReport = 56,
        [LocalizedDescription(nameof(EnumResources.IncorrectApplicationStatusReport))]
        IncorrectApplicationStatusReport = 57,
        [LocalizedDescription(nameof(EnumResources.ForeignHealthInsuranceReport))]
        ForeignHealthInsuranceReport = 58,
        //[LocalizedDescription(nameof(EnumResources.PendingFileInOfficeReport))]
        //PendingFileInOfficeReport = 59,
        [LocalizedDescription(nameof(EnumResources.UpdateInsuranceReport))]
        UpdateInsuranceReport = 60,
        //[LocalizedDescription(nameof(EnumResources.OutscanToEmbassyReport))]
        //OutscanToEmbassy = 61,
        [LocalizedDescription(nameof(EnumResources.WorkPermitReferenceNumberReport))]
        WorkPermitReferenceNumberReport = 62,
        [LocalizedDescription(nameof(EnumResources.TrafficInsuranceReport))]
        TrafficInsuranceReport = 63,
        [LocalizedDescription(nameof(EnumResources.DeliveredToCourierReport))]
        DeliveredToCourierReport = 64,
        [LocalizedDescription(nameof(EnumResources.InsuranceDetailSingleSheet))]
        InsuranceDetailSingleSheet = 65,
        [LocalizedDescription(nameof(EnumResources.CourierCheckReport))]
        CourierCheckReport = 66,
        [LocalizedDescription(nameof(EnumResources.AllApplicationsSingleSheet))]
        AllApplicationsSingleSheet = 67,
        [LocalizedDescription(nameof(EnumResources.IndiaAllApplicationsReport))]
        IndiaAllApplications = 68,
        [LocalizedDescription(nameof(EnumResources.ReportCashReport_3))]
        CashReport_3 = 77,
        [LocalizedDescription(nameof(EnumResources.RelatedInsuranceReport))]
        RelatedInsurance = 78,
        [LocalizedDescription(nameof(EnumResources.TurkmenistanApplicationTrackingReport))]
        TurkmenistanApplicationTrackingReport = 79,
        [LocalizedDescription(nameof(EnumResources.ReportFreeInsurance))]
        ReportFreeInsurance = 80,
        [LocalizedDescription(nameof(EnumResources.ReportMaidVisaCategory))]
        ReportMaidVisaCategory = 81
    }

    public enum ConsularReportType
    {
        [LocalizedDescription(nameof(EnumResources.MediationReport))]
        MediationReport = 71,
        [LocalizedDescription(nameof(EnumResources.MailReportBefore))]
        MailReportBefore = 72,
        [LocalizedDescription(nameof(EnumResources.MailReportAfter))]
        MailReportAfter = 73,
        [LocalizedDescription(nameof(EnumResources.ReceivedAtEmbassyReport))]
		ReceivedAtEmbassyReport = 74,
        [LocalizedDescription(nameof(EnumResources.TurkmenistanStatisticReport))]
		TurkmenistanStatisticReport = 75,
		[LocalizedDescription(nameof(EnumResources.RejectedReport))]
		RejectedReport = 76
	}

    public enum CompanyExtraFeeReportType
    {
		[LocalizedDescription(nameof(EnumResources.ReportTypeInsuranceApplications))]
		InsuranceApplications = 12,
		[LocalizedDescription(nameof(EnumResources.ReportTypeInsurance))]
		Insurance = 13,
		[LocalizedDescription(nameof(EnumResources.ReportNonApplicationInsurance))]
		NonApplicationInsurance = 17,
		[LocalizedDescription(nameof(EnumResources.ReportInsuranceCancellation))]
		InsuranceCancellation = 26,
		[LocalizedDescription(nameof(EnumResources.ReportInsuranceDetail))]
		InsuranceDetail = 33,
		[LocalizedDescription(nameof(EnumResources.UpdateInsuranceReport))]
		UpdateInsuranceReport = 60,
		[LocalizedDescription(nameof(EnumResources.InsuranceDetailSingleSheet))]
		InsuranceDetailSingleSheet = 65,
	}

    public enum CompanyExtraFeeInsuranceProviderCompany
    {
        [LocalizedDescription(nameof(EnumResources.EmaaInsurance))]
        EmaaInsurance = 1
    }

    /// <summary>
    /// Used in Report #19 Detail Report for "YAŞ ARALIĞINA GÖRE"	
    /// </summary>
    public enum ReportAgeRanges
    {
        [LocalizedDescription(nameof(EnumResources.Zero_Fifteen))]
        Zero_Fifteen = 15,
        [LocalizedDescription(nameof(EnumResources.Fifteen_TwentyFive))]
        Fifteen_TwentyFive = 25,
        [LocalizedDescription(nameof(EnumResources.TwentyFive_Fifty))]
        TwentyFive_Fifty = 50,
        [LocalizedDescription(nameof(EnumResources.MoreThanFifty))]
        MoreThanFifty = 51
    }

    /// <summary>
    /// Used in Report #19 Detail Report for "YAŞ ARALIĞINA GÖRE"	
    /// </summary>
    public enum ReportVisaExemptApplicationAgeRanges
    {
        [LocalizedDescription(nameof(EnumResources.Zero_Fifteen))]
        Zero_Fifteen = 15,
        [LocalizedDescription(nameof(EnumResources.Zero_Sixteen))]
        Zero_Sixteen = 16,
        [LocalizedDescription(nameof(EnumResources.Zero_Twenty))]
        Zero_Twenty = 20,
        [LocalizedDescription(nameof(EnumResources.MoreThanFourtyFive))]
        MoreThanFourtyFive = 46, 
        [LocalizedDescription(nameof(EnumResources.MoreThanFifty))]
        MoreThanFifty = 51,
        [LocalizedDescription(nameof(EnumResources.MoreThanSixtyFive))]
        MoreThanSixtyFive = 65,
    }

    /// <summary>
    /// Used in Report #19 Detail Report for "BAŞVURU TÜRÜNE GÖRE"		
    /// </summary>
    public enum ReportApplicationProcessTypes
    {
        [LocalizedDescription(nameof(EnumResources.VIP))] // Contains VIP in ExtraFee Translations --ExtraFeeCategoryType: Other=1
        VIP = 1,
        [LocalizedDescription(nameof(EnumResources.PrimeTime))] // Contains VIP in ExtraFee Translations --ExtraFeeCategoryType: Other=1
        PrimeTime = 2,
        [LocalizedDescription(nameof(EnumResources.Normal))] // Application Type can be used but in order make equal with Application count, if not 1 & 2 is implemented
        Normal = 3,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquois))]
        Turquois = 5,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquoisPremium))]
        TurquoisPremium = 6,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquoisGratis))]
        TurquoisGratis = 7,
        [LocalizedDescription(nameof(EnumResources.VisaServiceFromHome))]
        VisaServiceFromHome = 8,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquoisWithGratisNote))]
        TurquoisWithGratisNote = 9
    }

    /// <summary>
    /// Used in Report #19 Detail Report for "ÜCRETSİZ BAŞVURU"				
    /// </summary>
    public enum ReportFreeApplicationType
    {
        [LocalizedDescription(nameof(EnumResources.FreeGWServiceFee))] // From Extra Fee --ExtraFeeCategoryType: ServiceFee=6
        FreeServiceFee = 1,
        [LocalizedDescription(nameof(EnumResources.FreeVisaFee))] // From Application Type --3
        FreeVisaFee = 2,
        [LocalizedDescription(nameof(EnumResources.NonApplicationInsurance))] // From Application Type --4
        NonApplicationInsurance = 3,
        [LocalizedDescription(nameof(EnumResources.NonApplicationPCR))] // From Application Type --5
        NonApplicationPCR = 4,
        [LocalizedDescription(nameof(EnumResources.DocumentEditing))] // From Application Type --6
        DocumentEditing = 5,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPrintOut))]
        NonApplicationPrintOut = 6,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPhotocopy))]
        NonApplicationPhotocopy = 7,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPhotograph))]
        NonApplicationPhotograph = 8,
        [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplication))]
        NonApplication = 11,
        [LocalizedDescription(nameof(EnumResources.YSS))]
        YSS = 12,
        [LocalizedDescription(nameof(EnumResources.TS))]
        TS = 13,
        [LocalizedDescription(nameof(EnumResources.NonApplicationRelatedInsurance))]
        NonApplicationRelatedInsurance = 14,
        [LocalizedDescription(nameof(EnumResources.NonApplicationRelatedInsuranceTotalApplication))]
        NonApplicationRelatedInsuranceTotalApplication = 15,
    }

    /// <summary>
    /// Used in Report #24 "PCR İPTAL RAPORU"	
    /// Application status' are static in test db & seed data
    /// </summary>
    public enum ReportPCRStatusType
    {
        [LocalizedDescription(nameof(EnumResources.PCRTestDone))]
        PCRTestDone = 19,
        [LocalizedDescription(nameof(EnumResources.PCRTestCancelled))]
        PCRTestCanceled = 20
    }

    /// <summary>
    /// Used in Report #26 "SİGORTA İPTAL RAPORU"	
    /// Application status' are static in test db & seed data
    /// Used in other reports as well ...
    /// </summary>
    public enum ReportCancellationDateType
    {
        [LocalizedDescription(nameof(EnumResources.SameDayCancellation))]
        SameDayCancellation = 1,
        [LocalizedDescription(nameof(EnumResources.RetrospectiveCancellation))]
        RetrospectiveCancellation = 2
    }

    /// <summary>
    /// TODO: the Enums from ExtraFeeCategoryType
    /// </summary>
    public enum ReportExtraFeeType
    {
        Insurance = 2,
        Pcr = 3,
        VisaFee = 4,
        ServiceFee = 6
    }

    /// <summary>
    /// Used in Report #18 "RET İADE RAPORU"	
    /// Indicates whether refund is done or int still pending status
    /// </summary>
    public enum ReportRefundType
    {
        [LocalizedDescription(nameof(EnumResources.Pending))]
        Pending = 1,
        [LocalizedDescription(nameof(EnumResources.RejectionRefundDone))]
        RejectionRefundDone = 2
    }

    /// <summary>
    /// Used in Report #31 "PCR GENEL RAPORU"	
    /// </summary>
    public enum ReportPCRGeneralStatusType
    {
        [LocalizedDescription(nameof(EnumResources.Pending))]
        Pending = 1,
        [LocalizedDescription(nameof(EnumResources.PCRTestDone))]
        PCRTestDone = 2,
        [LocalizedDescription(nameof(EnumResources.Refund))]
        Refund = 3
    }

    public enum ReportAppointmentStatus
    {
        [LocalizedDescription(nameof(EnumResources.ApplicationDone))]
        ApplicationDone = 1,
        [LocalizedDescription(nameof(EnumResources.ApplicationNotReceived))]
        ApplicationNotReceived = 2,
        [LocalizedDescription(nameof(EnumResources.Postponed))]
        Postponed = 3,
        [LocalizedDescription(nameof(EnumResources.TokenNotCreated))]
        TokenNotCreated = 4
    }

    public enum ReportDepartment
    {
        Application = 1,
        VIP = 2,
        Cashier = 3,
        Biometrics = 4,
        VIPCashier = 5,
        VIPBiometrics = 6
    }

    #endregion

    #region Data

    public enum DataStatusType
    {
        [LocalizedDescription(nameof(EnumResources.WaittingToBeCompletedData))]
        WaittingToBeCompletedData = 1,
        [LocalizedDescription(nameof(EnumResources.WaittingToBeSentToQualityCheck))]
        WaittingToBeSentToQualityCheck = 2,
        [LocalizedDescription(nameof(EnumResources.WaittingForQualityCheck))]
        WaittingForQualityCheck = 3,
        [LocalizedDescription(nameof(EnumResources.InQualityCheck))]
        InQualityCheck = 4,
        [LocalizedDescription(nameof(EnumResources.DoneQualityCheck))]
        DoneQualityCheck = 5,
        [LocalizedDescription(nameof(EnumResources.FailedQualityCheck))]
        FailedQualityCheck = 6
    }

    public enum MeansOfTransportType
    {
        [LocalizedDescription(nameof(EnumResources.Land))]
        Land = 1,
        [LocalizedDescription(nameof(EnumResources.Air))]
        Air = 2,
        [LocalizedDescription(nameof(EnumResources.Sea))]
        Sea = 3,
        [LocalizedDescription(nameof(EnumResources.Train))]
        Train = 4
    }

    public enum ExpensesCoverByType
    {
        [LocalizedDescription(nameof(EnumResources.Myself))]
        Myself = 1,
        [LocalizedDescription(nameof(EnumResources.HostPerson))]
        HostPerson = 2,
        [LocalizedDescription(nameof(EnumResources.HostComapny))]
        HostComapny = 3
    }

    public enum TravelDocumentType
    {
        [TravelDocumentCategory(category: new TravelDocumentCategoryType[5]
        {
            TravelDocumentCategoryType.TouristBusinessperson,
            TravelDocumentCategoryType.OfficialVisa,
            TravelDocumentCategoryType.StudentEducationVisa,
            TravelDocumentCategoryType.WorkVisa,
            TravelDocumentCategoryType.OtherVisas
        })]
        [LocalizedDescription(nameof(EnumResources.OrdinaryPassport))]
        OrdinaryPassport = 1,

        [TravelDocumentCategory(category: new TravelDocumentCategoryType[2]
        {
            TravelDocumentCategoryType.TouristBusinessperson,
            TravelDocumentCategoryType.OfficialVisa
        })]
        [LocalizedDescription(nameof(EnumResources.DiplomaticPassport))]
        DiplomaticPassport = 2,

        [TravelDocumentCategory(category: new TravelDocumentCategoryType[2]
        {
            TravelDocumentCategoryType.TouristBusinessperson,
            TravelDocumentCategoryType.OfficialVisa
        })]
        [LocalizedDescription(nameof(EnumResources.ServicePassport))]
        ServicePassport = 3,

        [TravelDocumentCategory(category: new TravelDocumentCategoryType[2]
        {
            TravelDocumentCategoryType.TouristBusinessperson,
            TravelDocumentCategoryType.OfficialVisa
        })]
        [LocalizedDescription(nameof(EnumResources.SpecialPassport))]
        SpecialPassport = 4,

        [TravelDocumentCategory(category: new TravelDocumentCategoryType[1]
        {
            TravelDocumentCategoryType.TouristBusinessperson
        })]
        [LocalizedDescription(nameof(EnumResources.RefugeePassport))]
        RefugeePassport = 5,

        [TravelDocumentCategory(category: new TravelDocumentCategoryType[1]
        {
            TravelDocumentCategoryType.TouristBusinessperson
        })]
        [LocalizedDescription(nameof(EnumResources.NansenPassport))]
        NansenPassport = 6,

        [TravelDocumentCategory(category: new TravelDocumentCategoryType[1]
        {
            TravelDocumentCategoryType.TouristBusinessperson
        })]
        [LocalizedDescription(nameof(EnumResources.AliensPassport))]
        AliensPassport = 7,

        [TravelDocumentCategory(category: new TravelDocumentCategoryType[5]
        {
            TravelDocumentCategoryType.TouristBusinessperson,
            TravelDocumentCategoryType.OfficialVisa,
            TravelDocumentCategoryType.StudentEducationVisa,
            TravelDocumentCategoryType.WorkVisa,
            TravelDocumentCategoryType.OtherVisas
        })]
        [LocalizedDescription(nameof(EnumResources.Other))]
        Other = 8
    }

    public enum TravelDocumentCategoryType
    {
        [TravelPurpose(travel: new TravelPurposeType[10]
        {
            TravelPurposeType.TouristicVisit,
            TravelPurposeType.SingleVisit,
            TravelPurposeType.DoubleTransit,
            TravelPurposeType.BusinessMeetingCommerce,
            TravelPurposeType.ConferenceSeminarMeeting,
            TravelPurposeType.FestivalFairExhibition,
            TravelPurposeType.SportiveActivity,
            TravelPurposeType.CulturalArtisticActivity,
            TravelPurposeType.OfficialVisit,
            TravelPurposeType.VisitToTurkishRepublicOfNorthernCyprus,
        })]
        [LocalizedDescription(nameof(EnumResources.TouristBusinessperson))]
        TouristBusinessperson = 1,

        [TravelPurpose(travel: new TravelPurposeType[2]
        {
            TravelPurposeType.AssignedForDuty,
            TravelPurposeType.Courier
        })]
        [LocalizedDescription(nameof(EnumResources.OfficialVisa))]
        OfficialVisa = 2,

        [TravelPurpose(travel: new TravelPurposeType[8]
        {
            TravelPurposeType.IntershipVisa,
            TravelPurposeType.IntershipErasmus,
            TravelPurposeType.InternshipAisec,
            TravelPurposeType.TurkishLanguageCoursePurpose,
            TravelPurposeType.EducationPurpose,
            TravelPurposeType.EducationInTurkishRepublicOfNorthernCyprus,
            TravelPurposeType.IntershipIaeste,
            TravelPurposeType.CoursePurpose
        })]
        [LocalizedDescription(nameof(EnumResources.StudentEducationVisa))]
        StudentEducationVisa = 3,

        [TravelPurpose(travel: new TravelPurposeType[7]
        {
            TravelPurposeType.EmployementPurposeSpecialEmploymentPurpose,
            TravelPurposeType.AssignedLecturersAcademics,
            TravelPurposeType.AssignedSportsperson,
            TravelPurposeType.AssignedArtists,
            TravelPurposeType.AssignedFreeZoneWorkers,
            TravelPurposeType.AssignedJournalist,
            TravelPurposeType.MontageAndRepairmentPurposes
        })]
        [LocalizedDescription(nameof(EnumResources.WorkVisa))]
        WorkVisa = 4,

        [TravelPurpose(travel: new TravelPurposeType[35]
        {
            TravelPurposeType.TouristicVisit,
            TravelPurposeType.SingleVisit,
            TravelPurposeType.DoubleTransit,
            TravelPurposeType.BusinessMeetingCommerce,
            TravelPurposeType.ConferenceSeminarMeeting,
            TravelPurposeType.FestivalFairExhibition,
            TravelPurposeType.SportiveActivity,
            TravelPurposeType.CulturalArtisticActivity,
            TravelPurposeType.OfficialVisit,
            TravelPurposeType.VisitToTurkishRepublicOfNorthernCyprus,
            TravelPurposeType.AssignedForDuty,
            TravelPurposeType.Courier,
            TravelPurposeType.IntershipVisa,
            TravelPurposeType.IntershipErasmus,
            TravelPurposeType.InternshipAisec,
            TravelPurposeType.TurkishLanguageCoursePurpose,
            TravelPurposeType.EducationPurpose,
            TravelPurposeType.EducationInTurkishRepublicOfNorthernCyprus,
            TravelPurposeType.IntershipIaeste,
            TravelPurposeType.EmployementPurposeSpecialEmploymentPurpose,
            TravelPurposeType.AssignedLecturersAcademics,
            TravelPurposeType.AssignedSportsperson,
            TravelPurposeType.AssignedArtists,
            TravelPurposeType.AssignedFreeZoneWorkers,
            TravelPurposeType.AssignedJournalist,
            TravelPurposeType.MontageAndRepairmentPurposes,
            TravelPurposeType.ArchaelogicalExcavation,
            TravelPurposeType.FilmingDocumentaryPurpose,
            TravelPurposeType.Sportive,
            TravelPurposeType.TourOperatorRepresentative,
            TravelPurposeType.MedicalTreatmentPurposes,
            TravelPurposeType.FamilyUnificationPurpose,
            TravelPurposeType.FreightVisa,
            TravelPurposeType.SeafarerVisa,
            TravelPurposeType.CoursePurpose
        })]
        [LocalizedDescription(nameof(EnumResources.Other))]
        OtherVisas = 5
    }

    public enum TravelPurposeType
    {
        [LocalizedDescription(nameof(EnumResources.TouristicVisit))]
        TouristicVisit = 1,
        [LocalizedDescription(nameof(EnumResources.SingleVisit))]
        SingleVisit = 2,
        [LocalizedDescription(nameof(EnumResources.DoubleTransit))]
        DoubleTransit = 3,
        [LocalizedDescription(nameof(EnumResources.BusinessMeetingCommerce))]
        BusinessMeetingCommerce = 4,
        [LocalizedDescription(nameof(EnumResources.ConferenceSeminarMeeting))]
        ConferenceSeminarMeeting = 5,
        [LocalizedDescription(nameof(EnumResources.FestivalFairExhibition))]
        FestivalFairExhibition = 6,
        [LocalizedDescription(nameof(EnumResources.SportiveActivity))]
        SportiveActivity = 7,
        [LocalizedDescription(nameof(EnumResources.CulturalArtisticActivity))]
        CulturalArtisticActivity = 8,
        [LocalizedDescription(nameof(EnumResources.OfficialVisit))]
        OfficialVisit = 9,
        [LocalizedDescription(nameof(EnumResources.VisitToTurkishRepublicOfNorthernCyprus))]
        VisitToTurkishRepublicOfNorthernCyprus = 10,
        [LocalizedDescription(nameof(EnumResources.AssignedForDuty))]
        AssignedForDuty = 11,
        [LocalizedDescription(nameof(EnumResources.Courier))]
        Courier = 12,
        [LocalizedDescription(nameof(EnumResources.IntershipVisa))]
        IntershipVisa = 13,
        [LocalizedDescription(nameof(EnumResources.IntershipErasmus))]
        IntershipErasmus = 14,
        [LocalizedDescription(nameof(EnumResources.InternshipAisec))]
        InternshipAisec = 15,
        [LocalizedDescription(nameof(EnumResources.TurkishLanguageCoursePurpose))]
        TurkishLanguageCoursePurpose = 16,
        [LocalizedDescription(nameof(EnumResources.EducationPurpose))]
        EducationPurpose = 17,
        [LocalizedDescription(nameof(EnumResources.EducationInTurkishRepublicOfNorthernCyprus))]
        EducationInTurkishRepublicOfNorthernCyprus = 18,
        [LocalizedDescription(nameof(EnumResources.IntershipIaeste))]
        IntershipIaeste = 19,
        [LocalizedDescription(nameof(EnumResources.EmployementPurposeSpecialEmploymentPurpose))]
        EmployementPurposeSpecialEmploymentPurpose = 20,
        [LocalizedDescription(nameof(EnumResources.AssignedLecturersAcademics))]
        AssignedLecturersAcademics = 21,
        [LocalizedDescription(nameof(EnumResources.AssignedSportsperson))]
        AssignedSportsperson = 22,
        [LocalizedDescription(nameof(EnumResources.AssignedArtists))]
        AssignedArtists = 23,
        [LocalizedDescription(nameof(EnumResources.AssignedFreeZoneWorkers))]
        AssignedFreeZoneWorkers = 24,
        [LocalizedDescription(nameof(EnumResources.AssignedJournalist))]
        AssignedJournalist = 25,
        [LocalizedDescription(nameof(EnumResources.MontageAndRepairmentPurposes))]
        MontageAndRepairmentPurposes = 26,
        [LocalizedDescription(nameof(EnumResources.ArchaelogicalExcavation))]
        ArchaelogicalExcavation = 27,
        [LocalizedDescription(nameof(EnumResources.FilmingDocumentaryPurpose))]
        FilmingDocumentaryPurpose = 28,
        [LocalizedDescription(nameof(EnumResources.Sportive))]
        Sportive = 29,
        [LocalizedDescription(nameof(EnumResources.TourOperatorRepresentative))]
        TourOperatorRepresentative = 30,
        [LocalizedDescription(nameof(EnumResources.MedicalTreatmentPurposes))]
        MedicalTreatmentPurposes = 31,
        [LocalizedDescription(nameof(EnumResources.FamilyUnificationPurpose))]
        FamilyUnificationPurpose = 32,
        [LocalizedDescription(nameof(EnumResources.FreightVisa))]
        FreightVisa = 33,
        [LocalizedDescription(nameof(EnumResources.SeafarerVisa))]
        SeafarerVisa = 34,
        [LocalizedDescription(nameof(EnumResources.CoursePurpose))]
        CoursePurpose = 35
    }

    public enum DataOccupationType
    {
        [LocalizedDescription(nameof(EnumResources.Agriculture))]
        Agriculture = 1,
        [LocalizedDescription(nameof(EnumResources.ArmedSecurityForce))]
        ArmedSecurityForce = 2,
        [LocalizedDescription(nameof(EnumResources.ArtistPerformer))]
        ArtistPerformer = 3,
        [LocalizedDescription(nameof(EnumResources.Business))]
        Business = 4,
        [LocalizedDescription(nameof(EnumResources.CaregiverAndBabysitter))]
        CaregiverAndBabysitter = 5,
        [LocalizedDescription(nameof(EnumResources.Construction))]
        Construction = 6,
        [LocalizedDescription(nameof(EnumResources.CulinaryCookery))]
        CulinaryCookery = 7,
        [LocalizedDescription(nameof(EnumResources.DriverLorry))]
        DriverLorry = 8,
        [LocalizedDescription(nameof(EnumResources.EducationAndTraining))]
        EducationAndTraining = 9,
        [LocalizedDescription(nameof(EnumResources.Engineer))]
        Engineer = 10,
        [LocalizedDescription(nameof(EnumResources.FinanceAndBanking))]
        FinanceAndBanking = 11,
        [LocalizedDescription(nameof(EnumResources.Government))]
        Government = 12,
        [LocalizedDescription(nameof(EnumResources.HealthMedical))]
        HealthMedical = 13,
        [LocalizedDescription(nameof(EnumResources.InformationTechnologies))]
        InformationTechnologies = 14,
        [LocalizedDescription(nameof(EnumResources.LegalProfessional))]
        LegalProfessional = 15,
        [LocalizedDescription(nameof(EnumResources.Other))]
        Other = 16,
        [LocalizedDescription(nameof(EnumResources.PressMedia))]
        PressMedia = 17,
        [LocalizedDescription(nameof(EnumResources.ProfessionalSportsperson))]
        ProfessionalSportsperson = 18,
        [LocalizedDescription(nameof(EnumResources.ReligiousFunctionary))]
        ReligiousFunctionary = 19,
        [LocalizedDescription(nameof(EnumResources.ResearcherScientist))]
        ResearcherScientist = 20,
        [LocalizedDescription(nameof(EnumResources.Retired))]
        Retired = 21,
        [LocalizedDescription(nameof(EnumResources.Seafarer))]
        Seafarer = 22,
        [LocalizedDescription(nameof(EnumResources.SelfEmployed))]
        SelfEmployed = 23,
        [LocalizedDescription(nameof(EnumResources.ServiceSector))]
        ServiceSector = 24,
        [LocalizedDescription(nameof(EnumResources.StudentTrainee))]
        StudentTrainee = 25,
        [LocalizedDescription(nameof(EnumResources.Tourism))]
        Tourism = 26,
        [LocalizedDescription(nameof(EnumResources.Unemployed))]
        Unemployed = 27
    }

    public enum DataTravelHistoryQuestionType
    {
        [LocalizedDescription(nameof(EnumResources.DataTravelHistoryQuestion1))]
        Question1 = 1,
        [LocalizedDescription(nameof(EnumResources.DataTravelHistoryQuestion2))]
        Question2 = 2,
        [LocalizedDescription(nameof(EnumResources.DataTravelHistoryQuestion3))]
        Question3 = 3,
        [LocalizedDescription(nameof(EnumResources.DataTravelHistoryQuestion4))]
        Question4 = 4,
        [LocalizedDescription(nameof(EnumResources.DataTravelHistoryQuestion5))]
        Question5 = 5,
        [LocalizedDescription(nameof(EnumResources.DataTravelHistoryQuestion6))]
        Question6 = 6,
        [LocalizedDescription(nameof(EnumResources.DataTravelHistoryQuestion7))]
        Question7 = 7,
        [LocalizedDescription(nameof(EnumResources.DataTravelHistoryQuestion8))]
        Question8 = 8,
        [LocalizedDescription(nameof(EnumResources.DataTravelHistoryQuestion9))]
        Question9 = 9,
    }

    public enum RevocationDocumentType
    {
        [LocalizedDescription(nameof(EnumResources.StickerVisa))]
        StickerVisa = 1,
        [LocalizedDescription(nameof(EnumResources.StampVisa))]
        StampVisa = 2,
        [LocalizedDescription(nameof(EnumResources.Evisa))]
        Evisa = 3,
        [LocalizedDescription(nameof(EnumResources.ResidencePermit))]
        ResidencePermit = 4,
        [LocalizedDescription(nameof(EnumResources.WorkPermit))]
        WorkPermit = 5
    }

    public enum DeportationInstitutionType
    {
        [LocalizedDescription(nameof(EnumResources.RepublicOfTurkeyDirectorateGeneralOfMigrationManagement))]
        RepublicOfTurkeyDirectorateGeneralOfMigrationManagement = 1,
        [LocalizedDescription(nameof(EnumResources.TurkishNationalPolice))]
        TurkishNationalPolice = 2
    }

    public enum RejectionInstitutionType
    {
        [LocalizedDescription(nameof(EnumResources.RepublicTurkeyMinistryCustomsTrade))]
        RepublicTurkeyMinistryCustomsTrade = 1,
        [LocalizedDescription(nameof(EnumResources.TurkishNationalPolice))]
        TurkishNationalPolice = 2
    }

    public enum DeportationReasonType
    {
        [LocalizedDescription(nameof(EnumResources.OverstayedVisit))]
        OverstayedVisit = 1,
        [LocalizedDescription(nameof(EnumResources.CommissionOfCrime))]
        CommissionOfCrime = 2,
        [LocalizedDescription(nameof(EnumResources.ViolationInternationalProtection))]
        ViolationInternationalProtection = 3,
        [LocalizedDescription(nameof(EnumResources.SecurityBreach))]
        SecurityBreach = 4,
        [LocalizedDescription(nameof(EnumResources.HealthPublicSafety))]
        HealthPublicSafety = 5,
        [LocalizedDescription(nameof(EnumResources.BreakingTravelDocuments))]
        BreakingTravelDocuments = 6,
        [LocalizedDescription(nameof(EnumResources.IllegalEntryCounrty))]
        IllegalEntryCounrty = 7,
        [LocalizedDescription(nameof(EnumResources.MisrepresentationFactInformation))]
        MisrepresentationFactInformation = 8,
        [LocalizedDescription(nameof(EnumResources.FaultyDocumentation))]
        FaultyDocumentation = 9,
        [LocalizedDescription(nameof(EnumResources.Other))]
        Other = 10,
        [LocalizedDescription(nameof(EnumResources.LosingLegalStatus))]
        LosingLegalStatus = 11
    }

    public enum RejectionType
    {
        [LocalizedDescription(nameof(EnumResources.MissingDocuments))]
        MissingDocuments = 1,
        [LocalizedDescription(nameof(EnumResources.CriminalRecords))]
        CriminalRecords = 2,
        [LocalizedDescription(nameof(EnumResources.SecurityReason))]
        SecurityReason = 3,
        [LocalizedDescription(nameof(EnumResources.HealthGround))]
        HealthGround = 4,
        [LocalizedDescription(nameof(EnumResources.HavingAndInadmissibleRelative))]
        HavingAndInadmissibleRelative = 5,
        [LocalizedDescription(nameof(EnumResources.MisrepresentationFalseInformation))]
        MisrepresentationFalseInformation = 6,
        [LocalizedDescription(nameof(EnumResources.Other))]
        Other = 7,
        [LocalizedDescription(nameof(EnumResources.LackTravelHealthInsurance))]
        LackTravelHealthInsurance = 8
    }

    public enum RefusingAuthorityType
    {
        [LocalizedDescription(nameof(EnumResources.TurkishMissions))]
        TurkishMissions = 1,
        [LocalizedDescription(nameof(EnumResources.RepublicTurkeyMinistryLaborSocialSecurity))]
        RepublicTurkeyMinistryLaborSocialSecurity = 2,
        [LocalizedDescription(nameof(EnumResources.TurkishBorderGates))]
        TurkishBorderGates = 3,
        [LocalizedDescription(nameof(EnumResources.TurkishNationalPolice))]
        TurkishNationalPolice = 4,
        [LocalizedDescription(nameof(EnumResources.RepublicTurkeyDirectorateGeneralMigrationManagement))]
        RepublicTurkeyDirectorateGeneralMigrationManagement = 5,
        [LocalizedDescription(nameof(EnumResources.RepublicTurkeyMinistryMinistryForeignAfffairs))]
        RepublicTurkeyMinistryMinistryForeignAfffairs = 6
    }

    public enum QualityCheckType
    {
        [QualityCheckCategory(category: new QualityCheckCategoryType[5]
        {
            QualityCheckCategoryType.DemographicInformation,
            QualityCheckCategoryType.TravelDate,
            QualityCheckCategoryType.VisaCategory,
            QualityCheckCategoryType.VisaType,
            QualityCheckCategoryType.DepartureDate
        })]
        [LocalizedDescription(nameof(EnumResources.QCDataControlAtoZ))]
        DataControlAtoZ = 1,

        [QualityCheckCategory(category: new QualityCheckCategoryType[3]
        {
            QualityCheckCategoryType.BackgroundWhite,
            QualityCheckCategoryType.SuitableForPhotoSize,
            QualityCheckCategoryType.PhotoCurrentDated
        })]
        [LocalizedDescription(nameof(EnumResources.QCPhotoCheck))]
        PhotoCheck = 2,

        [QualityCheckCategory(category: new QualityCheckCategoryType[5]
        {
            QualityCheckCategoryType.PasswordExpireDateValidadtion,
            QualityCheckCategoryType.PassportNotDamaged,
            QualityCheckCategoryType.PassportHasBlankPages,
            QualityCheckCategoryType.ExistingTurkeyVisaStatus,
            QualityCheckCategoryType.PassportStampValidity
        })]
        [LocalizedDescription(nameof(EnumResources.QCPassportControl))]
        PassportControl = 3,

        [QualityCheckCategory(category: new QualityCheckCategoryType[1]
        {
            QualityCheckCategoryType.ResidenceDurationValidity
        })]
        [LocalizedDescription(nameof(EnumResources.QCResidenceCheck))]
        ResidenceCheck = 4,

        [QualityCheckCategory(category: new QualityCheckCategoryType[1]
        {
            QualityCheckCategoryType.StandardValidity
        })]
        [LocalizedDescription(nameof(EnumResources.QCIdCheck))]
        IdCheck = 5,

        [QualityCheckCategory(category: new QualityCheckCategoryType[5]
        {
            QualityCheckCategoryType.InstitutionAddress,
            QualityCheckCategoryType.EmployerLetterValidty,
            QualityCheckCategoryType.OccupationAndSalaryInformation,
            QualityCheckCategoryType.AllowedDates,
            QualityCheckCategoryType.CompanyDocuments
        })]
        [LocalizedDescription(nameof(EnumResources.QCEmployerLetterControl))]
        EmployerLetterControl = 6,

        [QualityCheckCategory(category: new QualityCheckCategoryType[3]
        {
            QualityCheckCategoryType.LastSixMonths,
            QualityCheckCategoryType.BankDocumentValidity,
            QualityCheckCategoryType.BankBalance
        })]
        [LocalizedDescription(nameof(EnumResources.QCBankAccountStatementControl))]
        BankAccountStatementControl = 7,

        [QualityCheckCategory(category: new QualityCheckCategoryType[4]
        {
            QualityCheckCategoryType.FlightRoundTripReservation,
            QualityCheckCategoryType.DestinationCityInTurkey,
            QualityCheckCategoryType.FlightApplicantNameSurname,
            QualityCheckCategoryType.FlightMainDirection
        })]
        [LocalizedDescription(nameof(EnumResources.QCFlightTicketReservationControl))]
        FlightTicketReservationControl = 8,

        [QualityCheckCategory(category: new QualityCheckCategoryType[3]
        {
            QualityCheckCategoryType.HotelValidityForRoundTripFlight,
            QualityCheckCategoryType.HotelReservationInTurkey,
            QualityCheckCategoryType.HotelApplicantNameSurname
        })]
        [LocalizedDescription(nameof(EnumResources.QCHotelReservationControl))]
        HotelReservationControl = 9,

        [QualityCheckCategory(category: new QualityCheckCategoryType[5]
        {
            QualityCheckCategoryType.InsuranceValidityForRoundTripFlight,
            QualityCheckCategoryType.ArrangedForTurkeyOrWorldwide,
            QualityCheckCategoryType.ApplicantNameSurnameAndPassportValidity,
            QualityCheckCategoryType.FuneralIncluded,
            QualityCheckCategoryType.InsuranceMinumGuarantee
        })]
        [LocalizedDescription(nameof(EnumResources.QCTravelHealthInsuranceControl))]
        TravelHealthInsuranceControl = 10,

        [QualityCheckCategory(category: new QualityCheckCategoryType[4]
        {
            QualityCheckCategoryType.ConsentLetterValidity,
            QualityCheckCategoryType.PermissiveParentSigniture,
            QualityCheckCategoryType.CustodyOrDeathCertificate,
            QualityCheckCategoryType.TravelWithParent
        })]
        [LocalizedDescription(nameof(EnumResources.QCConsentLetterControl))]
        ConsentLetterControl = 11,

        [QualityCheckCategory(category: new QualityCheckCategoryType[5]
        {
            QualityCheckCategoryType.HospitalInvitationContactInformation,
            QualityCheckCategoryType.HospitalInvitationApplicantNameSurname,
            QualityCheckCategoryType.HospitalInvitationDocumentValidity,
            QualityCheckCategoryType.HospitalInvitationUpToDate,
            QualityCheckCategoryType.IllnessReports
        })]
        [LocalizedDescription(nameof(EnumResources.QCHospitalInvitationLetterControl))]
        HospitalInvitationLetterControl = 12,

        [QualityCheckCategory(category: new QualityCheckCategoryType[4]
        {
            QualityCheckCategoryType.BusinessInvitationContactInformation,
            QualityCheckCategoryType.BusinessInvitationApplicantNameSurname,
            QualityCheckCategoryType.BusinessInvitationDocumentValidity,
            QualityCheckCategoryType.BusinessInvitationUpToDate
        })]
        [LocalizedDescription(nameof(EnumResources.QCBusinessInvitationLetterControl))]
        BusinessInvitationLetterControl = 13,

        [QualityCheckCategory(category: new QualityCheckCategoryType[6]
        {
            QualityCheckCategoryType.SchoolAcceptanceContactInformation,
            QualityCheckCategoryType.SchoolAcceptanceApplicantNameSurname,
            QualityCheckCategoryType.SchoolAcceptanceDocumentValidity,
            QualityCheckCategoryType.SchoolAcceptanceLetterForCurrentDate,
            QualityCheckCategoryType.SchoolAcceptancepaymentOrScolarship,
            QualityCheckCategoryType.SchoolAcceptanceTranscript
        })]
        [LocalizedDescription(nameof(EnumResources.QCSchoolAcceptanceLetterControl))]
        SchoolAcceptanceLetterControl = 14,

        [QualityCheckCategory(category: new QualityCheckCategoryType[1]
        {
            QualityCheckCategoryType.AllDocumentsAreOriginal
        })]
        [LocalizedDescription(nameof(EnumResources.QCAllDocumentsAreOriginal))]
        AllDocumentsAreOriginal = 15
    }

    public enum QualityCheckCategoryType
    {
        [LocalizedDescription(nameof(EnumResources.QCDemographicInformation))]
        DemographicInformation = 1,
        [LocalizedDescription(nameof(EnumResources.QCTravelDate))]
        TravelDate = 2,
        [LocalizedDescription(nameof(EnumResources.QCVisaCategory))]
        VisaCategory = 3,
        [LocalizedDescription(nameof(EnumResources.QCVisaType))]
        VisaType = 4,
        [LocalizedDescription(nameof(EnumResources.QCDepartureDate))]
        DepartureDate = 5,

        [LocalizedDescription(nameof(EnumResources.QCBackgroundWhite))]
        BackgroundWhite = 6,
        [LocalizedDescription(nameof(EnumResources.QCSuitableForPhotoSize))]
        SuitableForPhotoSize = 7,
        [LocalizedDescription(nameof(EnumResources.QCPhotoCurrentDated))]
        PhotoCurrentDated = 8,

        [LocalizedDescription(nameof(EnumResources.QCPasswordExpireDateValidadtion))]
        PasswordExpireDateValidadtion = 9,
        [LocalizedDescription(nameof(EnumResources.QCPassportNotDamaged))]
        PassportNotDamaged = 10,
        [LocalizedDescription(nameof(EnumResources.QCPassportHasBlankPages))]
        PassportHasBlankPages = 11,
        [LocalizedDescription(nameof(EnumResources.QCExistingTurkeyVisaStatus))]
        ExistingTurkeyVisaStatus = 12,
        [LocalizedDescription(nameof(EnumResources.QCPassportStampValidity))]
        PassportStampValidity = 13,

        [LocalizedDescription(nameof(EnumResources.QCResidenceDurationValidity))]
        ResidenceDurationValidity = 14,

        [LocalizedDescription(nameof(EnumResources.QCStandardValidity))]
        StandardValidity = 15,

        [LocalizedDescription(nameof(EnumResources.QCInstitutionAddress))]
        InstitutionAddress = 16,
        [LocalizedDescription(nameof(EnumResources.QCEmployerLetterValidty))]
        EmployerLetterValidty = 17,
        [LocalizedDescription(nameof(EnumResources.QCOccupationAndSalaryInformation))]
        OccupationAndSalaryInformation = 18,
        [LocalizedDescription(nameof(EnumResources.QCAllowedDates))]
        AllowedDates = 19,
        [LocalizedDescription(nameof(EnumResources.QCCompanyDocuments))]
        CompanyDocuments = 20,

        [LocalizedDescription(nameof(EnumResources.QCLastSixMonths))]
        LastSixMonths = 21,
        [LocalizedDescription(nameof(EnumResources.QCBankDocumentValidity))]
        BankDocumentValidity = 22,
        [LocalizedDescription(nameof(EnumResources.QCBankBalance))]
        BankBalance = 23,

        [LocalizedDescription(nameof(EnumResources.QCFlightRoundTripReservation))]
        FlightRoundTripReservation = 24,
        [LocalizedDescription(nameof(EnumResources.QCDestinationCityInTurkey))]
        DestinationCityInTurkey = 25,
        [LocalizedDescription(nameof(EnumResources.QCFlightApplicantNameSurname))]
        FlightApplicantNameSurname = 26,
        [LocalizedDescription(nameof(EnumResources.QCFlightMainDirection))]
        FlightMainDirection = 27,

        [LocalizedDescription(nameof(EnumResources.QCHotelValidityForRoundTripFlight))]
        HotelValidityForRoundTripFlight = 28,
        [LocalizedDescription(nameof(EnumResources.QCHotelReservationInTurkey))]
        HotelReservationInTurkey = 29,
        [LocalizedDescription(nameof(EnumResources.QCHotelApplicantNameSurname))]
        HotelApplicantNameSurname = 30,

        [LocalizedDescription(nameof(EnumResources.QCInsuranceValidityForRoundTripFlight))]
        InsuranceValidityForRoundTripFlight = 31,
        [LocalizedDescription(nameof(EnumResources.QCArrangedForTurkeyOrWorldwide))]
        ArrangedForTurkeyOrWorldwide = 32,
        [LocalizedDescription(nameof(EnumResources.QCApplicantNameSurnameAndPassportValidity))]
        ApplicantNameSurnameAndPassportValidity = 33,
        [LocalizedDescription(nameof(EnumResources.QCFuneralIncluded))]
        FuneralIncluded = 34,
        [LocalizedDescription(nameof(EnumResources.QCInsuranceMinumGuarantee))]
        InsuranceMinumGuarantee = 35,

        [LocalizedDescription(nameof(EnumResources.QCConsentLetterValidity))]
        ConsentLetterValidity = 36,
        [LocalizedDescription(nameof(EnumResources.QCPermissiveParentSigniture))]
        PermissiveParentSigniture = 37,
        [LocalizedDescription(nameof(EnumResources.QCCustodyOrDeathCertificate))]
        CustodyOrDeathCertificate = 38,
        [LocalizedDescription(nameof(EnumResources.QCTravelWithParent))]
        TravelWithParent = 39,

        [LocalizedDescription(nameof(EnumResources.QCHospitalInvitationContactInformation))]
        HospitalInvitationContactInformation = 40,
        [LocalizedDescription(nameof(EnumResources.QCHospitalInvitationApplicantNameSurname))]
        HospitalInvitationApplicantNameSurname = 41,
        [LocalizedDescription(nameof(EnumResources.QCHospitalInvitationDocumentValidity))]
        HospitalInvitationDocumentValidity = 42,
        [LocalizedDescription(nameof(EnumResources.QCHospitalInvitationUpToDate))]
        HospitalInvitationUpToDate = 43,
        [LocalizedDescription(nameof(EnumResources.QCIllnessReports))]
        IllnessReports = 44,

        [LocalizedDescription(nameof(EnumResources.QCBusinessInvitationContactInformation))]
        BusinessInvitationContactInformation = 45,
        [LocalizedDescription(nameof(EnumResources.QCBusinessInvitationApplicantNameSurname))]
        BusinessInvitationApplicantNameSurname = 46,
        [LocalizedDescription(nameof(EnumResources.QCBusinessInvitationDocumentValidity))]
        BusinessInvitationDocumentValidity = 47,
        [LocalizedDescription(nameof(EnumResources.QCBusinessInvitationUpToDate))]
        BusinessInvitationUpToDate = 48,

        [LocalizedDescription(nameof(EnumResources.QCSchoolAcceptanceContactInformation))]
        SchoolAcceptanceContactInformation = 49,
        [LocalizedDescription(nameof(EnumResources.QCSchoolAcceptanceApplicantNameSurname))]
        SchoolAcceptanceApplicantNameSurname = 50,
        [LocalizedDescription(nameof(EnumResources.QCSchoolAcceptanceDocumentValidity))]
        SchoolAcceptanceDocumentValidity = 51,
        [LocalizedDescription(nameof(EnumResources.QCSchoolAcceptanceLetterForCurrentDate))]
        SchoolAcceptanceLetterForCurrentDate = 52,
        [LocalizedDescription(nameof(EnumResources.QCSchoolAcceptancepaymentOrScolarship))]
        SchoolAcceptancepaymentOrScolarship = 53,
        [LocalizedDescription(nameof(EnumResources.QCSchoolAcceptanceTranscript))]
        SchoolAcceptanceTranscript = 54,

        [LocalizedDescription(nameof(EnumResources.QCAllDocumentsAreOriginal))]
        AllDocumentsAreOriginal = 55,
    }

    public enum QualityCheckStatus
    {
        [LocalizedDescription(nameof(EnumResources.Pending))]
        Pending = 1,
        [LocalizedDescription(nameof(EnumResources.OnHold))]
        OnHold = 2,
        [LocalizedDescription(nameof(EnumResources.Sent))]
        Sent = 3,
        [LocalizedDescription(nameof(EnumResources.Rejected))]
        Rejected = 4,
    }

    public enum IhbStatus
    {
        [LocalizedDescription(nameof(EnumResources.Suitable))]
        Suitable = 1,
        [LocalizedDescription(nameof(EnumResources.NotSuitable))]
        NotSuitable = 2,
    }

    #endregion

    #region B2B

    public enum B2BVisaCategoryType
    {
        [VisaCategoryFile(category: new VisaCategoryFileType[6]
        {
            VisaCategoryFileType.Petition,
            VisaCategoryFileType.Declaration,
            VisaCategoryFileType.FlightTicket,
            VisaCategoryFileType.HotelReservation,
            VisaCategoryFileType.HealthInsurance,
            VisaCategoryFileType.PassportPhotocopy,
        })]
        [LocalizedDescription(nameof(EnumResources.Touristic))]
        Touristic = 1,

        [VisaCategoryFile(category: new VisaCategoryFileType[7]
        {
            VisaCategoryFileType.Petition,
            VisaCategoryFileType.Declaration,
            VisaCategoryFileType.FlightTicket,
            VisaCategoryFileType.HotelReservation,
            VisaCategoryFileType.HealthInsurance,
            VisaCategoryFileType.PassportPhotocopy,
            VisaCategoryFileType.Invitation
        })]
        [LocalizedDescription(nameof(EnumResources.Health))]
        Health = 2,

        [VisaCategoryFile(category: new VisaCategoryFileType[7]
        {
            VisaCategoryFileType.Petition,
            VisaCategoryFileType.Declaration,
            VisaCategoryFileType.FlightTicket,
            VisaCategoryFileType.HotelReservation,
            VisaCategoryFileType.HealthInsurance,
            VisaCategoryFileType.PassportPhotocopy,
            VisaCategoryFileType.Invitation
        })]
        [LocalizedDescription(nameof(EnumResources.Business))]
        Business = 4,

        [VisaCategoryFile(category: new VisaCategoryFileType[7]
        {
            VisaCategoryFileType.Petition,
            VisaCategoryFileType.Declaration,
            VisaCategoryFileType.FlightTicket,
            VisaCategoryFileType.HotelReservation,
            VisaCategoryFileType.HealthInsurance,
            VisaCategoryFileType.PassportPhotocopy,
            VisaCategoryFileType.Invitation
        })]
        [LocalizedDescription(nameof(EnumResources.CulturalSportive))]
        CulturalSportive = 5,

        [VisaCategoryFile(category: new VisaCategoryFileType[7]
        {
            VisaCategoryFileType.Petition,
            VisaCategoryFileType.Declaration,
            VisaCategoryFileType.FlightTicket,
            VisaCategoryFileType.HotelReservation,
            VisaCategoryFileType.HealthInsurance,
            VisaCategoryFileType.PassportPhotocopy,
            VisaCategoryFileType.LetterOfAcceptance
        })]
        [LocalizedDescription(nameof(EnumResources.Student))]
        Student = 6
    }

    public enum VisaCategoryFileType
    {
        [LocalizedDescription(nameof(EnumResources.Petition))]
        Petition = 1,
        [LocalizedDescription(nameof(EnumResources.Declaration))]
        Declaration = 2,
        [LocalizedDescription(nameof(EnumResources.FlightTicket))]
        FlightTicket = 3,
        [LocalizedDescription(nameof(EnumResources.HotelReservation))]
        HotelReservation = 4,
        [LocalizedDescription(nameof(EnumResources.HealthInsurance))]
        HealthInsurance = 5,
        [LocalizedDescription(nameof(EnumResources.PassportPhotocopy))]
        PassportPhotocopy = 6,
        [LocalizedDescription(nameof(EnumResources.Invitation))]
        Invitation = 7,
        [LocalizedDescription(nameof(EnumResources.LetterOfAcceptance))]
        LetterOfAcceptance = 8
    }

    #endregion

    #region Inquiry

    public enum InquiryQuestionChoiceType
    {
        [LocalizedDescription(nameof(EnumResources.Elective))]
        Elective = 1,
        [LocalizedDescription(nameof(EnumResources.Explanation))]
        Explanation = 2,
        [LocalizedDescription(nameof(EnumResources.Combobox))]
        Combobox = 3
    }

    #endregion

    #region Vms

    public enum VmsAnnouncementLanguage
    {
        [LocalizedDescription(nameof(EnumResources.Turkish))]
        Turkish = 1,
        [LocalizedDescription(nameof(EnumResources.English))]
        English = 2,
        [LocalizedDescription(nameof(EnumResources.Arabic))]
        Arabic = 3,
        [LocalizedDescription(nameof(EnumResources.French))]
        French = 4,
        [LocalizedDescription(nameof(EnumResources.Russian))]
        Russian = 5,
        [LocalizedDescription(nameof(EnumResources.Turkmen))]
        Turkmen = 6
    }

    #endregion
}