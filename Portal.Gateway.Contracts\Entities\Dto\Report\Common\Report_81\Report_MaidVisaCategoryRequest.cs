﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System;

namespace Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_81
{
    public class Report_MaidVisaCategoryRequest : IReportDetail
    {
        [Required]
        public IEnumerable<int> BranchIds { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }
    }
}
