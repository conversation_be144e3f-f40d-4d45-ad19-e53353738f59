{"profiles": {"WSL": {"commandName": "WSL2", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}, "distributionName": ""}, "Portal.Gateway.BackgroundWorkers-Development-K8S": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development-K8S", "DOTNET_ENVIRONMENT": "Development-K8S"}}, "Portal.Gateway.BackgroundWorkers-Local": {"commandName": "Project", "environmentVariables": {"DOTNET_ENVIRONMENT": "Local"}}, "Portal.Gateway.BackgroundWorkers-Staging-K8S": {"commandName": "Project", "environmentVariables": {"DOTNET_ENVIRONMENT": "Staging-K8S"}}}, "$schema": "http://json.schemastore.org/launchsettings.json"}