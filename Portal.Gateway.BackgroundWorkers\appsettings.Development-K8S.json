{
  "AppSettings": {
    "ApiKey": "Gateway.ApiKey.2021",
    "ActiveDirectoryUrl": "gateway.com.tr",
    "EsimBucket": "esim",
    "BackgroundWorkers": {
      "IsNewClaimLossSendJobEnabled": true
    },
    "B2B": {
      "BaseUrl": "http://localhost:6001",
      "ForgetPasswordUrl": "forget-password"
    },
    "Ldap": {
      "PrimaryServer": {
        "HostName": "GWDC01.gateway.com.tr",
        "Port": 389
      },
      "SecondaryServer": {
        "HostName": "GWDC02.gateway.com.tr",
        "Port": 389
      },
      "SearchBase": "DC=gateway,DC=com,DC=tr",
      "UserIdAttributeName": "sAMAccountName",
      "ServiceUserDN": "CN=Gateway Portal Service,OU=SERVICE_USERS,OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr",
      "ServiceUserPassword": "2024@Pass",
      "SyncInterval": 5,
      "AllowedUnits": [
        "OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr"
      ],
      "NotAllowedUnits": [
        "OU=MYNEST,OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr",
        "OU=SERVICE_USERS,OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr",
        "OU=EXTERNALS,OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr"
      ],
      "Notification": {
        "Subject": "Kullanıcı İşlemleri Hakkında Bilgilendirme",
        "Contact": "<EMAIL>;<EMAIL>"
      }
    },
    "PublicKey": [
      {
        "Id": 1,
        "Token": "E9A48DD1-F72D-4C49-BB0D-CE61A2B35F5E",
        "Username": "Biometrics",
        "Urls": [
          "/api/Public/GetApplication",
          "/api/Public/AddBiometricsFingerPrintDocument"
        ]
      },
      {
        "Id": 2,
        "Token": "D9D35D60-DCE3-4651-86EB-E071D674BC6D",
        "Username": "GatewayGlobe",
        "Urls": [
          "/api/Public/GetApplicationStatus"
        ]
      },
      {
        "Id": 3,
        "Token": "EC604752-7E56-4077-B40D-6CDA6C1BBCF9",
        "Username": "Photobooth",
        "Urls": [
          "/api/Public/ValidatePhotoBooth",
          "/api/Public/UpdatePhotoBooth"
        ]
      },
      {
        "Id": 4,
        "Token": "B3115BC3-2B31-4F34-965F-FD63A2AF2128",
        "Username": "B2B",
        "Urls": [
          "/api/Public/GetApplication",
          "/api/Public/GetApplicationStatus",
          "/api/Public/ValidatePhotoBooth",
          "/api/Public/UpdatePhotoBooth",
          "/api/Public/AddPreApplication",
          "/api/Public/GetPaginatedPreApplications",
          "/api/Public/GetPreApplication",
          "/api/Public/DeletePreApplication",
          "/api/Public/UpdatePreApplication",
          "/api/Public/NotifyForgetPassword",
          "/api/Public/ValidateForgetPasswordToken",
          "/api/Public/SetPassword",
          "/api/Public/AgencyUserLogin",
          "/api/Public/ChangePassword",
          "/api/Public/GetBranchApplicationCountry",
          "/api/Public/GetSlots",
          "/api/Public/GetCustomerSlots",
          "/api/Public/AddAgency",
          "/api/Public/UpdateAgency",
          "/api/Public/GetAgency",
          "/api/Public/GetPaginatedAgencyFiles",
          "/api/Public/AddAgencyUser",
          "/api/Public/DeleteAgencyUser",
          "/api/Public/UpdateAgencyUser",
          "/api/Public/GetAgencyUser",
          "/api/Public/GetAllAgencyUsers",
          "/api/Public/GetCountries",
          "/api/Public/GetAgencyTypes",
          "/api/Public/GetBranchApplicationCountriesByCountryId",
          "/api/Public/GetEnumSelectList",
          "/api/Public/GetAllBranchApplicationCountryFiles",
          "/api/Public/GetAgencyApplicationStats",
          "/api/Public/GetActiveCountries",
          "/api/Public/GetPeriodicAppointmentStats",
          "/api/Public/GetAgencyInsuranceStats",
          "/api/Public/AddDocuments",
          "/api/Public/GetDocuments",
          "/api/Public/GetApplicationStatuses",
          "/api/Public/AddCustomer",
          "/api/Public/UpdateCustomer",
          "/api/Public/GetCustomer",
          "/api/Public/ChangeCustomerPassword",
          "/api/Public/CustomerLogin",
          "/api/Public/GetVisaCategoryFiles",
          "/api/Public/AddApplication",
          "/api/Public/GetPaginatedInsuranceApplications",
          "/api/Public/GetPreApplicationForm",
          "/api/Public/CreatePolicy",
          "/api/Public/CertificatePolicy",
          "/api/Public/UpdateSignStatus"
        ]
      },
      {
        "Id": 5,
        "Token": "D2797DAB-FEE2-4D62-94CF-3D6CC77DF672",
        "Username": "Avaya",
        "Urls": [
          "/api/Public/GetApplication",
          "/api/Public/GetApplicationStatus",
          "/api/Public/GetApplicationStatusNew"
        ]
      },
      {
        "Id": 6,
        "Token": "X2797DAB-FEE2-4D62-94CF-3D6CC77DF672",
        "Username": "Test",
        "Urls": [
          "/api/Public/GetApplication",
          "/api/Public/GetApplicationStatus",
          "/api/Ministry/UploadPackage",
          "/api/Ministry/CreatePackage"
        ]
      }
    ],
    "SftpConfiguration": {
      "Host": "************",
      "Port": 22,
      "Username": "sftptestuser",

      "Password": "4*Yhq!5Gw37@",

      "Fingerprint": "ssh-ed25519 255 Jb2iqhCnQ1Z2miSlbxTGOjxRLZT5Z63rmdQ2kgoYC2s=",
      "RootFolder": "./Uploads"
    },
    "MinioConfiguration": {
      "EndPoint": "visauatcdn.gateway.com.tr:443",
      "AccessKey": "QLQ5Wdvy4l7lDtWsW5kL",
      "SecretKey": "qgJs5lMHalS464tJPf2KIOEyrNc7jMmAHmL5An5V",
      "BucketPrefix": "test-"
    },
    "FileEncryptDecrypt": {
      "CipherKey": "DrdI28+0ag282rrqHsmxEwxK4K/FAKo8Pl03rVWC9zw=",
      "CipherIV": "aa0OzGWMLJGfpxCrz/h5PQ=="
    },
    "Cargo": {
      "BaseApiUrl": "http://gatewayportal-cargo-api.gatewayportal-develop.svc.cluster.local:8080",
      "ListCargoToCheckStatus": "/api/Cargo/ListCargoToCheckStatus/",
      "UpdateCargoStatus": "/api/Cargo/UpdateStatus",
      "Track": "/api/Cargo/Track/",
      "IsEnabled": false
    }
  },
  "IntegrationSettings": {
    "UnicoInsurance": {
      "Id": 1, // DB Provider Id
      "SecurityServiceEndpointUrl": "https://previva.unicosigorta.com.tr/Public/SecurityService.svc",
      "IntegrationServiceEndpointUrl": "https://previva.unicosigorta.com.tr/Public/Integration017.svc",
      "PolicyServiceEndpointUrl": "https://previva.unicosigorta.com.tr/Public/PolicyService.svc",
      "CertificateServiceEndpointUrl": "http://PolicyCertificateWS.unicosigorta.com.tr/Certificate.asmx",
      "Environment": "PreProd",
      "AppSecurityKey": "Gateway#",
      "UserName": "GRAVIS_WEB3",
      "Password": "EVVANAL1",
      "BranchCode": "13",
      "SourceCode": "3",
      "AgentNo": "0516052"
    },
    "Sap": {
      "Id": 2, // DB Provider Id
      "CreateOrderEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_001/400/zgwsd_fg004_001/zgwsd_fg004_001",
      "QueryInvoiceEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_003/400/zgwsd_fg004_003/zgwsd_fg004_003",
      "RefundInvoiceEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_004/400/zgwsd_fg004_004/zgwsd_fg004_004",
      "CancelInvoiceEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_005/400/zgwsd_fg004_005/zgwsd_fg004_005",
      "ExchangeInvoiceEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_009/400/zgwsd_fg004_009/zgwsd_fg004_009",
      "GetSapRateInformationUrl": "http://GWS4QA.gateway.com.tr:8000/sap/bc/srt/rfc/sap/zgwfi_ws_fg007/400/zgwfi_ws_fg007/zgwfi_ws_fg007_lp",
      "SendVisaRejectionSapUrl": "http://GWS4QA.gateway.com.tr:8000/sap/bc/srt/rfc/sap/zgwfi_ws_fg008/400/ws_fg008/ws_fg008",
      "Binding": "SapWebServiceBinding",
      "UserName": "WEBSERVICE",
      "Password": "WEBSERVICE2020"
    },
    "Emaa": {
      "Id": 3, // DB Provider Id
      "ServiceUrl": "http://10.180.90.50/POLSVC/SFSPOLSVCWS.asmx",
      "ServiceDisasteryUrl": "http://10.180.90.50/POLSVC/SFSPOLSVCWS.asmx",
      "LossClaimServiceUrl": "http://10.180.90.50/SFS.Services.Claim/FileOperations.svc",
      "LossClaimServiceDisasteryUrl": "http://10.180.90.50/SFS.Services.Claim/FileOperations.svc",
      "UserName": "TpiIFQDekYEKwg6zpZbPJA==",
      "Password": "m4YKwOovSAQ="
    },
    "Ministry": {
      "Token": "3e23e8160039594a33894f6564e1b1348bbd7a0088d42c4acb73eeaed59c009e",
      "VisaEndpointBaseUrl": "http://test.konsolosluk.gov.tr/api/v1/visapack/"
    },
    "Biometrics": {
      "BiometricsEndpointUrl": "http://10.31.42.70:3434",
      "BiometricsEndpointUrl-legacy": "http://visa.gateway.com.tr/WebServices/Biometry.asmx?op=BasvuruBilgileri",
      "Token": "604B120D-5BF3-4B3A-913A-753786214D49",
      "UserName": "BioUser",
      "Password": "432BY?"
    },
    "Sms": {
      "Clickatell": {
        "ClickatellServiceEndpointUrl": "https://platform.clickatell.com/v1/",
        "ClickatellApiMethod": "message/",
        "ApiKey": "xXLICydCQDKxB348M-_Y5w==",
        "Channel": "sms"
      },
      "TurkTelekom": {
        "TurkTelekomServiceEndpointUrl": "https://ws.ttmesaj.com/Service1.asmx",
        "Username": "gateway",
        "Password": "G4A9T1M6",
        "Origin": "Gateway"
      },
      "Turkcell": {
        "TurkcellServiceEndpointUrl": "https://api.dataport.com.tr/restapi/",
        "RegisterApiMethod": "Register/",
        "SendSmsApiMethod": "api/Messages/SendSMS/",
        "AccountNumber": "???",
        "UserName": "???",
        "Password": "???",
        "GrantType": "???"
      }
    },
    "Email": {
      "SmtpOffice365": {
        "UserName": "<EMAIL>",
        "Password": "eqYcSU5!",
        "Smtp": "smtp.office365.com",
        "Port": "587"
      },
      "SendGrid": {
        "Sender": "<EMAIL>",
        "Credential": "*********************************************************************"
      }
    },
    "ClearTaxKsaIcr": {
      "GenerateEInvoiceServiceUrl": "https://api-sandbox.cleartax.com/middle-east/ksa/einvoicing/v2/einvoices/generate"
    }
  },
  "elasticsearch": {
    "Index": "gateway-log",
    "Username": "elastic",
    "Password": "htJsew49I8jPiL6How7U",
    "Url": "https://visaelastic.gateway.com.tr:9200",
    "IsEnabled": false
  },
  "ConnectionStrings": {
    "GatewayPortalDbConnection": "Server=**********;Username=gw_portal_stg_app_usr;Password=*****************;Database=GatewayPortalDbDev;MinPoolSize=4;MaxPoolSize=25;ApplicationName=gateway-backgroundworkers;",
    "GatewayNotificationDbConnection": "Server=**********; Database=GatewayNotification; Username=gw_portal_stg_app_usr;Password=*****************;MinPoolSize=4;MaxPoolSize=25;ApplicationName=gateway-backgroundworkers;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "RabbitMq": {
    "Host": "visauatrabbitmq.gateway.com.tr",
    "User": "staging_test",
    "Password": "stagin_test*4mqp",
    "Port": 5672,
    "Exchange": "log.exchange",
    "LoggerQueue": "LoggerQueue",
    "EmailQueue": "EmailQueue",
    "SMSQueue": "SMSQueue"
  },
  "Redis": {
    "Url": "visauatredis.gateway.com.tr",
    "Port": "6379",
    "ConnectTimeout": 10000,
    "ConnectRetry": 3,
    "DefaultDatabase": 0,
    "PrinterServiceDefaultDatabase": 2
  },
  "ContactInformationVerificationSecret": "OvwtalPB3YjeeLReriRWjJyzt3phRCax",
  "Firebase": ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************,
  "EmailProviders": {
    "IsEnabled": false,
    "SendGrid": {
      "Credential": "*********************************************************************"
    }
  },
  "SmsProviders": {
    "IsEnabled": false,
    "TTMesaj": {
      "ServiceUrl": "https://ws.ttmesaj.com/Service1.asmx",
      "Username": "gateway",
      "Password": "G4A9T1M6",
      "Origin": "Gateway"
    },
    "Horisen": {
      "ServiceUrl": "https://sms.horisen.info:12021/bulk/sendsms",
      "Username": "14766_Gateway",
      "Password": "3qbnXtKL",
      "Origin": "Gateway"
    },
    "Clickatell": {
      "ServiceEndpointUrl": "https://api.clickatell.com/rest/message",
      "Token": "Bearer ZjLyMy1yVyC8E61ExaGTf1CVZZ6iRBSXuiFDgUMLOWWkz6txqzetsiX_Q5S98nCJoUCg597Pi29Oh0",
      "XVersion": "1"
    },
    "Sinch": {
      "ServiceEndpointUrl": "https://EU.sms.api.sinch.com/xms/v1/GatewayMgmt_rest/batches",
      "Token": "Bearer b2b3f39c9b09478aa2e7a913e8437eff"
    },
    "InfoBip": {
      "IRQ": {
        "ServiceEndpointUrl": "https://3gmmnv.api.infobip.com/sms/2/text/advanced",
        "Token": "943785accf5659cffc1a0c98fdab1f90-19d61b4f-2fe3-4933-b759-b7d106fb5488"
      },
      "TKM": {
        "ServiceEndpointUrl": "https://e1ddl2.api.infobip.com/sms/2/text/advanced",
        "Token": "3ed2dc1b39c4fb44a592457739514e30-80ebc26c-dbce-4104-a8fb-85f2fdc47936"
      },
      "KSA": {
        "ServiceEndpointUrl": "https://j3qqmn.api.infobip.com/sms/2/text/advanced",
        "Token": "0eaad25219d0eba8b688553ac09a1144-96d5f443-4bd3-4566-b6cf-ee9b5a01efbd"
      },
      "RUS": {
        "ServiceEndpointUrl": "https://6gyy35.api.infobip.com/sms/2/text/advanced",
        "Token": "4184c2698997957118d15ba51f280cc6-4f39926c-ba47-451e-88ac-6df69870c22b"
      },
      "KWT": {
        "ServiceEndpointUrl": "https://vv99mv.api.infobip.com/sms/2/text/advanced",
        "Token": "1e1fe864c8cc0608c7cd3b2885b0df95-ec07988d-df19-4e10-b483-0d0a23f55149"
      },
      "UAE": {
        "ServiceEndpointUrl": "https://9lvv3y.api.infobip.com/sms/2/text/advanced",
        "Token": "262a1fbe51fd2e612de5f32a9a325956-b50b7f6d-1fb4-4adb-9c2f-44beff9c5221"
      },
      "IND": {
        "ServiceEndpointUrl": "https://l3xxg2.api.infobip.com/sms/2/text/advanced",
        "Token": "5be72f94c3ed5415b62bd34e7997e38f-0146c1ae-fd65-4ad8-8dfe-d968a17f6c20"
      },
      "ALG": {
        "ServiceEndpointUrl": "https://5ypw6g.api.infobip.com/sms/2/text/advanced",
        "Token": "fc0f10be034b8a66027669a636680799-195ff075-3703-43d4-930f-c5049cef917a"
      },
      "LIB": {
        "ServiceEndpointUrl": "https://e1ve42.api.infobip.com/sms/2/text/advanced",
        "Token": "03296c44d286287b990c0fdfff9ef2f7-ef8884f9-1cdf-44f5-b1e1-7ef9300a3393"
      }
    }
  }
}
