﻿using System.Collections.Generic;

namespace Gateway.External.Application.User.Dto
{
    public class PushNotificationDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string CreatedAt { get; set; }
        public string LastSendAt { get; set; }
        public LookupValue Status { get; set; }
        public List<PushNotificationHistoryDto> Histories { get; set; }
    }
}
