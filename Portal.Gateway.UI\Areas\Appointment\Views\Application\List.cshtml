﻿@{
    ViewData["Title"] = @SiteResources.ApplicationList.ToTitleCase();
}

@model FilterApplicationViewModel

<head>
    <link href="~/css/font-awesome.min.css" rel="stylesheet" type="text/css" asp-append-version="true" />
</head>

<form id="report-form" class="card card-custom card-stretch" method="post">
    @Html.HiddenFor(p => p.FilterAllBranchs)
    <div class="card card-custom card-stretch">
        <div class="card-header">
            <div class="card-title">
                <h3 class="card-label">
                    @SiteResources.ApplicationList.ToTitleCase()
                </h3>
            </div>
            <div class="card-toolbar">
                <div class="btn-group">
                    <button type="submit" id="report-button-" style="display: none" class="btn btn-outline-primary font-weight-bold mr-2" asp-action="GetApplicationReport" asp-area="Appointment" asp-controller="Application">
                        <i class="la la-list-alt"></i> @SiteResources.GenerateReport.ToTitleCase()
                    </button>
                    <a href="/Appointment/Application/SelectBranchApplicationCountry" class="btn btn-primary font-weight-bold">
                        <i class="la la-plus"></i> @SiteResources.AddNewApplication.ToTitleCase()
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="mb-5">
                <span class='label label-xl label-dot label-success ml-1'></span>
                <span>@SiteResources.Main @EnumResources.ApplicantTypeGroup.ToTitleCase()</span>
                <span class='label label-xl label-dot label-info ml-1'></span>
                <span>@SiteResources.Main @EnumResources.ApplicantTypeFamily.ToTitleCase()</span>
                <span class='label label-xl label-dot label-warning ml-1'></span>
                <span>@SiteResources.ApplicationNotesExist.ToTitleCase()</span>
            </div>
            <div class="accordion accordion-toggle-arrow mb-3">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title" data-toggle="collapse" data-target="#filtering-options">
                            <i class="la la-filter"></i> @SiteResources.FilteringOptions.ToTitleCase()
                        </div>
                    </div>
                    <div id="filtering-options" class="collapse">
                        <div class="card-body">
                            <div class="form-group row">
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.ApplicationNumber.ToTitleCase()</label>
                                    <input type="text" asp-for="FilterApplicationNumber" class="form-control" />
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.Country.ToTitleCase()</label>
                                    @(Html.Kendo().DropDownListFor(m => m.FilterCountryId)
                                        .HtmlAttributes(new { @class = "form-control" })
                                        .Filter(FilterType.Contains)
                                        .OptionLabel(SiteResources.Select)
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .DataSource(source =>
                                        {
                                            source.Read(read =>
                                            {
                                                read.Action("GetCachedCountrySelectList", "Parameter", new { Area = "" });
                                            });
                                        }))
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.Agency.ToTitleCase()</label>
                                    @(Html.Kendo().DropDownListFor(m => m.FilterAgencyId)
                                        .HtmlAttributes(new { @class = "form-control" })
                                        .Filter(FilterType.Contains)
                                        .OptionLabel(SiteResources.Select)
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .DataSource(source =>
                                        {
                                            source.Read(read =>
                                            {
                                                read.Action("GetAgencySelectList", "Parameter", new { Area = "" });
                                            });
                                        }))
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.ApplicantType.ToTitleCase()</label>
                                    @(Html.Kendo().DropDownListFor(m => m.FilterApplicantTypeId)
                                        .HtmlAttributes(new { @class = "form-control" })
                                        .Filter(FilterType.Contains)
                                        .OptionLabel(SiteResources.Select)
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .DataSource(source =>
                                        {
                                            source.Read(read =>
                                            {
                                                read.Action("GetApplicantTypeSelectList", "Parameter", new { Area = "" });
                                            });
                                        }))
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.ApplicationType.ToTitleCase()</label>
                                    @(Html.Kendo().DropDownListFor(m => m.FilterApplicationTypeId)
                                        .HtmlAttributes(new { @class = "form-control" })
                                        .Filter(FilterType.Contains)
                                        .OptionLabel(SiteResources.Select)
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .DataSource(source =>
                                        {
                                            source.Read(read =>
                                            {
                                                read.Action("GetApplicationTypeSelectList", "Parameter", new { Area = "" });
                                            });
                                        }))
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.Name.ToTitleCase()</label>
                                    <input type="text" asp-for="FilterName" class="form-control" />
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.Surname.ToTitleCase()</label>
                                    <input type="text" asp-for="FilterSurname" class="form-control" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.PassportNumber.ToTitleCase()</label>
                                    <input type="text" asp-for="FilterPassportNumber" class="form-control" />
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.Nationality.ToTitleCase()</label>
                                    @(Html.Kendo().DropDownListFor(m => m.FilterNationalityId)
                                        .HtmlAttributes(new { @class = "form-control" })
                                        .Filter(FilterType.Contains)
                                        .OptionLabel(SiteResources.Select)
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .DataSource(source =>
                                        {
                                            source.Read(read =>
                                            {
                                                read.Action("GetCachedCountrySelectList", "Parameter", new { Area = "" });
                                            });
                                        }))
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.Email.ToTitleCase()</label>
                                    <input type="text" asp-for="FilterEmail" class="form-control" />
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.PhoneNumber.ToTitleCase()</label>
                                    <input type="text" asp-for="FilterPhoneNumber" class="form-control" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.VisaCategory.ToTitleCase()</label>
                                    @(Html.Kendo().DropDownListFor(m => m.FilterVisaCategoryId)
                                        .HtmlAttributes(new { @class = "form-control" })
                                        .Filter(FilterType.Contains)
                                        .OptionLabel(SiteResources.Select)
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .DataSource(source =>
                                        {
                                            source.Read(read =>
                                            {
                                                read.Action("GetVisaCategoryTypeByDefinitionSelectList", "Parameter", new { Area = "" });
                                            });
                                        }))
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold k-display-block">@SiteResources.AllowPassiveDeletedApplications.ToTitleCase()</label>
                                    @(Html.Kendo().CheckBoxFor(m => m.FilterAllowPassiveDeletedApplications).HtmlAttributes(new { @class = "checkbox-square", onclick = "checkPassiveDeletedApplicationsAllowed()" }).Label(SiteResources.Yes.ToTitleCase()))
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.StartDate.ToTitleCase()</label>
                                    @(Html.Kendo().DatePickerFor(m => m.FilterStartDate).Format(SiteResources.DatePickerFormatView))
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.EndDate.ToTitleCase()</label>
                                    @(Html.Kendo().DatePickerFor(m => m.FilterEndDate).Format(SiteResources.DatePickerFormatView))
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold k-display-block">@SiteResources.ResidenceApplicationToBeMade.ToTitleCase()</label>
                                    @(Html.Kendo().DropDownListFor(m => m.FilterResidenceApplication)
                                        .HtmlAttributes(new { @class = "form-control" })
                                        .AutoWidth(true)
                                        .OptionLabel(SiteResources.Select)
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .DataSource(source =>
                                        {
                                            source.Read(read =>
                                            {
                                                read.Action("GetYesNoQuestionSelectList", "Parameter", new { Area = "", hasUnspecified = true });
                                            });
                                        }))
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.BirthDate.ToTitleCase()</label>
                                    @(Html.Kendo().DatePickerFor(m => m.FilterBirthDate).Format(SiteResources.DatePickerFormatView).Max(DateTime.Now))
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-lg-6">
                                    <div>
                                        <button id="filterGridApplication" type="button" class="btn btn-primary mr-1"><i class="la la-search"></i>@SiteResources.Filter</button>
                                        <button id="clearGridApplicationFilter" type="reset" class="btn btn-secondary mr-1"><i class="la la-times"></i>@SiteResources.Clear</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-lg-12">
                    @(
                        Html.Kendo().Grid<ApplicationViewModel>()
                        .Name("gridApplication")
                        .Columns(columns =>
                        {
                            columns.Bound(o => o.RelationalApplicationId)
                            .ClientTemplate(
                            "<a class='font-weight-bolder btn btn-link-info' href='/Appointment/Application/DetailApplicationSummary?encryptedApplicationId=#=EncryptedId#' target='_blank'>#=getItemTextValue(EncryptedId,'ToDecryptInt').padStart(13, '0')#</a>" +
                            "# if (RelationalApplicationId == null && ApplicantTypeId == " + ((int)ApplicantType.Group).ToString() + ") { #" +
                            "<span class='label label-xl label-dot label-success ml-1'></span>" +
                            "# } #" +
                            "# if (RelationalApplicationId == null && ApplicantTypeId == " + ((int)ApplicantType.Family).ToString() + ") { #" +
                            "<span class='label label-xl label-dot label-info ml-1'></span>" +
                            "# } #" +
                            "# if (RelationalApplicationId != null) { #" +
                            "<span class='text-muted font-weight-bold mr-2' style='font-size:10px'>#=RelationalApplicationId.toString().padStart(13, '0')#</span>" +
                            "# } #" +
                            "# if (HasApplicationNotes) { #" +
                            "<span class='label label-xl label-dot label-warning ml-1'></span>" +
                            "# } #" +
                            (Model.IsAuthorizedInterviewChecbox == true ?
                            "# if (BranchCountryIso3 == 'TKM') { #" +
                            "# if (IsInterviewRequired) { #" +
                            "<br>" + "<input type='checkbox' checked onchange=UpdateInterviewInformation('#=EncryptedId#',this,''); #=IsInterviewRequired#/><span>&nbsp;" + SiteResources.InterviewRequired + "</span>" +
                            "# } #" +
                            "# if (!IsInterviewRequired) { #" +
                            "<br>" + "<input type='checkbox' onchange=UpdateInterviewInformation('#=EncryptedId#',this,''); #=IsInterviewRequired#/><span>&nbsp;" + SiteResources.InterviewRequired + "</span>" +
                            "# } #" +
                            "# if (IsInterviewDone) { #" +
                            "<br>" + "<input type='checkbox' checked onchange=UpdateInterviewInformation('#=EncryptedId#','',this); #=IsInterviewDone#/><span>&nbsp;" + SiteResources.InterviewDone + "</span>" +
                            "# } #" +
                            "# if (!IsInterviewDone) { #" +
                            "<br>" + "<input type='checkbox' onchange=UpdateInterviewInformation('#=EncryptedId#','',this); #=IsInterviewDone#/><span>&nbsp;" + SiteResources.InterviewDone + "</span>" +
                            "# } #" +
                            "# } #"
                            : "")
                            )
                            .Title(SiteResources.ApplicationNumber.ToTitleCase());
                            columns.Bound(o => o.PassportNumber).Title(SiteResources.PassportNumber.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.ApplicationTimeText).Title(SiteResources.ApplicationTime.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.ApplicantType).Title(SiteResources.ApplicantType.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.ApplicationType).Title(SiteResources.ApplicationType.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.ApplicationStatus).Title(SiteResources.ApplicationStatus.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.Status).Title(SiteResources.Status.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.Name).Title(SiteResources.Name.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.Surname).Title(SiteResources.Surname.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.CountryName).Title(SiteResources.Country.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.EncryptedId).ClientTemplateId("contactInformationTemplate").Title(SiteResources.ContactInformation.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.ApplicationNote).ClientTemplate(
                            "# if (ApplicationNote != '') { #" +
                            "<i class='k-icon k-i-warning' style='color: red; font-weight: bold;'></i>" +
                            "# } #"
                            ).Title(SiteResources.ApplicationNotes.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.Note).ClientTemplate(
                            "# if (Note != null) { #" +
                            "<i class='k-icon k-i-warning' style='color: black; font-weight: bold;'></i>" +
                            "# } #"
                            ).Title(SiteResources.Note.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.EncryptedId).ClientTemplateId("documents").Title(SiteResources.ApplicationFiles.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.EncryptedId).ClientTemplateId("gridOperationsTemplate").Title(SiteResources.Operations).Width(150);
                            columns.Bound(o => o.HasApplicationNotes).Hidden(true);
                            columns.Bound(o => o.HasApplicationFile).Hidden(true);
                            columns.Bound(o => o.EncryptedId).Hidden(true);
                            columns.Bound(o => o.BranchCountryIso3).Hidden(true);
                            columns.Bound(o => o.IsInterviewRequired).Hidden(true);
                            columns.Bound(o => o.IsInterviewDone).Hidden(true);
                            columns.Bound(o => o.Document.ProvidedWithHasRelatedInsurance).Hidden(true);
                        })
                        .DataSource(dataSource => dataSource
                        .Ajax()
                        .Read(read => read.Action("GetSanitizedPaginatedApplications", "Application", new { Area = "Appointment" }).Data("gridApplicationFilterData"))
                        .PageSize(10)
                        )
                        .Scrollable(s => s.Height("auto"))
                        .ClientDetailTemplateId("detailedInformationAboutExemptPersons")
                        .Pageable(pager => pager
                        .Refresh(true)
                        .PageSizes(new[] { 10, 20, 50 }))
                        .Events(evnts => evnts.DataBound("ApplicationNotesAndFiles"))
                        )
                </div>
            </div>
        </div>
    </div>
</form>

<div class="modal fade" id="modalAddApplicationCancellation" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.ApplicationCancellationDetails.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialAddApplicationCancellation"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalApplicationEasyUse" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.EasyUse.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialApplicationEasyUse"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal" data-backdrop="static" data-keyboard="false" id="modalYssCheck" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.YSS_Check.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialYssCheck"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal" data-backdrop="static" data-keyboard="false" id="modalPartialApplicationFiles" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.ApplicationFiles.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialApplicationFiles"></div>
            </div>
        </div>
    </div>
</div>

<style>
    .k-grid-toolbar {
        justify-content: flex-end;
    }

    .k-grid .k-alt.k-state-selected {
        background-color: #a3edf6 !important;
        color: #000000;
    }
</style>

@section Scripts {
    <script src="~/js/Appointment/Application/application.js"></script>

    <script id="gridOperationsTemplate" type="text/x-kendo-tmpl">
        <div class="btn-group">
            <button class="btn btn-sm btn-light-primary font-weight-bold btn-sm dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-placement="right">
        @Html.Raw(SiteResources.Select)
            </button>
            <div class="dropdown-menu dropdown-menu-right">
                # if (IsActive == true && IsDeleted == false) { #
                    <a class='dropdown-item' href='/Appointment/Application/DetailApplicationSummary?encryptedApplicationId=#=EncryptedId#' target="_blank">@Html.Raw(SiteResources.ApplicationDetails.ToTitleCase())</a>
                    <a class='dropdown-item' href='javascript:void(0);' onclick="partialYssCheck('#=EncryptedId#');">@Html.Raw(SiteResources.YSS_Check)</a>
                    <a class='dropdown-item' href='/Appointment/Application/Update?encryptedApplicationId=#=EncryptedId#' target="_blank">@Html.Raw(SiteResources.Update.ToTitleCase())</a>
                    <div class="dropdown-divider"></div>
                    <a class='dropdown-item' data-toggle='modal' data-target='\\#modalAddApplicationCancellation' href='javascript:void(0);' onclick="partialAddApplicationCancellation('#=EncryptedId#', '@ApplicationCancellationType.PartialRefund.ToInt()');">@Html.Raw(SiteResources.PartialRefund)</a>
                    <a class='dropdown-item' data-toggle='modal' data-target='\\#modalAddApplicationCancellation' href='javascript:void(0);' onclick="partialAddApplicationCancellation('#=EncryptedId#', '@ApplicationCancellationType.Cancellation.ToInt()');">@Html.Raw(SiteResources.Cancel)</a>
                    <div class="dropdown-divider"></div>
                    <a class='dropdown-item' href='/Appointment/ApplicationStatus/IncorrectApplicationStatus?encryptedApplicationId=#=EncryptedId#'">@Html.Raw(SiteResources.IncorrectApplicationStatus.ToTitleCase())</a>
                # } #
                    <a class='dropdown-item' data-toggle='modal' data-target='\\#modalApplicationEasyUse' href='javascript:void(0);' onclick="partialApplicationEasyUse('#=EncryptedId#');">@Html.Raw(SiteResources.EasyUse)</a>
            </div>
        </div>
    </script>

    <script id="contactInformationTemplate" type="text/x-kendo-tmpl">
        <div>
            <i class="la la-envelope"></i>
            <span>#=Email#</span>
        </div>
        <div>
            <i class="la la-phone"></i>
            <span>#=PhoneNumber1#</span>
        </div>
    </script>

    <script id="documents" type="text/x-kendo-tmpl">
        <div>
           <i class="la la-file-text icon" style="font-size:36px; color: purple;" href='javascript:void(0);' onclick="partialApplicationFiles('#=EncryptedId#');"></i>
        </div>
    </script>

    <script id="detailedInformationAboutExemptPersons" type="text/x-kendo-tmpl">
        @(Html.Kendo().Grid<ApplicationDetailExemptPersonsViewModel>()
                          .Name("gridApplicationExemptPerson_#=EncryptedId#")
                          .Columns(columns =>
                          {
                              columns.Bound(o => o.EncryptedId).Visible(false);
                              columns.Bound(o => o.ApplicantNumber).Title(SiteResources.ApplicationNumber.ToTitleCase());
                              columns.Bound(o => o.Nationality).Title(SiteResources.Nationality.ToTitleCase());
                              columns.Bound(o => o.PassportNumber).Title(SiteResources.PassportNo.ToTitleCase());
                              columns.Bound(o => o.Name).Title(SiteResources.Name.ToTitleCase());
                              columns.Bound(o => o.Surname).Title(SiteResources.Surname.ToTitleCase());
                              columns.Bound(o => o.PhoneNumber).Title(SiteResources.PhoneNumber.ToTitleCase());
                              columns.Bound(o => o.Email).Title(SiteResources.Email.ToTitleCase());
                          })
                          .DataSource(dataSource => dataSource
                              .Ajax()
                              .Read(read => read.Action("GetDetailedInformationAboutExemptPersons", "Application", new { Area = "Appointment" }).Data("gridApplicationExemptPersonData('#=EncryptedId#')"))
                              .PageSize(10)
                          )
                          .Scrollable(s => s.Height("auto"))
                          .ToClientTemplate()
                    )
    </script>


    <script>
        $('#filtering-options').on('keyup', function (e) {
            if (e.key == "Enter") $('#filterGridApplication').click();
            if (e.key == "Escape") $('#clearGridApplicationFilter').click();
        });

        function checkPassiveDeletedApplicationsAllowed() {
            var allowPassiveDeletedApplications = $('#FilterAllowPassiveDeletedApplications').is(":checked");
            $("#FilterAllowPassiveDeletedApplications").prop("checked", false);

            if (allowPassiveDeletedApplications) {
                return new Promise(function (resolve, reject) {
                    $.ajax({
                        type: "GET",
                        url: "/Appointment/Application/IsPassiveDeletedApplicationsAllowed",
                        data: {
                        },
                        success: function (data) {
                            if (data) {
                                if (data.Result === true) {
                                    $("#FilterAllowPassiveDeletedApplications").prop("checked", true);
                                }
                                else if (data.Message) {
                                    bootbox.dialog({
                                        message: '<p style="color:#FFFFFF;"><b>' + data.Message + '</b></p>',
                                        size: 'extra-large',
                                        onEscape: true,
                                        backdrop: true
                                    });
                                    $('.modal-content').css("background-color", "#FFA800");
                                }
                                else {
                                    showNotification('danger', jsResources.ErrorOccurred);
                                }
                            }
                        },
                        error: function () {
                            showNotification('danger', jsResources.ErrorOccurred);
                        },
                        complete: function () {
                            resolve();
                        }
                    });
                });
            }
        }

    </script>

    <script>
        $(document).ready(function () {
            $(window).keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });
        });
    </script>

    <script>
        function ApplicationNotesAndFiles() {
            var grid = $("#gridApplication").data("kendoGrid");
            var data = grid.dataSource.data();

            $.each(data, function (i, row) {
                if (row.HasApplicationNotes)
                    $('tr[data-uid="' + row.uid + '"] ').css("background-color", "#ffffb2");
            });

            $.each(data, function (i, row) {
                if (!row.HasApplicationFile) {
                    var row = $('tr[data-uid="' + row.uid + '"] ');
                    var icon = row.find(".icon");

                    icon.css("display", "none");
                }
            });

            var rows = grid.items();

            rows.each(function () {
                var dataItem = grid.dataItem(this);
                console.log(dataItem.Document.ProvidedWithHasRelatedInsurance)
                if (!dataItem.Document.ProvidedWithHasRelatedInsurance) {
                    $(this).find(".k-hierarchy-cell").html("");
                }
            });
        }
    </script>

    <script>
        function gridApplicationExemptPersonData(EncryptedId) {
            return {
                EncryptedApplicationId: EncryptedId
            };
        }
    </script>
}