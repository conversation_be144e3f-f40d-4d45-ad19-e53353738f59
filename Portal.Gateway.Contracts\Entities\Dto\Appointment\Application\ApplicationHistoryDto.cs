﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Portal.Gateway.Contracts.Entities.Dto.Appointment.Application
{
    public class ApplicationHistoryDto
    {
        public List<ApplicationHistory> ApplicationHistories { get; set; }

        public class ApplicationHistory
        {
            public int ApplicationHistoryId { get; set; }

            public string PropertyName { get; set; }

            public string PropertyNameDetail { get; set; }

            public string PreviousValue { get; set; }

            public string PreviousValueDetail { get; set; }

            public string CurrentValue { get; set; }

            public string CurrentValueDetail { get; set; }

            public int CreatedBy { get; set; }

            public string CreatedByNameSurname { get; set; }

            public DateTimeOffset CreatedAt { get; set; }
        }
    }
}
