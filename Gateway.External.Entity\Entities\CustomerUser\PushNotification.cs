﻿using System;
using System.Collections.Generic;

namespace Gateway.External.Entity.Entities.CustomerUser
{
    public class PushNotification 
    {
        public PushNotification()
        {
            PushNotificationHistories = new List<PushNotificationHistory>();
            PushNotificationTranslations = new List<PushNotificationTranslation>();
        }
        public int Id { get; set; }
        public string Title { get; set; }
        public byte StatusId { get; set; }
        public DateTime? ScheduledAt { get; set; }
        public DateTime? SendAt { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? UserId { get; set; }
        public int? SendBy { get; set; }
        public int? LastSentLocationId { get; set; }
        public int? LastSentNationalityId { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public virtual ICollection<PushNotificationHistory> PushNotificationHistories { get; set; }
        public virtual ICollection<PushNotificationTranslation> PushNotificationTranslations { get; set; }
    }
}
