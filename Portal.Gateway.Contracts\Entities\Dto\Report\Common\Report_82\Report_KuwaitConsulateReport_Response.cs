﻿using System.Collections.Generic;
using System;

namespace Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_82;
public class Report_KuwaitConsulateReport_Response
{
    public IEnumerable<Branch> Branches { get; set; }

    public class Branch
    {
        public string BranchName { get; set; }

        public IEnumerable<ScanCycleStatus> Data { get; set; }

        public class ScanCycleStatus
        {
            public string Name { get; set; }

            public IEnumerable<ScanCycleApplications> Applications { get; set; }

            public IEnumerable<VisaExtraFee> VisaExtraFees { get; set; }
        }

        public class ScanCycleApplications
        {
            public int OrderNo { get; set; }

            public int ApplicationId { get; set; }

            public string ApplicationNumber { get; set; }

            public DateTimeOffset ApplicationTime { get; set; }

            public string PassportNumber { get; set; }

            public string Name { get; set; }

            public string Surname { get; set; }

            public string NationalityCode { get; set; }

            public string StaffName { get; set; }

            public string StaffSurname { get; set; }

            public int VisaCategoryId { get; set; }
            public bool VIPCheck { get; set; }

            public decimal Price { get; set; }

            public string VisaNo { get; set; }

            public string VisaExtraFeeName { get; set; }

            public string SendToEmbassyStatusDate { get; set; }
            public int ApplicationTypeId { get; set; }
        }

        public class VisaExtraFee
        {
            public string Name { get; set; }
            public int Quantity { get; set; }
            public decimal Price { get; set; }
            public int Currency { get; set; }
            public decimal TotalPrice { get; set; }
        }
    }
}

#region Records for Report Data

public record KuwaitConsulateReportApplicationReportData
{
    public int BranchId { get; init; }
    public int ApplicationId { get; init; }
    public string ApplicationNumber { get; init; }
    public DateTime ApplicationTime { get; init; }
    public int VisaCategoryId { get; init; }
    public string PassportNumber { get; init; }
    public string Name { get; init; }
    public string Surname { get; init; }
    public string ISO2 { get; init; }
    public bool VIPCheck { get; init; }
    public IEnumerable<decimal> Price { get; init; }
    public string VisaNo { get; init; }
    public List<KuwaitConsulateReportApplicationStatusHistoryData> History { get; init; }
    public List<KuwaitConsulateReportExtraFeeData> ExtraFees { get; init; }
    public string SendToEmbassyStatusDate { get; init; }
    public int ApplicationTypeId { get; init; }
}

public record KuwaitConsulateReportApplicationStatusHistoryData
{
    public int UserId { get; init; }
    public int ApplicationStatusId { get; init; }
    public DateTimeOffset CreatedAt { get; init; }
    public bool IsActive { get; init; }
}

public record KuwaitConsulateReportExtraFeeData
{
    public int ExtraFeeId { get; init; }
    public string Name { get; init; }
    public decimal Price { get; init; }
    public int CurrencyId { get; init; }
}

#endregion