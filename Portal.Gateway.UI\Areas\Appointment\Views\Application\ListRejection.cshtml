﻿@{
	ViewData["Title"] = @SiteResources.RejectionSummaryPage.ToTitleCase();
}

@model FilterApplicationViewModel

@Html.HiddenFor(p => p.FilterAllBranchs)
@for (int i = 0; i < Model.FilterStatusHistoryStatusIds.Count; i++)
{
	@Html.HiddenFor(model => Model.FilterStatusHistoryStatusIds[i])
}
<form id="report-form" class="card card-custom card-stretch" method="post">
	<div class="card card-custom card-stretch">
		<div class="card-header">
			<div class="card-title">
				<h3 class="card-label">
					@SiteResources.RejectionSummaryPage.ToTitleCase()
				</h3>
			</div>
			<div class="card-toolbar">
				<div class="btn-group">
					<button type="submit" class="btn btn-outline-primary font-weight-bold mr-2" asp-action="GetApplicationsVisaRejectionReport" asp-area="Appointment" asp-controller="Application">
						<i class="la la-list-alt"></i> @SiteResources.ExcelExport.ToTitleCase()
					</button>
				</div>
			</div>
		</div>
		
		<div class="card-body">
			<div class="accordion accordion-toggle-arrow mb-3">
				<div class="card">
					<div class="card-header">
						<div class="card-title" data-toggle="collapse" data-target="#filtering-options">
							<i class="la la-filter"></i> @SiteResources.FilteringOptions.ToTitleCase()
						</div>
					</div>
					<div id="filtering-options" class="collapse">
						<div class="card-body">
							<div class="form-group row">
								<div class="col-lg-3 col-md-6">
									<label class="font-weight-bold">@SiteResources.ApplicationNumber.ToTitleCase()</label>
									<input type="text" asp-for="FilterApplicationNumber" class="form-control" />
								</div>
								<div class="col-lg-3 col-md-6">
									<label class="font-weight-bold">@SiteResources.Country.ToTitleCase()</label>
									@(Html.Kendo().DropDownListFor(m => m.FilterCountryId)
										.HtmlAttributes(new { @class = "form-control" })
										.Filter(FilterType.Contains)
										.OptionLabel(SiteResources.Select)
										.DataTextField("Text")
										.DataValueField("Value")
										.DataSource(source =>
										{
											source.Read(read =>
											{
												read.Action("GetCachedCountrySelectList", "Parameter", new { Area = "" });
											});
										}))
								</div>
								<div class="col-lg-3 col-md-6">
									<label class="font-weight-bold">@SiteResources.ResidingCountry.ToTitleCase()</label>
									@(Html.Kendo().DropDownListFor(m => m.FilterResidingCountryId)
										.HtmlAttributes(new { @class = "form-control" })
										.Filter(FilterType.Contains)
										.OptionLabel(SiteResources.Select)
										.DataTextField("Text")
										.DataValueField("Value")
										.DataSource(source =>
										{
											source.Read(read =>
											{
												read.Action("GetCachedCountrySelectList", "Parameter", new { Area = "" });
											});
										}))
								</div>
								<div class="col-lg-3 col-md-6">
									<label class="font-weight-bold">@SiteResources.Branch.ToTitleCase()</label>
									@(Html.Kendo().DropDownListFor(m => m.FilterBranchId)
										.HtmlAttributes(new { @class = "form-control" })
										.Filter(FilterType.Contains)
										.OptionLabel(SiteResources.Select)
										.DataTextField("Text")
										.DataValueField("Value")
										.DataSource(source =>
										{
											source.Read(read =>
											{
												read.Action("GetAuthorizedBranchSelectList", "Parameter", new { Area = "" });
											});
										}))
								</div>
							</div>
							<div class="form-group row">
								<div class="col-lg-3 col-md-12">
									<label class="font-weight-bold">@SiteResources.RejectedStartDate.ToTitleCase()</label>
									@(Html.Kendo().DatePickerFor(m => m.FilterRejectionStartDate).Format(SiteResources.DatePickerFormatView))
								</div>
								<div class="col-lg-3 col-md-12">
									<label class="font-weight-bold">@SiteResources.RejectedEndDate.ToTitleCase()</label>
									@(Html.Kendo().DatePickerFor(m => m.FilterRejectionEndDate).Format(SiteResources.DatePickerFormatView))
								</div>
								<div class="col-lg-3 col-md-12">
									<label class="font-weight-bold">@SiteResources.ApplicationDate.ToTitleCase()</label>
									@(Html.Kendo().DatePickerFor(m => m.FilterApplicationDate).Format(SiteResources.DatePickerFormatView))
								</div>
								<div class="col-lg-3 col-md-6">
									<label class="font-weight-bold">@SiteResources.PassportNumber.ToTitleCase()</label>
									<input type="text" asp-for="FilterPassportNumber" class="form-control" />
								</div>
							</div>
							<div class="form-group row">
								<div class="col-lg-3 col-md-6">
									<label class="font-weight-bold">@SiteResources.Name.ToTitleCase()</label>
									<input type="text" asp-for="FilterName" class="form-control" />
								</div>
								<div class="col-lg-3 col-md-6">
									<label class="font-weight-bold">@SiteResources.Surname.ToTitleCase()</label>
									<input type="text" asp-for="FilterSurname" class="form-control" />
								</div>
								<div class="col-lg-3 col-md-6">
									<label class="font-weight-bold">@SiteResources.Nationality.ToTitleCase()</label>
									@(Html.Kendo().DropDownListFor(m => m.FilterNationalityId)
										.HtmlAttributes(new { @class = "form-control" })
										.Filter(FilterType.Contains)
										.OptionLabel(SiteResources.Select)
										.DataTextField("Text")
										.DataValueField("Value")
										.DataSource(source =>
										{
											source.Read(read =>
											{
												read.Action("GetCachedCountrySelectList", "Parameter", new { Area = "" });
											});
										}))
								</div>
								<div class="col-lg-3 col-md-6">
									<label class="font-weight-bold">@SiteResources.PhoneNumber.ToTitleCase()</label>
									<input type="text" asp-for="FilterPhoneNumber" class="form-control" />
								</div>
							</div>
							<div class="form-group row">
								<div class="col-lg-3 col-md-6">
									<label class="font-weight-bold">@SiteResources.VisaCategory.ToTitleCase()</label>
									@(Html.Kendo().DropDownListFor(m => m.FilterVisaCategoryId)
										.HtmlAttributes(new { @class = "form-control" })
										.Filter(FilterType.Contains)
										.OptionLabel(SiteResources.Select)
										.DataTextField("Text")
										.DataValueField("Value")
										.DataSource(source =>
										{
											source.Read(read =>
											{
												read.Action("GetVisaCategoryTypeByDefinitionSelectList", "Parameter", new { Area = "" });
											});
										}))
								</div>
								<div class="col-lg-3 col-md-6">
									<label class="font-weight-bold">@SiteResources.Agency.ToTitleCase()</label>
									@(Html.Kendo().DropDownListFor(m => m.FilterAgencyId)
										.HtmlAttributes(new { @class = "form-control" })
										.Filter(FilterType.Contains)
										.OptionLabel(SiteResources.Select)
										.DataTextField("Text")
										.DataValueField("Value")
										.DataSource(source =>
										{
											source.Read(read =>
											{
												read.Action("GetAgencySelectList", "Parameter", new { Area = "" });
											});
										}))
								</div>
								<div class="col-lg-3" col-md-6">
									<label class="font-weight-bold">@SiteResources.VerificationType.ToTitleCase()</label>
									@(Html.Kendo().DropDownListFor(m => m.FilterVerificationTypeId)
										.HtmlAttributes(new { @class = "form-control" })
										.Filter(FilterType.Contains)
										.OptionLabel(SiteResources.Select)
										.DataTextField("Text")
										.DataValueField("Value")
										.DataSource(source =>
										{
											source.Read(read =>
											{
												read.Action("GetVerificationTypeList", "Parameter", new { Area = "" });
											});
										}))
								</div>
							</div>
							<div class="form-group row">
								<div class="col-lg-6">
									<div>
										<button id="filterGridApplication" type="button" class="btn btn-primary mr-1"><i class="la la-search"></i>@SiteResources.Filter</button>
										<button id="clearGridApplicationFilter" type="reset" class="btn btn-secondary mr-1"><i class="la la-times"></i>@SiteResources.Clear</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="form-group row">
				<div class="col-lg-12">
					@(Html.Kendo().Grid<ApplicationViewModel>()
						.Name("gridApplication")
						.Columns(columns =>
						{
							columns.Bound(o => o.EncryptedId).Visible(false).Exportable(false);
							columns.Bound(o => o.ApplicationNumber).Title(SiteResources.ApplicationNumber.ToTitleCase());
							columns.Bound(o => o.PassportNumber).Title(SiteResources.PassportNumber.ToTitleCase()).Media("lg");
							columns.Bound(o => o.ApplicationTimeForListRejection).Title(SiteResources.ApplicationTime.ToTitleCase()).Media("lg");
							columns.Bound(o => o.ResidingCountryName).Title(SiteResources.ResidingCountry.ToTitleCase()).Media("lg");
							columns.Bound(o => o.BranchName).ClientTemplate("#=BranchName#(#=CountryName#)").Title(SiteResources.BranchName.ToTitleCase()).Media("lg");
							columns.Bound(o => o.Name).Title(SiteResources.Name.ToTitleCase()).Media("lg");
							columns.Bound(o => o.Surname).Title(SiteResources.Surname.ToTitleCase()).Media("lg");
							columns.Bound(o => o.Insurance.Number).Title(SiteResources.PolicyNumber.ToTitleCase()).Media("lg");
							columns.Bound(o => o.ApplicationStatusRejectionDateForListRejection).Title(SiteResources.RejectedDate.ToTitleCase()).Media("lg");
							columns.Bound(o => o.ExtraGatewayServiceFeeRefundDate).Title(SiteResources.RefundDate.ToTitleCase()).Media("lg");
							columns.Bound(o => o.VerificationType).Title(SiteResources.VerificationType.ToTitleCase()).Media("lg");
							columns.Bound(o => o.GatewayServiceFeeName).Title(SiteResources.RefundedFeeName.ToTitleCase()).Media("lg");
							columns.Bound(o => o.GatewayServicePrice).Title(SiteResources.RefundedFeeAmount.ToTitleCase()).Media("lg");
							columns.Bound(o => o.VisaFeeName).Title(SiteResources.RefundedFeeName.ToTitleCase()).Media("lg");
							columns.Bound(o => o.VisaPrice).Title(SiteResources.RefundedFeeAmount.ToTitleCase()).Media("lg");
							columns.Bound(o => o.GatewayServiceFeeCurrency).Visible(false).Exportable(false);
							columns.Bound(o => o.ClaimNo).Title(SiteResources.ClaimNo.ToTitleCase()).Width(120);
							columns.Bound(o => o.EncryptedId).ClientTemplateId("gridOperationsTemplateInsurance").Title(SiteResources.Insurance).Width(70).Exportable(false);
							columns.Bound(o => o.Insurance.StartDate).Title(SiteResources.InsuranceStartDate.ToTitleCase()).Media("lg");
							columns.Bound(o => o.Insurance.EndDate).Title(SiteResources.InsuranceEndDate.ToTitleCase()).Media("lg");
							columns.Bound(o => o.EncryptedId).ClientTemplateId("gridOperationsTemplate").Title(SiteResources.Operations).Width(95).Exportable(false);
							columns.Bound(o => o.SendVisaRejection).Visible(false).Exportable(false);
						})
						.DataSource(dataSource => dataSource
						.Ajax()
						.Read(read => read.Action("GetPaginatedApplications", "Application", new { Area = "Appointment" }).Data("gridApplicationFilterData"))
						.PageSize(10)
						)
						.Scrollable(s => s.Height("auto"))
						.ClientDetailTemplateId("gridOperationsTemplateFile")
						.Pageable(pager => pager
						.Refresh(true)
						.PageSizes(new[] { 10, 20, 50 }))
						.Events(events => events.DataBound("dataBound"))
						)
				</div>
			</div>
		</div>
	</div>
</form>
<div class="modal fade" id="modalApplicationStatusHistory" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
	<div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title"></h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<i aria-hidden="true" class="ki ki-close"></i>
				</button>
			</div>
			<div class="modal-body">
				<div id="divPartialApplicationStatusHistory"></div>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modalCreateInsurancePolicy" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
	<div class="modal-dialog modal-dialog-scrollable modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">@SiteResources.PrintInsurancePolicy.ToTitleCase()</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<i aria-hidden="true" class="ki ki-close"></i>
				</button>
			</div>
			<div class="modal-body ignore-empty">
				<div class="form-group row">
					<div class="col-lg-3 col-md-6">
						<label class="font-weight-bold">@SiteResources.PolicyLanguage.ToTitleCase()</label>
						@(Html.Kendo().DropDownList()
							.Name("PolicyLanguageId")
							.HtmlAttributes(new { @class = "form-control" })
							.OptionLabel(SiteResources.Select)
							.DataTextField("Text")
							.DataValueField("Value")
							.DataSource(source =>
							{
								source.Read(read =>
								{
									read.Action("GetPolicyLanguageTypeSelectList", "Parameter", new { Area = "" });
								});
							}))
					</div>
					<div class="col-lg-3 col-md-6 p-1" style="align-self: flex-end;">
						<input type="hidden" id="create-policy" data-encryptedid="" />
						<button type="button" onclick="redirectToPolicy()" class="btn btn-primary font-weight-bold">@SiteResources.DisplayPolicy.ToTitleCase()</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

@section Scripts {
	<script src="~/js/Appointment/Application/list-rejection.js"></script>

	<script id="gridOperationsTemplate" type="text/x-kendo-tmpl">
		<div class="btn-group">
			<button class="btn btn-sm btn-light-primary font-weight-bold btn-sm dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-placement="right">
		@Html.Raw(SiteResources.Select)
			</button>
			<div class="dropdown-menu dropdown-menu-right">
				<a class='dropdown-item' href='/Appointment/Application/DetailApplicationSummary?encryptedApplicationId=#=EncryptedId#' target="_blank">@Html.Raw(SiteResources.ApplicationDetails.ToTitleCase())</a>
				#if(SendVisaRejection){#
					<a class='dropdown-item' href='javascript:void(0);' onclick="claimLossSend('#=EncryptedId#','#=GatewayServicePrice#','#=VisaPrice#','#=GatewayServiceFeeCurrency#','#=ClaimNo#');">@Html.Raw(SiteResources.OpenToEmaaLossClaimEnty.ToTitleCase())</a>
				#}else{#
					<a class='dropdown-item' href='javascript:void(0);' onclick="sendVisaRejection('#=EncryptedId#','#=GatewayServicePrice#','#=VisaPrice#','#=GatewayServiceFeeCurrency#');">@Html.Raw(SiteResources.ExportToSAP.ToTitleCase())</a>
				#}#
			</div>
		</div>
	</script>

	<script id="gridOperationsTemplateInsurance" type="text/x-kendo-tmpl">
		<div class="btn-group">
			<a class='font-weight-bold btn btn-link-info' data-toggle='modal' data-target='\\#modalCreateInsurancePolicy' href='javascript:void(0);' onclick="partialCertificateInsurance('#=EncryptedId#');">@Html.Raw(SiteResources.Policy.ToTitleCase())</a>
		</div>
	</script>

	<script id="gridOperationsTemplateFile" type="text/x-kendo-tmpl">
		@(Html.Kendo().Grid<ApplicationFileViewModel>()
								.Name("gridApplicationFile_#=EncryptedId#")
								.Columns(columns =>
								{
									columns.Bound(o => o.EncryptedId).Visible(false);
									columns.Bound(o => o.FileType).Title(SiteResources.ApplicationFileType.ToTitleCase()).Media("md");
									columns.Bound(o => o.OriginalFileName).Title(SiteResources.FileName.ToTitleCase());
									columns.Bound(o => o.Note).Title(SiteResources.Notes.ToTitleCase());
									columns.Bound(o => o.CreatedAt).Title(SiteResources.CreatedAt.ToTitleCase());
									columns.Bound(o => o.CreatedBy).Title(SiteResources.CreatedBy.ToTitleCase());
									columns.Bound(o => o.EncryptedDocumentId).ClientTemplateId("gridApplicationFileOperationsTemplate").Title(SiteResources.Operations).Width(150);
								})
								.DataSource(dataSource => dataSource
									.Ajax()
									.Read(read => read.Action("GetPaginatedApplicationFiles", "Application", new { Area = "Appointment" , IsRejectionList = true}).Data("gridApplicationFileData('#=EncryptedId#')"))
									.PageSize(10)
								)
								.Scrollable(s => s.Height("auto"))
								.ToClientTemplate()
								)
	</script>

	<script id="gridApplicationFileOperationsTemplate" type="text/x-kendo-tmpl">
		<div class="btn-group">
			<a class='font-weight-bold btn btn-link-info' href='/Appointment/Application/DownloadFile?encryptedDocumentId=#=EncryptedDocumentId#' target='_blank'>@Html.Raw(SiteResources.Download)</a>
		</div>
	</script>
	<script>
		$('#filtering-options').on('keyup', function (e) {
			if (e.key == "Enter") $('#filterGridApplication').click();
			if (e.key == "Escape") $('#clearGridApplicationFilter').click();
		});

		function dataBound() {
			this.expandRow(this.tbody.find("tr.k-master-row td").eq(3));
		}
	</script>
}

<style>
	.k-grid-toolbar {
		justify-content: flex-end;
	}

	.k-grid .k-alt.k-state-selected {
		background-color: #a3edf6 !important;
		color: #000000;
	}
</style>