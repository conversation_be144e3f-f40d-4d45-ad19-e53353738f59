{"profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_HTTPS_PORT": "44322", "ASPNETCORE_ENVIRONMENT": "Local"}}, "Portal.Gateway.Api-Local": {"commandName": "Project", "launchBrowser": true, "launchUrl": "http://localhost:5000/swagger", "environmentVariables": {"ASPNETCORE_URLS": "http://localhost:5000", "ASPNETCORE_ENVIRONMENT": "Local"}}, "Portal.Gateway.Api-Development": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "http://localhost:5000/"}, "Portal.Gateway.Api-Staging": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Staging"}, "applicationUrl": "http://localhost:5000/"}, "Portal.Gateway.Api-Production": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production"}, "applicationUrl": "http://localhost:5000/"}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "publishAllPorts": true}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:5000", "sslPort": 0}}}