﻿using AutoMapper;
using Gateway.Core.CustomAttributes;
using Gateway.Extensions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Portal.Gateway.Api.Processors;
using Portal.Gateway.Api.Processors.Dto;
using Portal.Gateway.ApiModel;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Appointment.Application;
using Portal.Gateway.ApiModel.Requests.Appointment.ApplicationStatus;
using Portal.Gateway.ApiModel.Requests.Appointment.ApplicationStatusOrder;
using Portal.Gateway.ApiModel.Requests.Appointment.BranchStatusEmail;
using Portal.Gateway.ApiModel.Requests.Appointment.BranchStatusSms;
using Portal.Gateway.ApiModel.Requests.Appointment.ExtraFee;
using Portal.Gateway.ApiModel.Requests.Appointment.PhotoBooth;
using Portal.Gateway.ApiModel.Requests.Appointment.PreApplication;
using Portal.Gateway.ApiModel.Requests.General;
using Portal.Gateway.ApiModel.Requests.Insurance;
using Portal.Gateway.ApiModel.Requests.Insurance.EmaaHasarServisExcel;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.Appointment.ApplicationStatus;
using Portal.Gateway.ApiModel.Responses.Appointment.BranchStatusEmail;
using Portal.Gateway.ApiModel.Responses.Appointment.BranchStatusSms;
using Portal.Gateway.ApiModel.Responses.Appointment.ExtraFee;
using Portal.Gateway.ApiModel.Responses.Appointment.PhotoBooth;
using Portal.Gateway.ApiModel.Responses.Appointment.PreApplication;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Insurance;
using Portal.Gateway.ApiModel.Responses.Management.ApplicationFormElement;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Contracts.Entities;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Dto;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Application;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Application.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Application.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.ApplicationStatus.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.ApplicationStatus.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.BranchStatusEmail.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.BranchStatusEmail.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.BranchStatusSms.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.BranchStatusSms.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.ExtraFee.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.ExtraFee.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Insurance;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.KsaIcr;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.PhotoBooth.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.PhotoBooth.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.PreApplication.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.PreApplication.Responses;
using Portal.Gateway.Contracts.Entities.Dto.General;
using Portal.Gateway.Contracts.Entities.Dto.General.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Insurance;
using Portal.Gateway.Contracts.Entities.Dto.Insurance.EmaaHasarServisExcel.Request;
using Portal.Gateway.Contracts.Entities.Dto.Management.ApplicationFormElement.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.Branch.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.Branch.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.User.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.User.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Notification;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Extensions;
using Portal.Gateway.Contracts.Services;
using Portal.Gateway.Contracts.Settings.AppSettings;
using Portal.Gateway.ExternalServices.Contracts;
using Portal.Gateway.ExternalServices.Models;
using Portal.Gateway.Resources;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Portal.Gateway.Common.Utility.Extensions;
using Microsoft.Extensions.Hosting.Internal;
using Portal.Gateway.ApiModel.Requests.Appointment.MainExtraFeeCategory;
using Portal.Gateway.ApiModel.Requests.Appointment.VisaType;
using Portal.Gateway.ApiModel.Responses.Appointment.MainExtraFeeCategory;
using Portal.Gateway.ApiModel.Responses.Appointment.VisaType;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.MainExtraFeeCategory.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.VisaType.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.VisaType.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.MainExtraFeeCategory.Responses;

namespace Portal.Gateway.Api.Controllers
{
    public class AppointmentController : BaseController<AppointmentController>
    {
        private readonly IAppointmentService _appointmentService;
        private readonly IEmailService _emailService;
        private readonly ISmsService _smsService;
        private readonly INotificationService _notificationService;
        private readonly IManagementService _managementService;
        private readonly IUserService _userService;

        public AppointmentController(
                IManagementService managementService,
                INotificationService notificationService,
                IEmailService emailService,
                ISmsService smsService,
                IAppointmentService appointmentService,
                IOptions<AppSettings> appSettings,
                IWebHostEnvironment hostingEnvironment,
                ILogger<AppointmentController> logger,
                IMapper mapper,
                IUserService userService) : base(appSettings, hostingEnvironment, logger, mapper)
        {
            _emailService = emailService;
            _smsService = smsService;
            _appointmentService = appointmentService;
            _notificationService = notificationService;
            _managementService = managementService;
            _userService = userService;
        }

        #region ExtraFee

        [SwaggerOperation(
            Summary = "Provides client/s to add new extra fee",
            Description = "Extra fee name must be unique, please check existing Extra Fees on before adding new one.")]
        [HttpPost(ApiMethodName.Appointment.AddExtraFee)]
        public Task<ApiResponse<AddApiResponse>> AddExtraFee([FromBody] AddExtraFeeApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.AddExtraFeeAsync(Mapper.Map<AddExtraFeeRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Update an existing Extra Fee",
            Description = "Extra fee name must be unique, please check existing Extra Fees on before updating with existing one.")]
        [HttpPut(ApiMethodName.Appointment.UpdateExtraFee)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateExtraFee([FromBody] UpdateExtraFeeApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.UpdateExtraFeeAsync(Mapper.Map<UpdateExtraFeeRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Get Extra Fee by Extra Fee id",
            Description = "Retrieve existing Extra Fee by id, returns name and id of Extra Fee.")]
        [HttpGet(ApiMethodName.Appointment.GetExtraFee + "{id}")]
        public Task<ApiResponse<ExtraFeeApiResponse>> GetExtraFee(int id)
        {
            return Process<ExtraFeeApiResponse, ExtraFeeResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetExtraFeeAsync(id));
        }

        [SwaggerOperation(
            Summary = "Delete existing Extra Fee",
            Description = "Delete Extra Fee by id, if not found Extra Fee, it throws not found exception")]
        [HttpDelete(ApiMethodName.Appointment.DeleteExtraFee + "{id}")]
        public Task<ApiResponse<DeleteApiResponse>> DeleteExtraFee(int id)
        {
            return Process<DeleteApiResponse, DeleteResponseDto>(new EmptyApiRequest(), () => _appointmentService.DeleteExtraFeeAsync(id));
        }

        [SwaggerOperation(
            Summary = "Get Extra Fees with paginated",
            Description = "Retrieve paginated Extra Fees")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedExtraFees)]
        public Task<ApiResponse<PaginationApiResponse<ExtraFeesApiResponse>>> GetPaginatedExtraFees([FromBody] PaginatedExtraFeesApiRequest request)
        {
            return Process<PaginationApiResponse<ExtraFeesApiResponse>, Pagination<ExtraFeesResponseDto>>(request, () => _appointmentService.GetPaginatedExtraFeesAsync(Mapper.Map<PaginatedExtraFeesRequestDto>(request)));
        }

        #endregion

        #region ApplicationStatus

        [SwaggerOperation(
            Summary = "Provides client/s to add new extra fee",
            Description = "Extra fee name must be unique, please check existing Extra Fees on before adding new one.")]
        [HttpPost(ApiMethodName.Appointment.AddApplicationStatus)]
        public Task<ApiResponse<AddApiResponse>> AddApplicationStatus([FromBody] AddApplicationStatusApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.AddApplicationStatusAsync(Mapper.Map<AddApplicationStatusRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Update an existing Extra Fee",
            Description = "Extra fee name must be unique, please check existing Extra Fees on before updating with existing one.")]
        [HttpPut(ApiMethodName.Appointment.UpdateApplicationStatus)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateApplicationStatus([FromBody] UpdateApplicationStatusApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.UpdateApplicationStatusAsync(Mapper.Map<UpdateApplicationStatusRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Get Extra Fee by Extra Fee id",
            Description = "Retrieve existing Extra Fee by id, returns name and id of Extra Fee.")]
        [HttpGet(ApiMethodName.Appointment.GetApplicationStatus + "{id}")]
        public Task<ApiResponse<ApplicationStatusApiResponse>> GetApplicationStatus(int id)
        {
            return Process<ApplicationStatusApiResponse, ApplicationStatusResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetApplicationStatusAsync(id));
        }

        [SwaggerOperation(
            Summary = "Delete existing Extra Fee",
            Description = "Delete Extra Fee by id, if not found Extra Fee, it throws not found exception")]
        [HttpDelete(ApiMethodName.Appointment.DeleteApplicationStatus + "{id}")]
        public Task<ApiResponse<DeleteApiResponse>> DeleteApplicationStatus(int id)
        {
            var request = new DeleteApiRequest { Id = id };

            return Process<DeleteApiResponse, DeleteResponseDto>(request, () => _appointmentService.DeleteApplicationStatusAsync(Mapper.Map<DeleteRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Get Extra Fees with paginated",
            Description = "Retrieve paginated Extra Fees")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedApplicationStatus)]
        public Task<ApiResponse<PaginationApiResponse<PaginatedApplicationStatusApiResponse>>> GetPaginatedApplicationStatuses([FromBody] PaginatedApplicationStatusApiRequest request)
        {
            return Process<PaginationApiResponse<PaginatedApplicationStatusApiResponse>, Pagination<PaginatedApplicationStatusResponseDto>>(request, () => _appointmentService.GetPaginatedApplicationStatusAsync(Mapper.Map<PaginatedApplicationStatusRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Update an existing status order of an application status",
            Description = "Please check existing application status id on before updating with existing one.")]
        [HttpPut(ApiMethodName.Appointment.UpdateApplicationStatusOrder)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateApplicationStatusOrder([FromBody] UpdateApplicationStatusOrderApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.UpdateApplicationStatusOrderAsync(Mapper.Map<UpdateApplicationStatusOrderRequestDto>(request)));
        }
        [SwaggerOperation(
           Summary = "Update an existing incorrect status active or pasive",
           Description = "Update an existing incorrect status active or pasive.")]
        [HttpPut(ApiMethodName.Appointment.UpdateIncorrectApplicationStatus)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateIncorrectApplicationStatus([FromBody] UpdateIncorrectApplicationStatusApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.UpdateIncorrectApplicationStatusAsync(Mapper.Map<UpdateIncorrectApplicationStatusRequestDto>(request)));
        }

        #endregion

        #region BranchStatusSms

        [SwaggerOperation(
            Summary = "Get all Sms data of Branch Status by Branch id",
            Description = "Retrieve existing Branch Status Sms by id, returns all existing data.")]
        [HttpGet(ApiMethodName.Appointment.GetBranchStatusSms + "{branchId}")]
        public Task<ApiResponse<BranchStatusSmsApiResponse>> GetBranchStatusSms(int branchId)
        {
            var request = new BranchStatusSmsApiRequest { BranchId = branchId };

            return Process<BranchStatusSmsApiResponse, BranchStatusSmsResponseDto>(request, () => _appointmentService.GetBranchStatusSmsAsync(Mapper.Map<BranchStatusSmsRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Update all Branch Status Sms data with valid texts",
            Description = "Please check existing branch application status id on before updating with existing one.")]
        [HttpPut(ApiMethodName.Appointment.UpdateBranchStatusSms)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateBranchStatusSms([FromBody] UpdateBranchStatusSmsApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.UpdateBranchStatusSmsAsync(Mapper.Map<UpdateBranchStatusSmsRequestDto>(request)));
        }

        #endregion

        #region BranchStatusEmail

        [SwaggerOperation(
            Summary = "Get all Email data of Branch Status by Branch id",
            Description = "Retrieve existing Branch Status Email by id, returns all existing data.")]
        [HttpGet(ApiMethodName.Appointment.GetBranchStatusEmail + "{branchId}")]
        public Task<ApiResponse<BranchStatusEmailApiResponse>> GetBranchStatusEmail(int branchId)
        {
            var request = new BranchStatusEmailApiRequest { BranchId = branchId };

            return Process<BranchStatusEmailApiResponse, BranchStatusEmailResponseDto>(request, () => _appointmentService.GetBranchStatusEmailAsync(Mapper.Map<BranchStatusEmailRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Update all Branch Status Email data with valid texts",
            Description = "Please check existing branch application status id on before updating with existing one.")]
        [HttpPut(ApiMethodName.Appointment.UpdateBranchStatusEmail)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateBranchStatusEmail([FromBody] UpdateBranchStatusEmailApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.UpdateBranchStatusEmailAsync(Mapper.Map<UpdateBranchStatusEmailRequestDto>(request)));
        }

        #endregion

        #region Application

        [SwaggerOperation(
            Summary = "Provides clients to get application details",
            Description = "Get application details by id and language option.")]
        [HttpGet(ApiMethodName.Appointment.GetApplicationsWithExemptInsuranceRelation + "{id}")]
        public Task<ApiResponse<ApplicationsWithExemptInsuranceRelationApiResponse>> GetApplicationsWithExemptInsuranceRelation(int id)
        {
            var request = new ApplicationApiRequest { Id = id };

            return Process<ApplicationsWithExemptInsuranceRelationApiResponse, GetApplicationsWithExemptInsuranceRelationResponseDto>(request, () => _appointmentService.GetApplicationsWithExemptInsuranceRelationAsync(Mapper.Map<ApplicationRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to not requirement PassportExpireDate",
            Description = "Prevents to control PassportExpireDate requirement")]
        [HttpGet(ApiMethodName.Appointment.IsRequired + "{branchApplicationCountryId}")]
        public Task<ApiResponse<ApplicationFormElementsApiResponse>> IsRequired(int branchApplicationCountryId)
        {
            return Process<ApplicationFormElementsApiResponse, ApplicationFormElementsResponseDto>(new EmptyApiRequest(), () => _appointmentService.IsRequiredAsync(branchApplicationCountryId));
        }

        [SwaggerOperation(
            Summary = "Provides clients to add new non application yss",
            Description = "-")]
        [HttpPost(ApiMethodName.Appointment.AddNonApplicationYss)]
        public Task<ApiResponse<AddApiResponse>> AddNonApplicationYss([FromBody] AddNonApplicationYssApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.AddNonApplicationYssAsync(Mapper.Map<AddNonApplicationYssRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Update application status with given selected status type",
            Description = "Made for Turkmenistan ihb file status")]
        [HttpPost(ApiMethodName.Appointment.UpdateSelectedApplicationStatusForApplicationFile)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateSelectedApplicationStatusForApplicationFile([FromBody] UpdateSelectedApplicationStatusForApplicationFileApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.UpdateSelectedApplicationStatusForApplicationFile(Mapper.Map<UpdateSelectedApplicationStatusForApplicationFileRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Get Application Sms History",
            Description = "Get Application Sms History")]
        [HttpGet(ApiMethodName.Appointment.GetApplicationSmsHistory + "{applicationId}")]
        public Task<ApiResponse<ApplicationSmsHistoryApiResponse>> GetApplicationSmsHistory(int applicationId)
        {
            var request = new ApplicationSmsHistoryApiRequest { Id = applicationId };
            return Process<ApplicationSmsHistoryApiResponse, ApplicationSmsHistoryDto>(request, () => _appointmentService.GetApplicationSmsHistoryAsync(Mapper.Map<ApplicationSmsHistoryRequestDto>(request)));
        }

        [SwaggerOperation(
          Summary = "Get Application Email History",
          Description = "Get Application Email History")]
        [HttpGet(ApiMethodName.Appointment.GetApplicationEmailHistory + "{applicationId}")]
        public Task<ApiResponse<ApplicationEmailHistoryApiResponse>> GetApplicationEmailHistory(int applicationId)
        {
            var request = new ApplicationEmailHistoryApiRequest { Id = applicationId };
            return Process<ApplicationEmailHistoryApiResponse, ApplicationEmailHistoryDto>(request, () => _appointmentService.GetApplicationEmailHistoryAsync(Mapper.Map<ApplicationEmailHistoryRequestDto>(request)));
        }

        [SwaggerOperation(
           Summary = "Send Confirmation Code Notification",
           Description = "Send Confirmation Code Notification")]
        [HttpPost(ApiMethodName.Appointment.SendConfirmationCodeNotification)]
        public Task<ApiResponse<UpdateApiResponse>> SendConfirmationCodeNotification([FromBody] SendConfirmationCodeNotificationApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateApiResponse>(request, () => this.UpdateApplicationConfirmationCodeAndSendNotifications(request));
        }

        internal async Task<UpdateApiResponse> UpdateApplicationConfirmationCodeAndSendNotifications(SendConfirmationCodeNotificationApiRequest request)
        {
            request.ConfirmationCode = new Random().Next(1000, 9999).ToString();

            var result = await _appointmentService.UpdateApplicationConfirmationCodeAsync(Mapper.Map<UpdateApplicationConfirmationCodeRequestDto>(request)).ConfigureAwait(false);

            if (request.SendNotification)
            {
                var applicationNotificationRequest = new ApplicationNotificationResponseDto()
                {
                    BranchCountryId = result.BranchCountryId,
                    SmsList = string.IsNullOrEmpty(request.PhoneNumber) ? new List<ApplicationNotificationResponseDto.Notification>() : new List<ApplicationNotificationResponseDto.Notification>()
                    {
                        new()
                        {
                            IsSendByPrefix = result.BranchSmsSendByPrefix,
                            Text = result.SmsContent,
                            ApplicantList = new List<ApplicationNotificationResponseDto.Applicant>()
                            {
                                new()
                                {
                                    ProviderId = result.BranchSmsProviderId,
                                    Sender = result.BranchSmsSender,
                                    Contact = request.PhoneNumber,
                                    Id = request.ApplicationId
                                }
                            }
                        }
                    },
                    EmailList = string.IsNullOrEmpty(request.Email) ? new List<ApplicationNotificationResponseDto.Notification>() : new List<ApplicationNotificationResponseDto.Notification>()
                    {
                        new()
                        {
                            Text = result.MailContent,
                            Subject = result.Subject,
                            ApplicantList = new List<ApplicationNotificationResponseDto.Applicant>()
                            {
                                new()
                                {
                                    ProviderId = result.BranchEmailProviderId,
                                    Sender = string.Empty,
                                    Contact = request.Email,
                                    Id = request.ApplicationId
                                }
                            }
                        }
                    },
                };

                await SendNotifications(applicationNotificationRequest, result.FooterLanguageId, request.UserId, (int)NotificationRouteType.ConfirmationCode, (int)NotificationType.ConfirmationCode, result.BranchId, null);
            }

            return new UpdateApiResponse()
            {
                Result = result.Result,
                ConfirmationCode = request.ConfirmationCode
            };
        }

        [SwaggerOperation(
            Summary = "Resend selected sms ",
            Description = "Resend selected sms ")]
        [HttpPost(ApiMethodName.Appointment.ApplicationInsuranceNotification)]
        public Task<ApiResponse<UpdateApiResponse>> ApplicationInsuranceNotification([FromBody] SendInsuranceNotificationApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateApiResponse>(request, () => this.SendInsuranceNotification(request));
        }

        internal async Task<UpdateApiResponse> SendInsuranceNotification(SendInsuranceNotificationApiRequest request)
        {
            if (request.IsNotificationEnabled)
            {
                var notificationContentSendInsurance = await _notificationService.GetNotificationContent((int)NotificationType.InsurancePolicy, request.LanguageId == 1 ? 1 : 2).ConfigureAwait(false);

                if (notificationContentSendInsurance.HasNotificationContent)
                {
                    new NotificationProcessor(_emailService, _smsService, _notificationService)
                       .ProcessNotification(NotificationType.InsurancePolicy, request.LanguageId == 1 ? 1 : 2,
                           new NotificationRequest
                           {
                               Notifications = new List<NotificationRequest.Notification>()
                               {
                                    new()
                                    {
                                        ApplicationNo = request.ApplicationNo,
                                        Attaches =request.PolicyAttachment.Select(s => new Attachment()
                                        {
                                            Content = Convert.ToBase64String(s),
                                            FileName = $"{NotificationType.InsurancePolicy.GetDescription()}.pdf"
                                        }).ToList(),
                                        Email = request.EmailList.Select(r => new NotificationRequest.Applicant
                                        {
                                            Contact = r,
                                            ProviderId = request.BranchEmailProviderId,
                                            Sender = string.Empty
                                        }),
                                        ContentRequest = new NotificationContentRequest()
                                        {
                                            FooterLanguageId = notificationContentSendInsurance.FooterLanguageId,
                                            Subject = notificationContentSendInsurance.Subject,
                                            Content = notificationContentSendInsurance.MailContent //replace
										}
                                    }
                               }
                           });

                    await _appointmentService.AddApplicationEmailHistoryAsync(request.EmailList.Select(r => new AddApplicationEmailHistoryRequestDto()
                    {
                        UserId = request.UserId,
                        EmailHistories = request.EmailList.Select(k => new AddApplicationEmailHistoryRequestDto.ApplicationEmailHistoryDto
                        {
                            ApplicationStatusId = 0,
                            ApplicationId = request.ApplicationNo,
                            IsInsuranceEmail = true
                        }).ToList()
                    }).ToList());
                }


            }

            return await Task.FromResult(new UpdateApiResponse
            {
                Result = true,
            }).ConfigureAwait(false);
        }


        [SwaggerOperation(
            Summary = "Resend selected sms ",
            Description = "Resend selected sms ")]
        [HttpPost(ApiMethodName.Appointment.ResendApplicationSms)]
        public Task<ApiResponse<UpdateApiResponse>> ResendApplicationSms([FromBody] ResendApplicationSmsApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateApiResponse>(request, () => this.ResendApplicationSmsAndSendNotification(request));
        }

        internal async Task<UpdateApiResponse> ResendApplicationSmsAndSendNotification(ResendApplicationSmsApiRequest request)
        {
            if (request.SendNotification)
            {
                var notificationRequest = new GetResendNotificationListRequestDto()
                {
                    BranchId = request.BranchId,
                    ApplicationStatusId = request.ApplicationStatusId,
                    ApplicationId = request.ApplicationId,
                    ApplicationSmsHistoryId = request.SmsId
                };

                var approvedSmsResponse = await _appointmentService.GetResendNotificationListAsync(notificationRequest).ConfigureAwait(false);

                if (approvedSmsResponse.SmsList != null && approvedSmsResponse.SmsList.Any())
                {
                    approvedSmsResponse.BranchCountryId = request.BranchCountryId;
                    approvedSmsResponse.SmsList.First().ResendParentId = request.SmsId;

                    await SendNotifications(approvedSmsResponse, 2, request.UserId, (int)NotificationRouteType.Resend, (int)ServiceNotificationType.Resend, request.BranchId, null);
                }
            }

            return new UpdateApiResponse
            {
                Result = true,
            };
        }

        [SwaggerOperation(
            Summary = "Update application current status",
            Description = "Update application current status.")]
        [HttpPut(ApiMethodName.Appointment.UpdateApplicationCurrentStatus)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateApplicationCurrentStatus([FromBody] UpdateApplicationsCurrentStatusApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateApiResponse>(request, () => this.UpdateApplicationCurrentStatusAndSendNotifications(request));
        }

        internal async Task<UpdateApiResponse> UpdateApplicationCurrentStatusAndSendNotifications(UpdateApplicationsCurrentStatusApiRequest request)
        {
            var result = await _appointmentService.UpdateApplicationCurrentStatusAsync(Mapper.Map<UpdateApplicationsCurrentStatusRequestDto>(request)).ConfigureAwait(false);

            if (!string.IsNullOrEmpty(result.CheckRejectionRefundDoneControlMessage))
                return new UpdateApiResponse { CheckRejectionRefundDoneControlMessage = result.CheckRejectionRefundDoneControlMessage };

            if (!string.IsNullOrEmpty(result.CheckRejectionApprovalControlMessage))
                return new UpdateApiResponse { CheckRejectionApprovalControlMessage = result.CheckRejectionApprovalControlMessage };

            var exceptIds = new List<int>();

            exceptIds.AddRange(result.NotUpdatedCargoIds ?? Enumerable.Empty<int>());
            exceptIds.AddRange(result.NotUpdatedInsuranceRefundIds?.Select(s => s.Key) ?? Enumerable.Empty<int>());
            exceptIds.AddRange(result.NonTurkmenNationIhbStatusIds ?? Enumerable.Empty<int>());
            exceptIds.AddRange(result.NotUpdatedRejectionRefundDoneIds ?? Enumerable.Empty<int>());
            exceptIds.AddRange(result.NotUpdatedTurkmenVisaRejectionRefundDoneIds ?? Enumerable.Empty<int>());

            request.ApplicationCurrentStatus = request.ApplicationCurrentStatus
                .Where(s => !exceptIds.Contains(s.ApplicationId)).ToList();

            result.ApprovedContent.Contents = result.ApprovedContent.Contents
                .Where(s => !exceptIds.Contains(s.ApplicationId)).ToList();

            if (request.SendNotification)
            {
                if (result.ApprovedContent.Contents.Any())
                {
                    var approvedStatusNotificationContent = await _appointmentService.GetApprovedStatusNotificationContentAsync(new GetApprovedStatusNotificationContentRequestDto
                    {
                        BranchId = request.BranchId,
                        StatusUpdateType = result.ApprovedContent.StatusUpdateType,
                        Contents = result.ApprovedContent.Contents.Select(r => new GetApprovedStatusNotificationContentRequestDto.ApprovedNotificationContent
                        {
                            ApplicationIds = new List<int>(),
                            ApplicationId = r.ApplicationId
                        }).ToList()
                    }).ConfigureAwait(false);

                    approvedStatusNotificationContent.BranchCountryId = result.BranchCountryId;

                    if (approvedStatusNotificationContent.SmsList != null && approvedStatusNotificationContent.SmsList.Any())
                        await SendNotifications(approvedStatusNotificationContent, 2, request.UserId, (int)NotificationRouteType.ScanSycle, (int)NotificationType.ScanSycle, request.BranchId, null);
                }

                if (result.IsEsimBeSent)
                {
                    var esimRequest = new GetStatusChangeNotificationRequestDto
                    {
                        BranchId = request.BranchId,
                        ApplicationCurrentStatus = request.ApplicationCurrentStatus
                            .Select(q => new ApplicationCurrentStatusRequestDto
                            {
                                ApplicationStatusId = q.ApplicationStatusId,
                                ApplicationId = q.ApplicationId
                            }).ToList()
                    };

                    var esimResult = await _appointmentService.GetApplicationEsimNotifications(esimRequest);

                    await SendNotifications(esimResult, 2, request.UserId, (int)NotificationRouteType.Esim, (int)NotificationType.Esim, request.BranchId, null);
                }

                if (request.ApplicationCurrentStatus.Any())
                {
                    var statusNotificationRequest = new GetStatusChangeNotificationRequestDto
                    {
                        BranchId = request.BranchId,
                        ApplicationCurrentStatus = request.ApplicationCurrentStatus.Select(q => new ApplicationCurrentStatusRequestDto
                        {
                            ApplicationStatusId = q.ApplicationStatusId,
                            ApplicationId = q.ApplicationId
                        }).ToList()
                    };

                    var statusResponse = await _appointmentService.GetStatusChangeNotificationList(statusNotificationRequest).ConfigureAwait(false);

                    statusResponse.BranchCountryId = result.BranchCountryId;

                    await SendNotifications(statusResponse, 2, request.UserId, (int)NotificationRouteType.ScanSycle, (int)NotificationType.ScanSycle, request.BranchId, null);
                }
            }

            return new UpdateApiResponse
            {
                Result = result.Result,
                NotUpdatedCargoIds = result.NotUpdatedCargoIds,
                NonTurkmenNationIhbStatusIds = result.NonTurkmenNationIhbStatusIds,
                CheckPreviousRefundIsDoneMessage = result.CheckPreviousRefundIsDoneMessage,
                NotUpdatedTurkmenVisaRejectionRefundDoneIds = result.NotUpdatedTurkmenVisaRejectionRefundDoneIds,
                CheckTurkmenRejectionRefundDoneControlMessage = result.CheckTurkmenRejectionRefundDoneControlMessage,
                NotUpdatedInsuranceRefundIds = result.NotUpdatedInsuranceRefundIds,
            };
        }

        [SwaggerOperation(
            Summary = "Update all applications' current status",
            Description = "Update all applications' current status.")]
        [HttpPut(ApiMethodName.Appointment.UpdateAllApplicationCurrentStatus)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateAllApplicationCurrentStatus([FromBody] UpdateAllApplicationsCurrentStatusApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateApiResponse>(request, () => this.UpdateAllApplicationsCurrentStatusAndSendNotifications(request));
        }

        internal async Task<UpdateApiResponse> UpdateAllApplicationsCurrentStatusAndSendNotifications(UpdateAllApplicationsCurrentStatusApiRequest request)
        {
            var result = await _appointmentService.UpdateAllApplicationCurrentStatusAsync(Mapper.Map<UpdateAllApplicationsCurrentStatusRequestDto>(request)).ConfigureAwait(false);

            if (!string.IsNullOrEmpty(result.CheckRejectionApprovalControlMessage))
                return new UpdateApiResponse { CheckRejectionApprovalControlMessage = result.CheckRejectionApprovalControlMessage };

            if (!string.IsNullOrEmpty(result.CheckRejectionRefundDoneControlMessage))
                return new UpdateApiResponse { CheckRejectionRefundDoneControlMessage = result.CheckRejectionRefundDoneControlMessage };

            var exceptIds = new List<int>();

            exceptIds.AddRange(result.NotUpdatedCargoIds ?? Enumerable.Empty<int>());
            exceptIds.AddRange(result.NotUpdatedInsuranceRefundIds?.Select(s => s.Key) ?? Enumerable.Empty<int>());
            exceptIds.AddRange(result.NonTurkmenNationIhbStatusIds ?? Enumerable.Empty<int>());
            exceptIds.AddRange(result.NotUpdatedRejectionRefundDoneIds ?? Enumerable.Empty<int>());
            exceptIds.AddRange(result.NotUpdatedTurkmenVisaRejectionRefundDoneIds ?? Enumerable.Empty<int>());

            result.ApplicationIds = result.ApplicationIds
                .Where(s => !exceptIds.Contains(s)).ToList();

            if (request.SendNotification)
            {
                if (result.ApprovedContent.Contents.Any())
                {
                    var approvedStatusNotificationContent = await _appointmentService.GetApprovedStatusNotificationContentAsync(new GetApprovedStatusNotificationContentRequestDto()
                    {
                        BranchId = request.BranchId,
                        StatusUpdateType = result.ApprovedContent.StatusUpdateType,
                        Contents = result.ApprovedContent.Contents.Select(r => new GetApprovedStatusNotificationContentRequestDto.ApprovedNotificationContent()
                        {
                            ApplicationIds = result.ApplicationIds,
                            ApplicationId = r.ApplicationId,
                        }).ToList()
                    }).ConfigureAwait(false);
                    approvedStatusNotificationContent.BranchCountryId = result.BranchCountryId;

                    if (approvedStatusNotificationContent.SmsList != null && approvedStatusNotificationContent.SmsList.Any())
                        await SendNotifications(approvedStatusNotificationContent, 2, request.UserId, (int)NotificationRouteType.ScanSycle, (int)NotificationType.ScanSycle, request.BranchId, null);
                }

                if (result.IsEsimBeSent)
                {
                    var esimRequest = new GetStatusChangeNotificationRequestDto
                    {
                        BranchId = request.BranchId,
                        ApplicationCurrentStatus = result.ApplicationIds
                            .Select(q => new ApplicationCurrentStatusRequestDto
                            {
                                ApplicationStatusId = request.ApplicationStatusId,
                                ApplicationId = q
                            }).ToList()
                    };

                    var esimResult = await _appointmentService.GetApplicationEsimNotifications(esimRequest);

                    await SendNotifications(esimResult, 2, request.UserId, (int)NotificationRouteType.Esim, (int)NotificationType.Esim, request.BranchId, null);
                }

                if (result.ApplicationIds.Any())
                {
                    var notificationRequest = new GetAllStatusChangeNotificationRequestDto()
                    {
                        BranchId = request.BranchId,
                        ApplicationStatusId = request.ApplicationStatusId,
                        StartDate = request.StartDate,
                        EndDate = request.EndDate,
                        ApplicationIds = result.ApplicationIds
                    };

                    var response = await _appointmentService.GetAllStatusChangeNotificationList(notificationRequest).ConfigureAwait(false);

                    response.BranchCountryId = result.BranchCountryId;

                    await SendNotifications(response, 2, request.UserId, (int)NotificationRouteType.ScanSycle, (int)NotificationType.ScanSycle, request.BranchId, null);

                }
            }

            return new UpdateApiResponse
            {
                Result = result.Result,
                NotUpdatedCargoIds = result.NotUpdatedCargoIds,
                NonTurkmenNationIhbStatusIds = result.NonTurkmenNationIhbStatusIds,
                NotUpdatedTurkmenVisaRejectionRefundDoneIds = result.NotUpdatedTurkmenVisaRejectionRefundDoneIds,
                CheckTurkmenRejectionRefundDoneControlMessage = result.CheckTurkmenRejectionRefundDoneControlMessage,
                NotUpdatedInsuranceRefundIds = result.NotUpdatedInsuranceRefundIds,
            };
        }

        [SwaggerOperation(
            Summary = "Update selected applications' current status",
            Description = "Update selected applications' current status.")]
        [HttpPut(ApiMethodName.Appointment.UpdateSelectedApplicationsCurrentStatus)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateSelectedApplicationsCurrentStatus([FromBody] UpdateSelectedApplicationsCurrentStatusApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateApiResponse>(request, () => this.UpdateSelectedApplicationsCurrentStatusAndSendNotifications(request));
        }

        internal async Task<UpdateApiResponse> UpdateSelectedApplicationsCurrentStatusAndSendNotifications(UpdateSelectedApplicationsCurrentStatusApiRequest request)
        {
            var result = await _appointmentService.UpdateSelectedApplicationsCurrentStatusAsync(Mapper.Map<UpdateSelectedApplicationsCurrentStatusRequestDto>(request)).ConfigureAwait(false);

            if (!string.IsNullOrEmpty(result.Item1.CheckRejectionApprovalControlMessage))
                return new UpdateApiResponse { CheckRejectionApprovalControlMessage = result.Item1.CheckRejectionApprovalControlMessage };

            if (!string.IsNullOrEmpty(result.Item1.CheckRejectionRefundDoneControlMessage))
                return new UpdateApiResponse { CheckRejectionRefundDoneControlMessage = result.Item1.CheckRejectionRefundDoneControlMessage };

            var exceptIds = new List<int>();

            exceptIds.AddRange(result.Item1.NotUpdatedCargoIds ?? Enumerable.Empty<int>());
            exceptIds.AddRange(result.Item1.NotUpdatedInsuranceRefundIds?.Select(s => s.Key) ?? Enumerable.Empty<int>());
            exceptIds.AddRange(result.Item1.NonTurkmenNationIhbStatusIds ?? Enumerable.Empty<int>());
            exceptIds.AddRange(result.Item1.NotUpdatedRejectionRefundDoneIds ?? Enumerable.Empty<int>());
            exceptIds.AddRange(result.Item1.NotUpdatedTurkmenVisaRejectionRefundDoneIds ?? Enumerable.Empty<int>());

            request.IdList = request.IdList
                .Where(s => !exceptIds.Contains(s)).ToList();

            result.Item1.ApprovedContent.Contents = result.Item1.ApprovedContent.Contents
                .Where(s => !exceptIds.Contains(s.ApplicationId)).ToList();

            if (request.SendNotification)
            {
                if (result.Item1.ApprovedContent.Contents.Any())
                {
                    var approvedStatusNotificationContent = await _appointmentService.GetApprovedStatusNotificationContentAsync(new GetApprovedStatusNotificationContentRequestDto
                    {
                        BranchId = request.BranchId,
                        StatusUpdateType = result.Item1.ApprovedContent.StatusUpdateType,
                        Contents = result.Item1.ApprovedContent.Contents.Select(r => new GetApprovedStatusNotificationContentRequestDto.ApprovedNotificationContent
                        {
                            ApplicationIds = new List<int>(),
                            ApplicationId = r.ApplicationId
                        }).ToList()
                    }).ConfigureAwait(false);

                    approvedStatusNotificationContent.BranchCountryId = result.Item1.BranchCountryId;

                    if (approvedStatusNotificationContent.SmsList != null && approvedStatusNotificationContent.SmsList.Any())
                        await SendNotifications(approvedStatusNotificationContent, 2, request.UserId, (int)NotificationRouteType.ScanSycle, (int)NotificationType.ScanSycle, request.BranchId, null);
                }

                if (result.Item1.IsEsimBeSent)
                {
                    var esimRequest = new GetStatusChangeNotificationRequestDto
                    {
                        BranchId = request.BranchId,
                        ApplicationCurrentStatus = request.IdList
                            .Select(q => new ApplicationCurrentStatusRequestDto
                            {
                                ApplicationStatusId = request.ApplicationStatusId,
                                ApplicationId = q
                            }).ToList()
                    };

                    var esimResult = await _appointmentService.GetApplicationEsimNotifications(esimRequest);

                    await SendNotifications(esimResult, 2, request.UserId, (int)NotificationRouteType.Esim, (int)NotificationType.Esim, request.BranchId, null);
                }

                if (request.IdList.Any())
                {
                    var notificationRequest = new GetSelectedStatusChangeNotificationRequestDto
                    {
                        BranchId = request.BranchId,
                        ApplicationStatusId = request.ApplicationStatusId,
                        StartDate = request.StartDate,
                        EndDate = request.EndDate,
                        IdList = request.IdList,
                        CurrentApplicationStatusId = request.CurrentApplicationStatusId
                    };

                    var response = await _appointmentService.GetSelectedStatusChangeNotificationList(notificationRequest).ConfigureAwait(false);

                    response.BranchCountryId = result.Item1.BranchCountryId;

                    await SendNotifications(response, 2, request.UserId, (int)NotificationRouteType.ScanSycle, (int)NotificationType.ScanSycle, request.BranchId, null);
                }
            }

            return new UpdateApiResponse
            {
                Result = result.Item1.Result,
                NotUpdatedCargoIds = result.Item1.NotUpdatedCargoIds,
                NotUpdatedRejectionRefundDoneIds = result.Item1.NotUpdatedRejectionRefundDoneIds,
                NonTurkmenNationIhbStatusIds = result.Item1.NonTurkmenNationIhbStatusIds,
                NotUpdatedTurkmenVisaRejectionRefundDoneIds = result.Item1.NotUpdatedTurkmenVisaRejectionRefundDoneIds,
                CheckTurkmenRejectionRefundDoneControlMessage = result.Item1.CheckTurkmenRejectionRefundDoneControlMessage,
                NotUpdatedInsuranceRefundIds = result.Item1.NotUpdatedInsuranceRefundIds,
            };
        }

        [SwaggerOperation(
            Summary = "Send Reference number notification",
            Description = "Send Reference number notification.")]
        [HttpPost(ApiMethodName.Appointment.SendReferenceNumberNotification)]
        public Task<ApiResponse<UpdateApiResponse>> SendReferenceNumberNotification([FromBody] SendReferenceNumberNotificationApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateApiResponse>(request, () => this.UpdateApplicationReferenceNumberAndSendNotifications(request));
        }

        internal async Task<UpdateApiResponse> UpdateApplicationReferenceNumberAndSendNotifications(SendReferenceNumberNotificationApiRequest request)
        {
            var result = await _appointmentService.UpdateApplicationReferenceNumberAsync(Mapper.Map<UpdateApplicationReferenceNumberRequestDto>(request)).ConfigureAwait(false);

            if (request.SendNotification && request.ReferenceNumber != null)
            {
                var applicationNotificationRequest = new ApplicationNotificationResponseDto()
                {
                    BranchCountryId = result.BranchCountryId,
                    SmsList = string.IsNullOrEmpty(request.PhoneNumber) ? new List<ApplicationNotificationResponseDto.Notification>() : new List<ApplicationNotificationResponseDto.Notification>()
                    {
                        new()
                        {
                            IsSendByPrefix = result.BranchSmsIsSendByPrefix,
                            Text = result.SmsContent,
                            ApplicantList = new List<ApplicationNotificationResponseDto.Applicant>()
                            {
                                new()
                                {
                                    Contact = request.PhoneNumber,
                                    ProviderId = result.BranchSmsProviderId,
                                    Sender = result.BranchSmsSender,
                                    Id = request.ApplicationId
                                }
                            }
                        }
                    },
                    EmailList = string.IsNullOrEmpty(request.Email) ? new List<ApplicationNotificationResponseDto.Notification>() : new List<ApplicationNotificationResponseDto.Notification>()
                    {
                        new()
                        {
                            Subject = result.Subject,
                            Text = result.MailContent,
                            ApplicantList = new List<ApplicationNotificationResponseDto.Applicant>()
                            {
                                new()
                                {
                                    ProviderId = result.BranchEmailProviderId,
                                    Sender = string.Empty,
                                    Contact = request.Email,
                                    Id = request.ApplicationId
                                }
                            }
                        }
                    }
                };

                await SendNotifications(applicationNotificationRequest, result.FooterLanguageId, request.UserId, (int)NotificationRouteType.WorkPermit, (int)NotificationType.ReferenceNumber, result.BranchId, null);
            }

            return new UpdateApiResponse
            {
                Result = result.Result
            };
        }

        internal async Task SendNotifications(ApplicationNotificationResponseDto response, int footerLanguageId, int userId, int notificationRouteType, int notificationType, int branchId, int? rejectionApprovalId)
        {
            if (response.EmailList?.Count() > 0)
            {
                await _emailService.SendEmail(response.EmailList.Select(p => new SendNotificationServiceRequest.Notification
                {
                    TransactionId = string.Empty,
                    Subject = p.Subject,
                    Text = p.Text,
                    FooterLanguageId = footerLanguageId,
                    NotificationType = notificationType,
                    ApplicantList = p.ApplicantList?.Select(q => new SendNotificationServiceRequest.Applicant
                    {
                        ProviderId = q.ProviderId,
                        Sender = q.Sender,
                        Contact = q.Contact,
                        IndividualAttachment = notificationRouteType == (int)NotificationRouteType.Esim
                          ? _appointmentService.GetEsimContents(q.Id.GetValueOrDefault()).Result : null
                    }).OrderBy(c => c.Contact).ToList(),
                    IsPriorityNotification = false
                }).ToList());

                await _appointmentService.AddApplicationEmailHistoryAsync(response.EmailList.Select(r => new AddApplicationEmailHistoryRequestDto
                {
                    UserId = userId,
                    EmailHistories = r.ApplicantList.Select(k => new AddApplicationEmailHistoryRequestDto.ApplicationEmailHistoryDto
                    {
                        ApplicationStatusId = notificationRouteType is (int)NotificationRouteType.WorkPermit
                        or (int)NotificationRouteType.ConfirmationCode
                        or (int)NotificationRouteType.RejectionApproval ? 0 : (int)k.ApplicationStatusId,
                        ApplicationId = (int)k.Id,
                        IsWorkPermitEmail = notificationRouteType == (int)NotificationRouteType.WorkPermit,
                        IsApproveEmail = k.IsApproved is true,
                        IsEsim = notificationType == (int)NotificationRouteType.Esim,
                        IsRejectionEmail = k.IsRejected is true,
                        IsConfirmationCodeEmail = notificationRouteType == (int)NotificationRouteType.ConfirmationCode,
                        IsRejectedApprovalCodeEmail = notificationRouteType == (int)NotificationRouteType.RejectionApproval,
                        ParentId = r.ResendParentId,
                        RejectionApprovalId = rejectionApprovalId
                    }).ToList()
                }).ToList());
            }

            if (response.SmsList?.Count() > 0)
            {
                await _smsService.SendSms(response.SmsList.Select(p => new SendNotificationServiceRequest.Notification
                {
                    TransactionId = string.Join(",", p.ApplicantList.OrderBy(c => c.Contact).Select(r => r.Id)),
                    NotificationType = notificationType,
                    Text = p.Text,
                    ResendParentId = p.ResendParentId,
                    IsPriorityNotification = notificationRouteType == (int)NotificationRouteType.ConfirmationCode,
                    ApplicantList = p.IsSendByPrefix ? Mapper.Map<IEnumerable<SendNotificationServiceRequest.Applicant>>(_notificationService.UpdateSmsProvidersByPrefix(branchId, p.ApplicantList.Select(q => new SmsProviderPrefixDto
                    {
                        Contact = q.Contact
                    })).Result) : p.ApplicantList.Select(q => new SendNotificationServiceRequest.Applicant
                    {
                        Sender = q.Sender,
                        ProviderId = q.ProviderId,
                        Contact = q.Contact,
                    }).OrderBy(c => c.Contact).ToList()
                }).ToList());

                await _appointmentService.AddApplicationSmsHistoryAsync(response.SmsList.Select(r => new AddApplicationSmsHistoryRequestDto()
                {
                    UserId = userId,
                    SmsHistories = r.ApplicantList.Select(k => new AddApplicationSmsHistoryRequestDto.ApplicationSmsHistoryDto
                    {
                        ApplicationStatusId = notificationRouteType is (int)NotificationRouteType.WorkPermit or (int)NotificationRouteType.ConfirmationCode or (int)NotificationRouteType.RejectionApproval ? 0 : (int)k.ApplicationStatusId,
                        ApplicationId = (int)k.Id,
                        IsWorkPermitSms = notificationRouteType == (int)NotificationRouteType.WorkPermit,
                        IsApproveSms = k.IsApproved is true,
                        IsRejectionSms = k.IsRejected is true,
                        IsConfirmationCodeSms = notificationRouteType == (int)NotificationRouteType.ConfirmationCode,
                        IsRejectedApprovalCodeSms = notificationRouteType == (int)NotificationRouteType.RejectionApproval,
                        ParentId = r.ResendParentId,
                        RejectionApprovalId = rejectionApprovalId
                    }).ToList()
                }).ToList());
            }
        }


        [SwaggerOperation(
            Summary = "Send Invidual Insurance Link",
            Description = "Send Invidual Insurance Link.")]
        [HttpPost(ApiMethodName.Appointment.SendInvidualInsuranceSms)]
        public Task<ApiResponse<UpdateApiResponse>> SendInvidualInsuranceSms([FromBody] SendInviduallInsuranceSmsApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateApiResponse>(request, () => this.SendInvidualInsuranceSmsForApplication(request));
        }
        internal async Task<UpdateApiResponse> SendInvidualInsuranceSmsForApplication(SendInviduallInsuranceSmsApiRequest request)
        {
            var result = await _appointmentService.SendInvidualInsuranceSmsForApplicationAsync(Mapper.Map<SendInvidualInsuranceSmsForApplicationRequestDto>(request)).ConfigureAwait(false);

            var applicationNotificationRequest = new ApplicationNotificationResponseDto()
            {
                BranchCountryId = result.BranchCountryId,
                SmsList = string.IsNullOrEmpty(result.PhoneNumber)
                    ? new List<ApplicationNotificationResponseDto.Notification>()
                    : new List<ApplicationNotificationResponseDto.Notification>()
                    {
                        new()
                        {
                            IsSendByPrefix = result.BranchSmsIsSendByPrefix,
                            Text = result.SmsContent,
                            ApplicantList = new List<ApplicationNotificationResponseDto.Applicant>()
                            {
                                new()
                                {
                                    Contact = result.PhoneNumber,
                                    ProviderId = result.BranchSmsProviderId,
                                    Sender = result.BranchSmsSender,
                                    Id = request.ApplicationId
                                }
                            }
                        }
                    }
            };

            await SendSmsReleatedInsurance(applicationNotificationRequest, result.FooterLanguageId, request.UserId,
                (int)NotificationRouteType.ReleatedInsurance, (int)NotificationType.ReferenceNumber, result.BranchId, null);


            return new UpdateApiResponse
            {
                Result = result.Result
            };
        }

        internal async Task SendSmsReleatedInsurance(ApplicationNotificationResponseDto response, int footerLanguageId, int userId, int notificationRouteType, int notificationType, int branchId, int? rejectionApprovalId)
        {
            if (response.SmsList?.Count() > 0)
            {
                await _smsService.SendSms(response.SmsList.Select(p => new SendNotificationServiceRequest.Notification
                {
                    TransactionId = string.Join(",", p.ApplicantList.OrderBy(c => c.Contact).Select(r => r.Id)),
                    NotificationType = notificationType,
                    Text = p.Text,
                    ResendParentId = p.ResendParentId,
                    IsPriorityNotification = notificationRouteType == (int)NotificationRouteType.ConfirmationCode,
                    ApplicantList = p.IsSendByPrefix ? Mapper.Map<IEnumerable<SendNotificationServiceRequest.Applicant>>(_notificationService.UpdateSmsProvidersByPrefix(branchId, p.ApplicantList.Select(q => new SmsProviderPrefixDto
                    {
                        Contact = q.Contact
                    })).Result) : p.ApplicantList.Select(q => new SendNotificationServiceRequest.Applicant
                    {
                        Sender = q.Sender,
                        ProviderId = q.ProviderId,
                        Contact = q.Contact,
                    }).OrderBy(c => c.Contact).ToList()
                }).ToList());

                await _appointmentService.AddApplicationSmsHistoryAsync(response.SmsList.Select(r => new AddApplicationSmsHistoryRequestDto()
                {
                    UserId = userId,
                    SmsHistories = r.ApplicantList.Select(k => new AddApplicationSmsHistoryRequestDto.ApplicationSmsHistoryDto
                    {
                        ApplicationStatusId = notificationRouteType is (int)NotificationRouteType.WorkPermit or (int)NotificationRouteType.ConfirmationCode or (int)NotificationRouteType.RejectionApproval or (int)NotificationRouteType.ReleatedInsurance ? 0 : (int)k.ApplicationStatusId,
                        ApplicationId = (int)k.Id,
                        IsWorkPermitSms = notificationRouteType == (int)NotificationRouteType.WorkPermit,
                        IsApproveSms = k.IsApproved is true,
                        IsRejectionSms = k.IsRejected is true,
                        IsConfirmationCodeSms = notificationRouteType == (int)NotificationRouteType.ConfirmationCode,
                        IsRejectedApprovalCodeSms = notificationRouteType == (int)NotificationRouteType.RejectionApproval,
                        IsReleatedInsurance = notificationRouteType == (int)NotificationRouteType.ReleatedInsurance,
                        ParentId = r.ResendParentId,
                        RejectionApprovalId = rejectionApprovalId
                    }).ToList()
                }).ToList());
            }
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collections of countries to do application from branch",
            Description = "Branch information is required.")]
        [HttpPost(ApiMethodName.Appointment.GetBranchApplicationCountries)]
        public Task<ApiResponse<BranchApplicationCountriesApiResponse>> GetBranchApplicationCountries([FromBody] BranchApplicationCountriesApiRequest request)
        {
            return Process<BranchApplicationCountriesApiResponse, BranchApplicationCountriesResponseDto>(request, () => _appointmentService.GetBranchApplicationCountriesAsync(Mapper.Map<BranchApplicationCountriesRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collections of visa categories for a country to do application from branch",
            Description = "Branch information is required.")]
        [HttpPost(ApiMethodName.Appointment.GetBranchApplicationCountryVisaCategories)]
        public Task<ApiResponse<BranchApplicationCountryVisaCategoriesApiResponse>> GetBranchApplicationCountryVisaCategories([FromBody] BranchApplicationCountryVisaCategoriesApiRequest request)
        {
            return Process<BranchApplicationCountryVisaCategoriesApiResponse, BranchApplicationCountryVisaCategoriesResponseDto>(request, () => _appointmentService.GetBranchApplicationCountryVisaCategoriesAsync(Mapper.Map<BranchApplicationCountryVisaCategoriesRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get specific collections such as ApplicationFormElement & ExtraFee to create branch & country specific application form",
            Description = "Get specific collections such as ApplicationFormElement & ExtraFee by using filtering parameters such as id of country to do application from branch")]
        [HttpPost(ApiMethodName.Appointment.GetPreApplicationForm)]
        public Task<ApiResponse<PreApplicationFormApiResponse>> GetPreApplicationForm([FromBody] PreApplicationFormApiRequest request)
        {
            return Process<PreApplicationFormApiResponse, PreApplicationFormResponseDto>(request, () => _appointmentService.GetPreApplicationFormAsync(Mapper.Map<PreApplicationFormRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to add new application",
            Description = "Fill required fields for Description, Basic Information, Document Information & Extra Package sections.")]
        [HttpPost(ApiMethodName.Appointment.AddApplication)]
        public Task<ApiResponse<AddApiResponse>> AddApplication([FromBody] AddUpdateApplicationApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.AddApplicationAsync(Mapper.Map<AddUpdateApplicationRequestDto>(request)));
        }

        [SwaggerOperation(
           Summary = "generate code",
           Description = "generate code")]
        [HttpPost("api/Appointment/ContactInformationGenerateVerificationCode")]
        public async Task<ApiResponse<VeriyfContactInformationCreateCodeResponseDto>> ContactInformationGenerateVerificationCode([FromBody] ContactInformationGenerateVerificationCodeApiRequest request)
        {
            var result = await Process<VeriyfContactInformationCreateCodeResponseDto, VeriyfContactInformationCreateCodeResponseDto>(request, () => _appointmentService.ContactInformationGenerateVerificationCode(Mapper.Map<ContactInformationGenerateVerificationCodeDto>(request)));
            return result;
        }

        [SwaggerOperation(
           Summary = "Verify code",
           Description = "Verify code")]
        [HttpPost("api/Appointment/ContactInformationVerifyVerificationCode")]
        public async Task<ApiResponse<VeriyfContactInformationVerifyCodeResponseDto>> ContactInformationVerifyVerificationCode([FromBody] ContactInformationVerifyVerificationCodeApiRequest request)
        {
            var result = await Process<VeriyfContactInformationVerifyCodeResponseDto, VeriyfContactInformationVerifyCodeResponseDto>(request, () => _appointmentService.ContactInformationVerifyVerificationCode(Mapper.Map<ContactInformationVerifyVerificationCodeDto>(request)));

            return result;


        }
        [SwaggerOperation(
          Summary = "generate code",
          Description = "generate code")]
        [HttpPost("api/Appointment/ContactInformationVerifyUser")]
        public async Task<ApiResponse<UserLoginResponseDto>> ContactInformationVerifyUser([FromBody] ContactInformationVerifyUserApiRequest request)
        {
            var msg = "";
            var branchAuthoritiesReques = new GetBranchAuthoritiesRequestDto
            {
                Id = request.BranchId,
                LanguageId = request.LanguageId,
            };
            var contactRequest = new ContactInformationVerifyUserDto
            {
                IsVerified = true,
                UserName = request.Email,
                UserId = request.UserId,
                PassportNumber = request.PassportNumber,
            };
            UserLoginResponseDto result = null;
            GetBranchAuthoritiesResponseDto branchAuthorities = null;
            try
            {
                result = await _userService.UserLoginAsync(Mapper.Map<UserLoginRequestDto>(request));
                branchAuthorities = await _managementService.GetBranchAuthoritiesAsync(branchAuthoritiesReques);
            }
            catch (NoFoundDataPortalException ex)
            {
                msg = ex.ErrorMessage;
                contactRequest.IsVerified = false;
            }
            catch (Exception ex)
            {
                msg = ex.Message;
                contactRequest.IsVerified = false;
            }
            var roleIds = branchAuthorities?.SupervisorRoleList?.Where(k => k.IsSelected && k.Role != null).Select(k => k.Role.Id).ToList();
            var userIds = branchAuthorities?.SupervisorRoleList?.Where(k => k.IsSelected && k.UserId != null).Select(k => k.UserId).ToList();

            if (result == null)
            {
                msg = nameof(SiteResources.UserNotFound).ToSiteResourcesValue(request.LanguageId);
                contactRequest.IsVerified = false;
            }
            else if (branchAuthorities == null || !userIds.Contains(result.UserId))
            {
                msg = nameof(SiteResources.AdminNotFound).ToSiteResourcesValue(request.LanguageId);
                contactRequest.IsVerified = false;
            }
            else if (!roleIds.Intersect(new int[] { 8, 7, 5 }).Any())
            {
                msg = nameof(SiteResources.NotAuthorizedBranchManager).ToSiteResourcesValue(request.LanguageId);
                contactRequest.IsVerified = false;
            }

            _ = await _appointmentService.ContactInformationVerifyUser(contactRequest);
            var apiresult = await Process<UserLoginResponseDto, UserLoginResponseDto>(request, () => Task.FromResult(result));

            if (!contactRequest.IsVerified)
            {
                apiresult.IsSuccess = false;
                apiresult.Message = msg;
            }

            return apiresult;
        }

        [SwaggerOperation(
         Summary = "generate code",
         Description = "generate code")]
        [HttpGet("api/Appointment/GetContactInformationVerificationHistory")]
        public async Task<ApiResponse<List<ContactInformationVerificationHistoryDto>>> GetContactInformationVerificationHistory(string passportNumber, string encryptedId, int relationalApplicationId, int applicantTypeId)
        {
            var data = _appointmentService.GetContactInformationVerificationHistory(passportNumber, encryptedId, relationalApplicationId, applicantTypeId);
            var request = new ContactInformationVerificationHistory { PassportNumber = passportNumber, EncryptedId = encryptedId, RelationalApplicationId = relationalApplicationId, ApplicantTypeId = applicantTypeId };
            var result = await Process<List<ContactInformationVerificationHistoryDto>, List<ContactInformationVerificationHistoryDto>>(request, () => data);
            return result;
        }

        [SwaggerOperation(
            Summary = "Provides clients to update existing application",
            Description = "Fill required fields for Description, Basic Information, Document Information & Extra Package sections.")]
        [HttpPut(ApiMethodName.Appointment.UpdateApplication)]
        public Task<ApiResponse<UpdateApplicationApiResponse>> UpdateApplication([FromBody] AddUpdateApplicationApiRequest request)
        {
            return Process<UpdateApplicationApiResponse, UpdateApplicationResponseDto>(request, () => _appointmentService.UpdateApplicationAsync(Mapper.Map<AddUpdateApplicationRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to update existing application for easy use",
            Description = "-")]
        [HttpPut(ApiMethodName.Appointment.UpdateApplicationEasyUse)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateApplicationEasyUse([FromBody] UpdateApplicationEasyUseApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.UpdateApplicationEasyUseAsync(Mapper.Map<UpdateApplicationEasyUseRequestDto>(request)));
        }

        [SwaggerOperation(
          Summary = "Provides clients to update existing application Local Authority Status",
          Description = "-")]
        [HttpPut(ApiMethodName.Appointment.UpdateApplicationLocalAuthorityStatus)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateApplicationLocalAuthorityStatus([FromBody] UpdateApplicationLocalAuthorityStatusApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.UpdateApplicationLocalAuthorityStatusAsync(Mapper.Map<UpdateApplicationLocalAuthorityStatusRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of paginated applications",
            Description = "Get collection of paginated applications by using filtering parameters")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedApplications)]
        public Task<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>> GetPaginatedApplications([FromBody] PaginatedApplicationsApiRequest request)
        {
            return Process<PaginationApiResponse<ApplicationsApiResponse>, Pagination<ApplicationsDto>>(request, () => _appointmentService.GetPaginatedApplicationsAsync(Mapper.Map<PaginatedApplicationsRequestDto>(request)));
        }

        [SwaggerOperation(
          Summary = "Provides clients to get collection of paginated applications",
          Description = "Get collection of paginated applications by using filtering parameters")]
        [HttpPost(ApiMethodName.Appointment.GetSanitizedPaginatedApplications)]
        public Task<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>> GetSanitizedPaginatedApplications([FromBody] PaginatedApplicationsApiRequest request)
        {
            return Process<PaginationApiResponse<ApplicationsApiResponse>, Pagination<ApplicationsDto>>(request, () => _appointmentService.GetSanitizedPaginatedApplicationsAsync(Mapper.Map<PaginatedApplicationsRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of paginated application exempt persons",
            Description = "Get collection of paginated application exempt person by using filtering parameters")]
        [HttpPost(ApiMethodName.Appointment.GetDetailedInformationAboutExemptPersons)]
        public Task<ApiResponse<PaginationApiResponse<ApplicationDetailExemptPersonsApiResponse>>> GetDetailedInformationAboutExemptPersons([FromBody] PaginatedApplicationExemptPersonApiRequest request)
        {
            return Process<PaginationApiResponse<ApplicationDetailExemptPersonsApiResponse>, Pagination<ApplicationDetailExemptPersonsDto>>(request, () => _appointmentService.GetDetailedInformationAboutExemptPersonsAsync(Mapper.Map<PaginatedApplicationDetailExemptPersonsRequestDto>(request)));
        }

        [SwaggerOperation(
         Summary = "Provides clients to get collection of paginated applications",
         Description = "Get collection of paginated applications by using filtering parameters")]
        [HttpPost(ApiMethodName.Appointment.GetSanitizedPaginatedApplicationsForScanSycle)]
        public Task<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>> GetSanitizedPaginatedApplicationsForScanSycle([FromBody] PaginatedApplicationsApiRequest request)
        {
            return Process<PaginationApiResponse<ApplicationsApiResponse>, Pagination<ApplicationsDto>>(request, () => _appointmentService.GetSanitizedPaginatedApplicationsForScanSycleAsync(Mapper.Map<PaginatedApplicationsRequestDto>(request)));
        }

        [HttpPost(ApiMethodName.Appointment.GetPaginatedApplicationsForRejectionControl)]
        public Task<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>> GetPaginatedApplicationsForRejectionControl([FromBody] PaginatedApplicationsApiRequest request)
        {
            return Process<PaginationApiResponse<ApplicationsApiResponse>, Pagination<ApplicationsDto>>(request, () => _appointmentService.GetPaginatedApplicationsForRejectionControlAsync(Mapper.Map<PaginatedApplicationsRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to check of passport number",
            Description = "Provides clients to check jobs of previous applications with respect to their passport numbers")]
        [HttpPost(ApiMethodName.Appointment.AddUpdateApplicationsControlForJob)]
        public Task<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>> AddUpdateApplicationsControlForJob([FromBody] PaginatedApplicationsApiRequest request)
        {
            return Process<PaginationApiResponse<ApplicationsApiResponse>, Pagination<ApplicationsDto>>(request, () => _appointmentService.AddUpdateApplicationsControlForJobAsync(Mapper.Map<PaginatedApplicationsRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to check of passport number",
            Description = "Provides clients to check of passport number for block and same passport number situations.")]
        [HttpPost(ApiMethodName.Appointment.AddUpdateApplicationsControlForPassportNumber)]
        public Task<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>> AddUpdateApplicationsControlForPassportNumber([FromBody] PaginatedApplicationsApiRequest request)
        {
            return Process<PaginationApiResponse<ApplicationsApiResponse>, Pagination<ApplicationsDto>>(request, () => _appointmentService.AddUpdateApplicationsControlForPassportNumberAsync(Mapper.Map<PaginatedApplicationsRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to check of mission rejection",
            Description = "Provides clients to check of mission rejection")]
        [HttpPost(ApiMethodName.Appointment.ApplicationControlForMissionRejection)]
        public Task<ApiResponse<MissionRejectionApiResponse>> ApplicationControlForMissionRejection([FromBody] MissionRejectionApiRequest request)
        {
            return Process<MissionRejectionApiResponse, MissionRejectionResponseDto>(request, () => _appointmentService.ApplicationControlForMissionRejection(Mapper.Map<MissionRejectionRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to check of relationship",
            Description = "Provides clients to check of relationship.")]
        [HttpPost(ApiMethodName.Appointment.RelationShipCheck)]
        public Task<ApiResponse<RelationShipCheckApiResponse>> RelationShipCheck([FromBody] RelationShipCheckApiRequest request)
        {
            return Process<RelationShipCheckApiResponse, RelationShipCheckApiResponseDto>(request, () => _appointmentService.RelationShipCheckAsync(Mapper.Map<RelationShipCheckApiRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get application details",
            Description = "Get application details by id and language option.")]
        [HttpGet(ApiMethodName.Appointment.GetApplication + "{id}")]
        public Task<ApiResponse<ApplicationApiResponse>> GetApplication(int id)
        {
            var request = new ApplicationApiRequest { Id = id };

            return Process<ApplicationApiResponse, ApplicationDto>(request, () => _appointmentService.GetApplicationAsync(Mapper.Map<ApplicationRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get application details",
            Description = "Get application details by id and language option.")]
        [HttpGet(ApiMethodName.Appointment.GetApplicationInsuranceInformation + "{id}")]
        public Task<ApiResponse<ApplicationApiResponse>> GetApplicationInsuranceInformation(int id)
        {
            var request = new ApplicationApiRequest { Id = id };

            return Process<ApplicationApiResponse, ApplicationDto>(request, () => _appointmentService.GetApplicationInsuranceInformationAsync(Mapper.Map<ApplicationRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get application extra fee details",
            Description = "Get extra fee details by id and language option.")]
        [HttpGet(ApiMethodName.Appointment.GetSanitizedApplicationExtraFees + "{id}")]
        public Task<ApiResponse<ApplicationExtraFeeApiResponse>> GetSanitizedApplicationExtraFees(int id)
        {
            var request = new ApplicationExtraFeeApiRequest { Id = id };
            return Process<ApplicationExtraFeeApiResponse, ApplicationExtraFeeDto>(request, () => _appointmentService.GetSanitizedApplicationExtraFeesAsync(Mapper.Map<ApplicationExtraFeeRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get application information summary details",
            Description = "Get information summary details by id and language option.")]
        [HttpGet(ApiMethodName.Appointment.GetSanitizedApplicationSummary + "{id}")]
        public Task<ApiResponse<ApplicationSummaryApiResponse>> GetSanitizedApplicationDetails(int id)
        {
            var request = new ApplicationSummaryApiRequest { Id = id };
            return Process<ApplicationSummaryApiResponse, ApplicationSummaryDto>(request, () => _appointmentService.GetSanitizedApplicationSummaryAsync(Mapper.Map<ApplicationSummaryRequestDto>(request)));
        }

        [SwaggerOperation(
           Summary = "Send visa rejection application details",
           Description = "Send visa rejection application details for Sap.")]
        [HttpGet(ApiMethodName.Appointment.GetSendVisaRejectionApplication + "{applicationId}/{rejectionAmount}/{currency}")]
        public Task<ApiResponse<ApplicationVisaRejectionResponse>> GetSendVisaRejectionApplication(int applicationId, decimal rejectionAmount, string currency)
        {
            var request = new ApplicationVisaRejectionRequest { ApplicationId = applicationId, RejectionAmount = rejectionAmount, Currency = currency };

            return Process<ApplicationVisaRejectionResponse, ApplicationVisaRejectionDto>(request, () => _appointmentService.GetSendVisaRejectionApplicationAsync(Mapper.Map<ApplicationVisaRejectionRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get application information foreign health insurance",
            Description = "Get information Foreign Health Insurance.")]
        [HttpGet(ApiMethodName.Appointment.ForeignHealthInsuranceCheck + "{id}")]
        public Task<ApiResponse<ForeignHealthInsuranceCheckApiResponse>> ForeignHealthInsuranceCheck(int id)
        {
            var request = new ApplicationSummaryApiRequest { Id = id };
            return Process<ForeignHealthInsuranceCheckApiResponse, ForeignHealthInsuranceCheckDto>(request, () => _appointmentService.ForeignHealthInsuranceCheckAsync(Mapper.Map<ApplicationSummaryRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get application information document details",
            Description = "Get information summary details by id and language option.")]
        [HttpGet(ApiMethodName.Appointment.GetSanitizedApplicationDocument + "{id}")]
        public Task<ApiResponse<ApplicationDocumentApiResponse>> GetSanitizedApplicationDocument(int id)
        {
            var request = new ApplicationDocumentApiRequest { Id = id };
            return Process<ApplicationDocumentApiResponse, ApplicationDocumentDto>(request, () => _appointmentService.GetSanitizedApplicationDocumentAsync(Mapper.Map<ApplicationDocumentRequestDto>(request)));
        }

        [SwaggerOperation(
           Summary = "Provides clients to get application information status history details",
           Description = "Get information summary details by id and language option.")]
        [HttpGet(ApiMethodName.Appointment.GetSanitizedApplicationStatusHistory + "{id}")]
        public Task<ApiResponse<ApplicationStatusHistoryApiResponse>> GetSanitizedApplicationStatusHistory(int id)
        {
            var request = new ApplicationStatusHistoryApiRequest { Id = id };
            return Process<ApplicationStatusHistoryApiResponse, ApplicationStatusHistoryDto>(request, () => _appointmentService.GetSanitizedApplicationStatusHistoryAsync(Mapper.Map<ApplicationStatusHistoryRequestDto>(request)));
        }

        [SwaggerOperation(
           Summary = "Provides clients to get application information history details",
           Description = "Get information summary details by id and language option.")]
        [HttpGet(ApiMethodName.Appointment.GetSanitizedApplicationHistory + "{id}/{languageId}")]
        public Task<ApiResponse<ApplicationHistoryApiResponse>> GetSanitizedApplicationHistory(int id, int languageId)
        {
            var request = new ApplicationHistoryApiRequest { Id = id,  LanguageId = languageId };
            return Process<ApplicationHistoryApiResponse, ApplicationHistoryDto>(request, () => _appointmentService.GetSanitizedApplicationHistoryAsync(Mapper.Map<ApplicationHistoryRequestDto>(request)));
        }

        [SwaggerOperation(
          Summary = "Provides clients to get informations used in entry form",
          Description = "Get information used in entry form by id and language option.")]
        [HttpGet(ApiMethodName.Appointment.GetSanitizedApplicationEntryForm + "{id}")]
        public Task<ApiResponse<ApplicationEntryFormApiResponse>> GetSanitizedApplicationEntryForm(int id)
        {
            var request = new ApplicationEntryFormApiRequest { ApplicationId = id };

            return Process<ApplicationEntryFormApiResponse, ApplicationEntryFormDto>(request, () => _appointmentService.GetSanitizedApplicationEntryFormAsync(Mapper.Map<ApplicationEntryFormRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get application details for easy use",
            Description = "Get application details by id and language option for easy use.")]
        [HttpGet(ApiMethodName.Appointment.GetApplicationEasyUse + "{id}")]
        public Task<ApiResponse<ApplicationEasyUseApiResponse>> GetApplicationEasyUse(int id)
        {
            var request = new ApplicationEasyUseApiRequest { Id = id };

            return Process<ApplicationEasyUseApiResponse, ApplicationEasyUseDto>(request, () => _appointmentService.GetApplicationEasyUseAsync(Mapper.Map<ApplicationEasyUseRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get application details for relational appointments",
            Description = "Get application details by relational application id")]
        [HttpGet(ApiMethodName.Appointment.GetRelationalApplications + "{id}")]
        public Task<ApiResponse<RelationalApplicationsApiResponse>> GetRelationalApplications(int id)
        {
            var request = new RelationalApplicationsApiRequest { RelationalApplicationId = id };

            return Process<RelationalApplicationsApiResponse, RelationalApplicationsDto>(request, () => _appointmentService.GetRelationalApplicationAsync(Mapper.Map<RelationalApplicationsRequestDto>(request)));
        }

        [SwaggerOperation(
        Summary = "Provides clients to get filtered appointment list",
        Description = "Provides clients to get filtered appointment list")]
        [HttpPost(ApiMethodName.Appointment.GetApplicationReport)]
        public Task<ApiResponse<ApplicationReportApiResponse>> GetApplicationReport([FromBody] GetApplicationReportApiRequest request)
        {
            return Process<ApplicationReportApiResponse, ApplicationReportResponseDto>(request, () => _appointmentService.GetApplicationReportAsync(Mapper.Map<GetApplicationReportRequestDto>(request)));
        }

        [SwaggerOperation(
         Summary = "Get SAP order send status",
         Description = "Get SAP order send status for appointmentId")]
        [HttpGet(ApiMethodName.Appointment.GetSendToSapOrder + "{appointmentId}")]
        public Task<ApiResponse<GetSendToSapOrderGroupIdResponse>> GetSendToSapOrder(int appointmentId)
        {
            return Process<GetSendToSapOrderGroupIdResponse, GetSendToSapOrderDto>(new EmptyApiRequest(), () => _appointmentService.GetSendToSapOrder(appointmentId));

        }

        [SwaggerOperation(
         Summary = "Get VasType from PreApplication",
         Description = "Get VasType from PreApplication by passportNumber")]
        [HttpGet(ApiMethodName.Appointment.GetApplicationVasType + "{passportNumber}/{branchCountryId}")]
        public Task<ApiResponse<AddApiResponse>> GetApplicationVasType(string passportNumber, int branchCountryId)
        {
            return Process<AddApiResponse, AddResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetApplicationVasTypeAsync(passportNumber, branchCountryId));

        }

        [SwaggerOperation(
            Summary = "Get Nll Check by passport number and nationalty",
            Description = "Get Nll Check by passport number and nationalty")]
        [HttpPost(ApiMethodName.Appointment.GetNllChechByPassportNumberAndNationalty)]
        public Task<ApiResponse<AddApiResponse>> GetNllChechByPassportNumberAndNationalty([FromBody] GetNllCheckByPassportNumberNationaltyApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.GetNllChechByPassportNumberAndNationalty(Mapper.Map<GetNllCheckByPassportNumberNationaltyApiRequest>(request)));
        }

        [SwaggerOperation(
          Summary = "Get Nll status Check by passport number and nationalty",
          Description = "Get Nll Check by passport number and nationalty")]
        [HttpPost(ApiMethodName.Appointment.GetRejectionRefundDoneChechByPassportNumberAndNationalty)]
        public Task<ApiResponse<ApplicationChechRefundDoneResponse>> GetRejectionRefundDoneChechByPassportNumberAndNationalty([FromBody] GetNllCheckByPassportNumberNationaltyApiRequest request)
        {
            return Process<ApplicationChechRefundDoneResponse, ApplicationChechRefundDoneResponseDto>(request, () => _appointmentService.GetRejectionRefundDoneChechByPassportNumberAndNationalty(Mapper.Map<GetNllCheckByPassportNumberNationaltyApiRequest>(request)));
        }

        [SwaggerOperation(
            Summary = "Get Application VisaCategory Reference Number information",
            Description = "Get Application VisaCategory Reference Number information")]
        [HttpGet(ApiMethodName.Appointment.GetApplicationVisaCategoryReferenceNumber + "{id}")]
        public Task<ApiResponse<ApplicationVisaCategoryReferanceNumberApiResponse>> GetApplicationVisaCategoryReferenceNumber(int id)
        {
            var request = new ApplicationVisaCategoryReferenceNumberApiRequest { ApplicationId = id };
            return Process<ApplicationVisaCategoryReferanceNumberApiResponse, ApplicationVisaCategoryReferenceNumberResponseDto>(request, () => _appointmentService.GetApplicationVisaCategoryReferenceNumberAsync(Mapper.Map<ApplicationVisaCategoryReferenceNumberRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Update Confirmation Type of Application",
            Description = "Update Confirmation Type of Application")]
        [HttpPost(ApiMethodName.Appointment.UpdateConfirmationType)]
        public Task<ApiResponse<UpdateApplicationConfirmationCodeApiResponse>> UpdateConfirmationType([FromBody] UpdateApplicationConfirmationCodeApiRequest request)
        {
            return Process<UpdateApplicationConfirmationCodeApiResponse, UpdateApplicationConfirmationCodeResponseDto>(request, () => _appointmentService.UpdateApplicationConfirmationCodeAsync(Mapper.Map<UpdateApplicationConfirmationCodeRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Get Application Confirmation Code information",
            Description = "Get Application Confirmation Code information")]
        [HttpPost(ApiMethodName.Appointment.GetApplicationConfirmationCode)]
        public Task<ApiResponse<ApplicationConfirmationCodeApiResponse>> GetApplicationConfirmationCode([FromBody] ApplicationConfirmationCodeApiRequest request)
        {
            return Process<ApplicationConfirmationCodeApiResponse, ApplicationConfirmationCodeResponseDto>(request, () => _appointmentService.GetApplicationConfirmationCodeAsync(Mapper.Map<ApplicationConfirmationCodeRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to update Turkmenistan application interview informations",
            Description = "Update Turkmenistan application interview informations")]
        [HttpPut(ApiMethodName.Appointment.UpdateInterviewInformation)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateInterviewInformation([FromBody] ApplicationUpdateInterviewInformationApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.UpdateInterviewInformationAsync(Mapper.Map<ApplicationUpdateInterviewInformationRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Get Countries According To Branches",
            Description = "Get Countries According To Branch Ids.")]
        [HttpPost(ApiMethodName.Appointment.GetCountriesAccordingToBranches)]
        public Task<ApiResponse<CountriesApiResponse>> GetCountriesAccordingToBranches([FromBody] GetBranchesApiRequest request)
        {
            return Process<CountriesApiResponse, CountriesDto>(request, () => _appointmentService.GetCountriesAccordingToBranchesAsync(Mapper.Map<GetBranchesRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Get information that insurance allows for more than one year",
            Description = "Get information that insurance allows for more than one year according to PassportExpireDate")]
        [HttpGet(ApiMethodName.Appointment.IsInsuranceAllowsForMoreThanOneYear + "{extraFee}")]
        public Task<ApiResponse<ApplicationIsInsuranceAllowsForMoreThanOneYearApiResponse>> IsInsuranceAllowsForMoreThanOneYear(string extraFee)
        {
            return Process<ApplicationIsInsuranceAllowsForMoreThanOneYearApiResponse, ApplicationIsInsuranceAllowsForMoreThanOneYearDto>(new EmptyApiRequest(), () => _appointmentService.IsInsuranceAllowsForMoreThanOneYearAsync(extraFee));
        }

        [SwaggerOperation(
         Summary = "Is Last Application",
         Description = "Is last application for Family and Group Applications")]
        [HttpGet(ApiMethodName.Appointment.IsLastApplication + "{applicationId}")]
        public Task<ApiResponse<LastApplicationApiResponse>> IsLastApplication(int applicationId)
        {
            return Process<LastApplicationApiResponse, LastApplicationDto>(new EmptyApiRequest(), () => _appointmentService.IsLastApplicationAsync(applicationId));
        }

        [SwaggerOperation(
        Summary = "Check Contact Information Update Allowed",
        Description = "Check Contact Information Update Allowed")]
        [HttpGet(ApiMethodName.Appointment.CheckContactInformationUpdateAllowed + "{applicationId}")]
        public Task<ApiResponse<CheckContactInformationUpdateAllowedApiResponse>> CheckContactInformationUpdateAllowed(int applicationId)
        {
            return Process<CheckContactInformationUpdateAllowedApiResponse, CheckContactInformationUpdateAllowedDto>(new EmptyApiRequest(), () => _appointmentService.CheckContactInformationUpdateAllowedAsync(applicationId));
        }

        [SwaggerOperation(
         Summary = "Is Main Application",
         Description = "Is Main application for Family and Group Applications")]
        [HttpGet(ApiMethodName.Appointment.IsMainApplication + "{relationalApplicationId}/{id}")]
        public Task<ApiResponse<ValidateApiResponse>> IsMainApplication(int relationalApplicationId, int id)
        {
            return Process<ValidateApiResponse, ValidateResponseDto>(new EmptyApiRequest(), () => _appointmentService.IsMainApplicationAsync(relationalApplicationId, id));
        }

        [SwaggerOperation(
         Summary = "Is Insurance exist",
         Description = "Is Not Created Insurance exist for application")]
        [HttpGet(ApiMethodName.Appointment.IsNotCreatedInsuranceExist + "{applicationId}")]
        public Task<ApiResponse<ValidateApiResponse>> IsNotCreatedInsuranceExist(int applicationId)
        {
            return Process<ValidateApiResponse, ValidateResponseDto>(new EmptyApiRequest(), () => _appointmentService.IsNotCreatedInsuranceExistAsync(applicationId));
        }

        #region ApplicationCancellation

        [SwaggerOperation(
            Summary = "Provides clients to add new application cancellation",
            Description = "-")]
        [HttpPost(ApiMethodName.Appointment.AddApplicationCancellation)]
        public Task<ApiResponse<AddApiResponse>> AddApplicationCancellation([FromBody] AddApplicationCancellationApiRequest request)
        {
            return Process<AddApiResponse, AddApiResponse>(request, () => this.AddApplicationCancellationAndNotification(request));
        }

        internal async Task<AddApiResponse> AddApplicationCancellationAndNotification(AddApplicationCancellationApiRequest request)
        {
            var result = await _appointmentService.AddApplicationCancellationAsync(Mapper.Map<AddApplicationCancellationRequestDto>(request)).ConfigureAwait(false);

            //TODO - Replace with Azure FN instead of Task.Run

            return new AddApiResponse()
            {
                Id = result.Id
            };
        }

        [SwaggerOperation(
            Summary = "Provides clients to update existing application cancellation request",
            Description = "-")]
        [HttpPut(ApiMethodName.Appointment.UpdateApplicationCancellationStatus)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateApplicationCancellationStatus([FromBody] UpdateApplicationCancellationStatusApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.UpdateApplicationCancellationStatusAsync(Mapper.Map<UpdateApplicationCancellationStatusRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of paginated application cancellations",
            Description = "Get collection of paginated application cancellations by using filtering parameters")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedApplicationCancellations)]
        public Task<ApiResponse<PaginationApiResponse<ApplicationCancellationsApiResponse>>> GetPaginatedApplicationCancellations([FromBody] PaginatedApplicationCancellationsApiRequest request)
        {
            return Process<PaginationApiResponse<ApplicationCancellationsApiResponse>, Pagination<ApplicationCancellationsDto>>(request, () => _appointmentService.GetPaginatedApplicationCancellationsAsync(Mapper.Map<PaginatedApplicationCancellationsRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get application cancellation details",
            Description = "Get application cancellation details by id.")]
        [HttpGet(ApiMethodName.Appointment.GetApplicationCancellation + "{id}")]
        public Task<ApiResponse<ApplicationCancellationApiResponse>> GetApplicationCancellation(int id)
        {
            var request = new ApplicationCancellationApiRequest { Id = id };

            return Process<ApplicationCancellationApiResponse, ApplicationCancellationDto>(request, () => _appointmentService.GetApplicationCancellationAsync(Mapper.Map<ApplicationCancellationRequestDto>(request)));
        }

        [SwaggerOperation(
           Summary = "Provides clients to get application cancellation photobooth application used check",
           Description = "Get application cancellation cancellation photobooth application used check.")]
        [HttpGet(ApiMethodName.Appointment.GetExistingPhotoboothApplicationUsedCheck + "{applicationId}")]
        public Task<ApiResponse<ValidateApiResponse>> GetExistingPhotoboothApplicationUsedCheck(int applicationId)
        {
            return Process<ValidateApiResponse, ValidateResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetExistingPhotoboothApplicationUsedCheckAsync(applicationId));
        }

        [SwaggerOperation(
            Summary = "Provides clients to update existing application Update PhotoBooth Application Delete",
            Description = "-")]
        [HttpGet(ApiMethodName.Appointment.UpdatePhotoBoothApplicationDelete + "{applicationId}/{usedCheck}")]
        public Task<ApiResponse<UpdateApiResponse>> UpdatePhotoBoothApplicationDelete(int applicationId, bool usedCheck)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(new EmptyApiRequest(), () => _appointmentService.UpdatePhotoBoothApplicationDeleteAsync(applicationId, usedCheck));
        }

        [SwaggerOperation(
            Summary = "Provides clients to update existing Update PhotoBooth Application Status",
            Description = "-")]
        [HttpGet(ApiMethodName.Appointment.UpdatePhotoBoothApplicationStatus + "{photoBoothId}/{statusId}")]
        public Task<ApiResponse<UpdateApiResponse>> UpdatePhotoBoothApplicationStatus(int photoBoothId, int statusId)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(new EmptyApiRequest(), () => _appointmentService.UpdatePhotoBoothApplicationStatusAsync(photoBoothId, statusId));
        }

        [SwaggerOperation(
            Summary = "Send Ksa Icr Generate EInvoice",
            Description = "Send Ksa Icr Generate EInvoice")]
        [HttpPost(ApiMethodName.Appointment.SendKsaIcrGenerateEInvoice)]
        public Task<ApiResponse<UpdateApiResponse>> SendKsaIcrGenerateEInvoice([FromBody] SendKsaIcrGenerateEInvoiceApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.SendKsaIcrGenerateEInvoiceAsync(Mapper.Map<SendKsaIcrGenerateEInvoiceDto>(request)));

        }

        [SwaggerOperation(
            Summary = "Update application current status",
            Description = "Update application current status.")]
        [HttpPost(ApiMethodName.Appointment.SendKsaIcrPartialOrCancelEInvoice)]
        public Task<ApiResponse<UpdateApiResponse>> SendKsaIcrPartialOrCancelEInvoice([FromBody] SendKsaIcrGenerateEInvoiceApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.SendKsaIcrPartialOrCancelEInvoiceAsync(Mapper.Map<SendKsaIcrGenerateEInvoiceDto>(request)));

        }

        #endregion

        [SwaggerOperation(
          Summary = "Provides clients to get filtered visa rejection application list",
          Description = "Provides clients to get filtered visa rejection application list")]
        [HttpPost(ApiMethodName.Appointment.GetApplicationsVisaRejectionReport)]
        public Task<ApiResponse<GetApplicationsVisaRejectionReportApiResponse>> GetApplicationsVisaRejectionReport([FromBody] GetApplicationsVisaRejectionReportApiRequest request)
        {
            return Process<GetApplicationsVisaRejectionReportApiResponse, GetApplicationsVisaRejectionReportResponseDto>(request, () => _appointmentService.GetApplicationsVisaRejectionReportAsync(Mapper.Map<GetApplicationsVisaRejectionReportRequestDto>(request)));
        }
        #endregion

        #region PhotoBooth

        [SwaggerOperation(
            Summary = "Provides clients to add new photo booth with 1 hour expire time",
            Description = "Excep ApplicationId, all fields are required. Between Application & Photo Booth, there is a 1to1 relation so 2 insert for one ApplicationId cannot be done")]
        [HttpPost(ApiMethodName.Appointment.AddPhotoBooth)]
        public Task<ApiResponse<AddApiResponse>> AddPhotoBooth([FromBody] AddPhotoBoothApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.AddPhotoBoothAsync(Mapper.Map<AddPhotoBoothRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Delete existing Photo Booth",
            Description = "Delete Photo Booth by id, if not found Photo Booth, it throws not found exception")]
        [HttpDelete(ApiMethodName.Appointment.DeletePhotoBooth + "{id}")]
        public Task<ApiResponse<DeleteApiResponse>> DeletePhotoBooth(int id)
        {
            var request = new DeleteApiRequest { Id = id };

            return Process<DeleteApiResponse, DeleteResponseDto>(request, () => _appointmentService.DeletePhotoBoothAsync(Mapper.Map<DeleteRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to check Photo Booth validation",
            Description = "Get validation result by Photo Booth id")]
        [HttpGet(ApiMethodName.Appointment.CheckPhotoBoothValidation + "{id}")]
        public Task<ApiResponse<PhotoBoothValidationApiResponse>> CheckPhotoBoothValidation(long id)
        {
            return Process<PhotoBoothValidationApiResponse, PhotoBoothValidationResponseDto>(new EmptyApiRequest(), () => _appointmentService.CheckPhotoBoothValidationAsync(id));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of paginated applications without appointment referencee of current date",
            Description = "Get collection of paginated Photo Booth by using filtering parameter")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedNonReferencedPhotoBooth)]
        public Task<ApiResponse<PaginationApiResponse<PaginatedPhotoBoothsApiResponse>>> GetPaginatedNonReferencedPhotoBooth([FromBody] PaginatedPhotoBoothApiRequest request)
        {
            return Process<PaginationApiResponse<PaginatedPhotoBoothsApiResponse>, Pagination<PaginatedPhotoBoothsResponseDto>>(request, () => _appointmentService.GetPaginatedNonReferencedPhotoBoothAsync(Mapper.Map<PaginatedPhotoBoothRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of paginated photo booth applications with appointment reference of current date",
            Description = "Get collection of paginated Photo Booth by using filtering parameter")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedReferencedPhotoBooth)]
        public Task<ApiResponse<PaginationApiResponse<PaginatedPhotoBoothsApiResponse>>> GetPaginatedReferencedPhotoBooth([FromBody] PaginatedPhotoBoothApiRequest request)
        {
            return Process<PaginationApiResponse<PaginatedPhotoBoothsApiResponse>, Pagination<PaginatedPhotoBoothsResponseDto>>(request, () => _appointmentService.GetPaginatedReferencedPhotoBoothAsync(Mapper.Map<PaginatedPhotoBoothRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of paginated photo booth applications with appointment reference of current date",
            Description = "Get collection of paginated Photo Booth by using filtering parameter")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedPhotoBooth)]
        public Task<ApiResponse<PaginationApiResponse<PaginatedPhotoBoothsApiResponse>>> GetPaginatedPhotoBooth([FromBody] PaginatedPhotoBoothApiRequest request)
        {
            return Process<PaginationApiResponse<PaginatedPhotoBoothsApiResponse>, Pagination<PaginatedPhotoBoothsResponseDto>>(request, () => _appointmentService.GetPaginatedPhotoBoothAsync(Mapper.Map<PaginatedPhotoBoothRequestDto>(request)));
        }
        [SwaggerOperation(
            Summary = "Provides clients to get Photo Booth application",
            Description = "Get details by Photo Booth id")]
        [HttpGet(ApiMethodName.Appointment.GetPhotoBooth + "{id}")]
        public Task<ApiResponse<PhotoBoothApiResponse>> GetPhotoBooth(int id)
        {
            return Process<PhotoBoothApiResponse, PhotoBoothResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetPhotoBoothAsync(id));
        }

        #endregion

        #region Insurance

        [SwaggerOperation(
        Summary = "Emaa Hasar Servis Excel",
        Description = "Emaa Hasar Servis Excel")]
        [HttpPost(ApiMethodName.Appointment.GetSanitizedPaginatedEmaaHasarServisExcel)]
        public Task<ApiResponse<PaginationApiResponse<EmaaHasarServisExcelListApiResponse>>> GetSanitizedPaginatedEmaaHasarServisExcel([FromBody] PaginatedEmaaHasarServisApiRequest request)
        {
            return Process<PaginationApiResponse<EmaaHasarServisExcelListApiResponse>, Pagination<EmaaHasarServisExcelListDto>>(request, () => _appointmentService.GetSanitizedPaginatedEmaaHasarServisExcelAsync(Mapper.Map<PaginatedEmaaHasarServisApiRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to update Update Old Insurance Passive",
            Description = "Provides clients to update Update Old Insurance Passive")]
        [HttpPost(ApiMethodName.Appointment.UpdateOldInsurancePassive + "{applicationId}/{policyNumber}")]
        public Task<ApiResponse<UpdateApiResponse>> UpdateOldInsurancePassive(int applicationId, string policyNumber)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(new EmptyApiRequest(), () => _appointmentService.UpdateOldInsurancePassiveAsync(applicationId, policyNumber));
        }

        [SwaggerOperation(
            Summary = "Get Sap Rate Information Daily",
            Description = "Get Sap Rate Information Daily details.")]
        [HttpGet(ApiMethodName.Appointment.GetSapRateInformationFromInsert + "{Tcurr}")]
        public Task<ApiResponse<GetSapRateInformationResponse>> GetSapRateInformation(string tcurr)
        {

            return Process<GetSapRateInformationResponse, GetSapRateInformationResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetSapRateInformationFromInsertAsync(tcurr));
        }

        [SwaggerOperation(
            Summary = "Provides clients to add new claim loss entry log",
            Description = "Provides clients to add new claim loss entry log")]
        [HttpPost(ApiMethodName.Appointment.AddClaimLossEntryLog)]
        public Task<ApiResponse<AddApiResponse>> AddClaimLossEntryLog([FromBody] AddClaimLossEntryLog request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.AddClaimLossEntryLogAsync(Mapper.Map<AddClaimLossEntryLogDto>(request)));

        }

        [SwaggerOperation(
            Summary = "Claim Loss Update Status",
            Description = "Claim Loss Update Status")]
        [HttpPost(ApiMethodName.Appointment.UpdateClaimLossStatus)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateClaimLossStatus([FromBody] AddClaimLossEntryLog request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(new EmptyApiRequest(), () => _appointmentService.UpdateClaimLossStatusAsync(Mapper.Map<AddClaimLossEntryLogDto>(request)));
        }


        #endregion

        #region PreApplication

        [SwaggerOperation(
            Summary = "Provides clients to add new pre-application",
            Description = "Provides clients to add new pre-application.")]
        [HttpPost(ApiMethodName.Appointment.AddPreApplication)]
        public Task<ApiResponse<AddApiResponse>> AddPreApplication([FromBody] AddUpdatePreApplicationApiRequest request)
        {
            return Process<AddApiResponse, AddApiResponse>(request, () => this.AddPreApplicationAndSendNotifications(request));
        }

        internal async Task<AddApiResponse> AddPreApplicationAndSendNotifications(AddUpdatePreApplicationApiRequest request)
        {
            var result = await _appointmentService.AddPreApplicationAsync(Mapper.Map<AddUpdatePreApplicationRequestDto>(request)).ConfigureAwait(false);

            // TODO - Replace with Azure FN instead of Task.Run

            return new AddApiResponse() { Id = result.Id };
        }

        [SwaggerOperation(
            Summary = "Provides clients to get mail.",
            Description = "Provides clients get mail about pre-application details.")]
        [HttpPost(ApiMethodName.Appointment.AddPreApplicationConfirmationPdfMailBefore)]
        public Task<ApiResponse<UpdateApiResponse>> AddPreApplicationConfirmationPdfMailBefore([FromBody] AddUpdatePreApplicationApiRequest request3)
        {
            return Process<UpdateApiResponse, UpdateApiResponse>(request3, () => this.AddPreApplicationConfirmationPdfMail(request3));
        }

        internal async Task<UpdateApiResponse> AddPreApplicationConfirmationPdfMail(AddUpdatePreApplicationApiRequest request3)
        {
            if (request3.SlotId == 0) // If walkin application
            {
                return await Task.FromResult(new UpdateApiResponse() { Result = true }).ConfigureAwait(false);
            }

            var notificationContentAddAppointment = await _notificationService.GetBranchNotificationContent(
                (int)NotificationType.NewPreApplication, request3.BranchId, request3.LanguageId).ConfigureAwait(false);

            if (!string.IsNullOrEmpty(request3.BranchApplicationCountryName))
            {
                request3.BranchApplicationCountryName = Contracts.Extensions.StringExtensions.GetUntilOrEmpty(
                    request3.BranchApplicationCountryName, "Şube");
                request3.BranchApplicationCountryName = Contracts.Extensions.StringExtensions.GetUntilOrEmpty(
                    request3.BranchApplicationCountryName, "Branch");
            }

            if (!notificationContentAddAppointment.HasNotificationContent &&
                request3.Applicants.All(r => r.Email.IsNullOrWhitespace() && r.PhoneNumber.IsNullOrWhitespace()))
            {
                return await Task.FromResult(new UpdateApiResponse() { Result = true }).ConfigureAwait(false);
            }

            var attaches = new List<Attachment>
            {
                new Attachment
                {
                    Content = Convert.ToBase64String(request3.pdfFile.ToArray()),
                    FileName = $"{NotificationType.NewPreApplication.GetDescription()}.pdf"
                }
            };

            var basicGuidelineAttach = await _notificationService.GetBasicGuidelineByBranch(
                HostingEnvironment.ContentRootPath, request3.BranchId);

            if (basicGuidelineAttach != null)
            {
                attaches.Add(basicGuidelineAttach);
            }

            await ProcessNotificationAsync(NotificationType.NewPreApplication, request3, notificationContentAddAppointment, attaches, default);

            return await Task.FromResult(new UpdateApiResponse() { Result = true }).ConfigureAwait(false);
        }


        [SwaggerOperation(
            Summary = "Get PreApplication Confirmation PDF informations by Slot id and Language id",
            Description = "Creating PreApplication Confirmation PDF with Slot id and Language id")]
        [HttpGet(ApiMethodName.Appointment.AddUpdatePreApplicationConfirmationPdf + "{slotId}/{preApplicationId}/{languageId}")]
        public Task<ApiResponse<PreApplicationConfirmationPdfMailApiResponse>> AddUpdatePreApplicationConfirmationPdf(int slotId, int preApplicationId, int languageId)
        {
            return Process<PreApplicationConfirmationPdfMailApiResponse, PreApplicationConfirmationPdfMailResponseDto>(new EmptyApiRequest(), () => _appointmentService.AddUpdatePreApplicationConfirmationPdfAsync(slotId, preApplicationId, languageId));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of paginated pre applications",
            Description = "Get collection of paginated applications by using filtering parameters")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedPreApplications)]
        public Task<ApiResponse<PaginationApiResponse<PaginatedPreApplicationApiResponse>>> GetPaginatedPreApplications([FromBody] PaginatedPreApplicationApiRequest request)
        {
            return Process<PaginationApiResponse<PaginatedPreApplicationApiResponse>, Pagination<PaginatedPreApplicationResponseDto>>(request, () => _appointmentService.GetPaginatedPreApplicationsAsync(Mapper.Map<PaginatedPreApplicationRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get pre application details",
            Description = "Get collection of pre application details with applicant list")]
        [HttpGet(ApiMethodName.Appointment.GetPreApplication + "{id}")]
        public Task<ApiResponse<PreApplicationApiResponse>> GetPreApplication(int id)
        {
            var request = new GetPreApplicationApiRequest() { Id = id };
            return Process<PreApplicationApiResponse, PreApplicationResponseDto>(request, () => _appointmentService.GetPreApplicationAsync(Mapper.Map<GetPreApplicationRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to delete pre application with applicants",
            Description = "Delete pre application by id.")]
        [HttpDelete(ApiMethodName.Appointment.DeletePreApplication)]
        public Task<ApiResponse<DeleteApiResponse>> DeletePreApplication([FromBody] DeletePreApplicationApiRequest request)
        {
            return Process<DeleteApiResponse, DeleteResponseDto>(new EmptyApiRequest(), () => _appointmentService.DeletePreApplicationAsync(Mapper.Map<DeletePreApplicationRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to delete applicant of pre application",
            Description = "Delete existing individual applicant of group or family applications")]
        [HttpDelete(ApiMethodName.Appointment.DeletePreApplicationApplicant)]
        public Task<ApiResponse<DeleteApiResponse>> DeletePreApplicationApplicant([FromBody] DeletePreApplicationApplicantApiRequest request)
        {
            return Process<DeleteApiResponse, DeleteResponseDto>(new EmptyApiRequest(), () => _appointmentService.DeletePreApplicationApplicantAsync(Mapper.Map<DeletePreApplicationApplicantRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to update existing pre-application",
            Description = "Provides clients to update existing pre-application.")]
        [HttpPost(ApiMethodName.Appointment.UpdatePreApplication)]
        public Task<ApiResponse<UpdatePreApplicationApiResponse>> UpdatePreApplication([FromBody] AddUpdatePreApplicationApiRequest request)
        {
            return Process<UpdatePreApplicationApiResponse, UpdatePreApplicationApiResponse>(request, () => this.UpdatePreApplicationAndSendNotifications(request));
        }

        internal async Task<UpdatePreApplicationApiResponse> UpdatePreApplicationAndSendNotifications(AddUpdatePreApplicationApiRequest request)
        {
            var result = await _appointmentService.UpdatePreApplicationAsync(Mapper.Map<AddUpdatePreApplicationRequestDto>(request)).ConfigureAwait(false);

            // TODO - Replace with Azure FN instead of Task.Run

            var branch = await _managementService.GetBranchApplicationCountryAsync(new Contracts.Entities.Dto.Management.BranchApplicationCountry.Requests.BranchApplicationCountryRequestDto()
            {
                LanguageId = request.GetLanguageId(),
                Id = request.BranchApplicationCountryId
            }).ConfigureAwait(false);

            var slot = await _managementService.GetSlotAsync(request.SlotId).ConfigureAwait(false);

            var notificationContentAddAppointment = await _notificationService.GetBranchNotificationContent(
                (int)NotificationType.NewPreApplication, branch.BranchId, request.GetLanguageId()).ConfigureAwait(false);
            var notificationContentUpdateAppointment = await _notificationService.GetBranchNotificationContent(
                (int)NotificationType.UpdatePreApplication, branch.BranchId, request.GetLanguageId()).ConfigureAwait(false);

            if (!string.IsNullOrEmpty(branch.BranchName))
            {
                request.BranchApplicationCountryName = Contracts.Extensions.StringExtensions.GetUntilOrEmpty(
                    branch.BranchName, "Şube");
                request.BranchApplicationCountryName = Contracts.Extensions.StringExtensions.GetUntilOrEmpty(
                    branch.BranchName, "Branch");
            }

            if (!notificationContentAddAppointment.HasNotificationContent &&
                !notificationContentUpdateAppointment.HasNotificationContent &&
                request.Applicants.All(r => r.Email.IsNullOrWhitespace() && r.PhoneNumber.IsNullOrWhitespace()))
            {
                return new UpdatePreApplicationApiResponse();
            }

            var attaches = new List<Attachment>
            {
                 new Attachment
                 {
                     Content = Convert.ToBase64String(request.pdfFile.ToArray()),
                     FileName = $"[FileName].pdf"
                 }
            };

            var basicGuidelineAttach = await _notificationService.GetBasicGuidelineByBranch(
                HostingEnvironment.ContentRootPath, branch.BranchId);

            var preApplicationHistory =
                await _appointmentService.GetPreApplicationApplicantHistoryByPreApplicationAsync(request.PreApplicationId);

            foreach (var applicant in request.Applicants)
            {
                applicant.IsEmailUpdated = preApplicationHistory.PreApplicationApplicantHistories != null && preApplicationHistory.PreApplicationApplicantHistories
                    .Any(q => q.PreApplicationApplicantId == applicant.ApplicantId
                              && q.Changes.All(a => a.PropertyName == "Email"));
            }

            NotificationType notificationType =
                request.IsUpdated && request.Applicants.Any(a => a.IsEmailUpdated) && !result.isSlotDateChanged
                    ? NotificationType.NewPreApplication
                    : NotificationType.UpdatePreApplication;

            UpdateAttachmentFilenames(attaches, notificationType.GetDescription());
            if (basicGuidelineAttach != null && notificationType is NotificationType.NewPreApplication)
            {
                attaches.Add(basicGuidelineAttach);
            }

            var notificationContent = notificationType == NotificationType.NewPreApplication
                ? notificationContentAddAppointment
                : notificationContentUpdateAppointment;

            request.BranchId = branch.BranchId;
            request.BranchNameEn = branch.BranchNameEn;
            request.BranchEmailProviderId = branch.BranchEmailProviderId;
            request.BranchSmsProviderId = branch.BranchSmsProviderId;
            request.BranchSmsSender = branch.BranchSmsSender;
            request.BranchSmsIsSendByPrefix = branch.BranchSmsIsSendByPrefix;
            request.BranchCountryId = branch.BranchCountryId;

            await ProcessNotificationAsync(notificationType, request, notificationContent, attaches, slot.SlotTime);

            return new UpdatePreApplicationApiResponse();
        }

        private void UpdateAttachmentFilenames(List<Attachment> attaches, string description)
        {
            foreach (var attachment in attaches)
            {
                attachment.FileName = attachment.FileName.Replace("[FileName]", description);
            }
        }

        private async Task ProcessNotificationAsync(NotificationType notificationType, AddUpdatePreApplicationApiRequest request, BranchNotificationContentDto notificationContent, List<Attachment> attaches, DateTimeOffset? slotTime)
        {
            var notifications = new List<NotificationRequest.Notification>();

            if (notificationContent.MailContent != null)
            {
                notifications.Add(new NotificationRequest.Notification()
                {
                    Email = request.Applicants.Select(p => p.Email).Select(r => new NotificationRequest.Applicant
                    {
                        Contact = r,
                        ProviderId = request.BranchEmailProviderId,
                        Sender = string.Empty,
                    }),
                    Attaches = attaches,
                    ApplicationDate = request.ApplicationDate,
                    BranchName = request.BranchApplicationCountryName,
                    ApplicationNo = request.ApplicationNo,
                    Text = request.Note,
                    ContentRequest = new NotificationContentRequest()
                    {
                        FooterLanguageId = notificationContent.FooterLanguageId,
                        Subject = notificationContent.Subject,
                        Content = notificationContent.MailContent
                            .Replace("[DATE]", slotTime.HasValue
                                ? slotTime.Value.ToString("dd.MM.yyyy")
                                : request.ApplicationDate.ToString("dd.MM.yyyy"))
                            .Replace("[TIME]", slotTime.HasValue ? slotTime.Value.ToString("HH:mm") : request.ApplicationDate.ToString("HH:mm"))
                            .Replace("[BRANCH]", request.BranchApplicationCountryName) + "<br><br><br>" + request.Note,
                    }
                });
            }

            if (request.BranchSmsSender != null && notificationContent.SmsContent != null)
            {
                notifications.Add(new NotificationRequest.Notification()
                {
                    IsSms = true,
                    Phone = request.BranchSmsIsSendByPrefix
                        ? Mapper.Map<IEnumerable<NotificationRequest.Applicant>>(await _notificationService.UpdateSmsProvidersByPrefix(request.BranchId, request.Applicants.Select(p => p.PhoneNumber).Select(r => new SmsProviderPrefixDto { Contact = r })))
                        : request.Applicants.Select(p => p.PhoneNumber).Select(r => new NotificationRequest.Applicant
                        {
                            Contact = r,
                            ProviderId = request.BranchSmsProviderId,
                            Sender = request.BranchSmsSender
                        }),
                    ApplicationDate = request.ApplicationDate,
                    CountryId = request.CountryId,
                    BranchId = request.BranchId,
                    BranchName = request.BranchApplicationCountryName,
                    BranchCountryId = request.BranchCountryId,
                    BranchNameEn = request.BranchNameEn,
                    ApplicationNo = request.ApplicationNo,
                    Text = request.Note,
                    ContentRequest = new NotificationContentRequest()
                    {
                        FooterLanguageId = notificationContent.FooterLanguageId,
                        Subject = notificationContent.Subject,
                        Content = notificationContent.SmsContent
                            .Replace("[DATE]", slotTime.HasValue
                                ? slotTime.Value.ToString("dd.MM.yyyy")
                                : request.ApplicationDate.ToString("dd.MM.yyyy"))
                            .Replace("[TIME]", slotTime.HasValue ? slotTime.Value.ToString("HH:mm") : request.ApplicationDate.ToString("HH:mm"))
                            .Replace("[BRANCH]", request.BranchApplicationCountryName)
                    }
                });
            }

            new NotificationProcessor(_emailService, _smsService, _notificationService)
                .ProcessNotification(notificationType, request.LanguageId, new NotificationRequest()
                {
                    Notifications = notifications
                });
        }

        [SwaggerOperation(
            Summary = "Provides clients to postpone pre application slot",
            Description = "Provides clients to postpone existing pre-application.")]
        [HttpPost(ApiMethodName.Appointment.PostponePreApplication)]
        public Task<ApiResponse<UpdateApiResponse>> PostponePreApplication([FromBody] PostponePreApplicationApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.PostponePreApplicationAsync(Mapper.Map<PostponePreApplicationRequestDto>(request)));
        }


        [SwaggerOperation(
            Summary = "Provides clients to get filtered appointment list",
            Description = "Provides clients to get filtered appointment list")]
        [HttpPost(ApiMethodName.Appointment.GetPreApplicationReport)]
        public Task<ApiResponse<PreApplicationReportApiResponse>> GetPreApplicationReport([FromBody] GetPreApplicationReportApiRequest request)
        {
            return Process<PreApplicationReportApiResponse, PreApplicationReportResponseDto>(request, () => _appointmentService.GetPreApplicationReportAsync(Mapper.Map<GetPreApplicationReportRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get report that gives the repeated passportnumber and phonenumber datas",
            Description = "Provides clients gives the repeated passportnumber and phonenumber datas")]
        [HttpPost(ApiMethodName.Appointment.GetProcedureControlReport)]
        public Task<ApiResponse<ProcedureControlReportApiResponse>> GetProcedureControlReport([FromBody] GetProcedureControlReportApiRequest request)
        {
            return Process<ProcedureControlReportApiResponse, ProcedureControlReportResponseDto>(request, () => _appointmentService.GetProcedureControlReportAsync(Mapper.Map<GetProcedureControlReportRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Get Extra Fee by Extra Fee id",
            Description = "Retrieve existing Extra Fee by id, returns name and id of Extra Fee.")]
        [HttpGet(ApiMethodName.Appointment.ActivatePreApplication + "{preApplicationId}/{userId}")]
        public Task<ApiResponse<UpdateApiResponse>> ActivatePreApplication(int preApplicationId, int userId)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(new EmptyApiRequest(), () => _appointmentService.ActivatePreApplicationAsync(preApplicationId, userId));
        }

        [SwaggerOperation(
            Summary = "Provides clients to QMS WalkIn Call Center Error",
            Description = "Provides clients to QMS WalkIn Call Center Error")]
        [HttpGet(ApiMethodName.Appointment.CallCenterError + "{passportNumber}")]
        public Task<ApiResponse<CheckCallCenterErrorOrFutureAppointmentApiResponse>> QmsWalkInCCError(string passportNumber)
        {
            return Process<CheckCallCenterErrorOrFutureAppointmentApiResponse, CheckCallCenterErrorOrFutureAppointmentResponseDto>(new EmptyApiRequest(), () => _appointmentService.CallCenterError(passportNumber));
        }

        [SwaggerOperation(
           Summary = "Provides clients to QMS WalkIn Check Future Appointment",
           Description = "Provides clients to QMS WalkIn Check Future Appointment")]
        [HttpGet(ApiMethodName.Appointment.CheckFutureAppointment + "{passportNumber}/{branchId}")]
        public Task<ApiResponse<CheckCallCenterErrorOrFutureAppointmentApiResponse>> QmsCheckFutureAppointment(string passportNumber, int branchId)
        {
            return Process<CheckCallCenterErrorOrFutureAppointmentApiResponse, CheckCallCenterErrorOrFutureAppointmentResponseDto>(new EmptyApiRequest(), () => _appointmentService.CheckFutureAppointment(passportNumber, branchId));
        }


        [SwaggerOperation(
            Summary = "Provides clients to get PreApplication Applicant History by PreApplication id",
            Description = "Retrieve existing PreApplication Applicant History by PreApplication id.")]
        [HttpGet(ApiMethodName.Appointment.GetPreApplicationApplicantHistoryByPreApplication + "{preApplicationId}")]
        public Task<ApiResponse<GetPreApplicationApplicantHistoryByPreApplicationApiResponse>> GetPreApplicationApplicantHistoryByPreApplication(int preApplicationId)
        {
            return Process<GetPreApplicationApplicantHistoryByPreApplicationApiResponse, GetPreApplicationApplicantHistoryByPreApplicationResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetPreApplicationApplicantHistoryByPreApplicationAsync(preApplicationId));
        }

        #endregion

        #region ApplicationFile

        [SwaggerOperation(
            Summary = "Provides clients to add new application file",
            Description = "-")]
        [HttpPost(ApiMethodName.Appointment.AddApplicationFile)]
        public Task<ApiResponse<AddApiResponse>> AddApplicationFile([FromBody] AddApplicationFileApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.AddApplicationFileAsync(Mapper.Map<AddApplicationFileRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of paginated application files",
            Description = "Get collection of paginated application files by using filtering parameters")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedApplicationFiles)]
        public Task<ApiResponse<PaginationApiResponse<ApplicationFilesApiResponse>>> GetPaginatedApplicationFiles([FromBody] PaginatedApplicationFilesApiRequest request)
        {
            return Process<PaginationApiResponse<ApplicationFilesApiResponse>, Pagination<ApplicationFilesDto>>(request, () => _appointmentService.GetPaginatedApplicationFilesAsync(Mapper.Map<PaginatedApplicationFilesRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of application files",
            Description = "Get collection of application files")]
        [HttpPost(ApiMethodName.Appointment.GetApplicationFiles)]
        public Task<ApiResponse<ApplicationFilesApiResponse>> GetApplicationFiles([FromBody] PaginatedApplicationFilesApiRequest request)
        {
            return Process<ApplicationFilesApiResponse, ApplicationFilesDto>(request, () => _appointmentService.GetApplicationFilesAsync(Mapper.Map<PaginatedApplicationFilesRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to delete existing application file",
            Description = "Delete existing application file by id.")]
        [HttpDelete(ApiMethodName.Appointment.DeleteApplicationFile + "{id}")]
        public Task<ApiResponse<DeleteApiResponse>> DeleteApplicationFile(int id)
        {
            var request = new DeleteApiRequest { Id = id };

            return Process<DeleteApiResponse, DeleteResponseDto>(request, () => _appointmentService.DeleteApplicationFileAsync(Mapper.Map<DeleteRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to not get more than one appointment",
            Description = "Prevents to get another appointment by using filtering Passport Number parameter")]
        [HttpGet(ApiMethodName.Appointment.GetPreApplicationNotExistPassportNumber + "{passportNumber}")]
        public Task<ApiResponse<UpdateApiResponse>> GetPreApplicationNotExistPassportNumber(string passportNumber)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetPreApplicationNotExistPassportNumberAsync(passportNumber));
        }

        #endregion

        #region ApplicationNote

        [SwaggerOperation(
            Summary = "Provides clients to add new application note",
            Description = "-")]
        [HttpPost(ApiMethodName.Appointment.AddApplicationNote)]
        public Task<ApiResponse<AddApiResponse>> AddApplicationNote([FromBody] AddApplicationNoteApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.AddApplicationNoteAsync(Mapper.Map<AddApplicationNoteRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of paginated application notes",
            Description = "Get collection of paginated application notes by using filtering parameters")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedApplicationNotes)]
        public Task<ApiResponse<PaginationApiResponse<ApplicationNotesApiResponse>>> GetPaginatedApplicationNotes([FromBody] PaginatedApplicationNotesApiRequest request)
        {
            return Process<PaginationApiResponse<ApplicationNotesApiResponse>, Pagination<ApplicationNotesDto>>(request, () => _appointmentService.GetPaginatedApplicationNotesAsync(Mapper.Map<PaginatedApplicationNotesRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to delete existing application note",
            Description = "Delete existing application note by id.")]
        [HttpDelete(ApiMethodName.Appointment.DeleteApplicationNote + "{id}")]
        public Task<ApiResponse<DeleteApiResponse>> DeleteApplicationNote(int id)
        {
            return Process<DeleteApiResponse, DeleteResponseDto>(new EmptyApiRequest(), () => _appointmentService.DeleteApplicationNoteAsync(id));
        }

        #endregion

        #region ApplicationOfficialNote

        [SwaggerOperation(
          Summary = "Provides clients to add new application official note",
          Description = "-")]
        [HttpPost(ApiMethodName.Appointment.AddApplicationOfficialNote)]
        public Task<ApiResponse<AddApiResponse>> AddApplicationOfficialNote([FromBody] AddApplicationOfficialNoteApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.AddApplicationOfficialNoteAsync(Mapper.Map<AddApplicationOfficialNoteRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of paginated application notes",
            Description = "Get collection of paginated application notes by using filtering parameters")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedApplicationOfficialNotes)]
        public Task<ApiResponse<PaginationApiResponse<ApplicationOfficialNotesApiResponse>>> GetPaginatedApplicationOfficialNotes([FromBody] PaginatedApplicationOfficialNotesApiRequest request)
        {
            return Process<PaginationApiResponse<ApplicationOfficialNotesApiResponse>, Pagination<ApplicationOfficialNotesDto>>(request, () => _appointmentService.GetPaginatedApplicationOfficialNotesAsync(Mapper.Map<PaginatedApplicationOfficialNotesRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to delete existing application note",
            Description = "Delete existing application note by id.")]
        [HttpDelete(ApiMethodName.Appointment.DeleteApplicationOfficialNote + "{id}")]
        public Task<ApiResponse<DeleteApiResponse>> DeleteApplicationOfficialNote(int id)
        {
            return Process<DeleteApiResponse, DeleteResponseDto>(new EmptyApiRequest(), () => _appointmentService.DeleteApplicationOfficialNoteAsync(id));
        }
        #endregion

        #region ApplicationVisaDecision

        [SwaggerOperation(
            Summary = "Provides clients to get visa decision information according to his/her fee and insurance selection ",
            Description = "Provides clients to get visa decision information according to his/her fee and insurance selection ")]
        [HttpGet(ApiMethodName.Appointment.GetApplicationVisaDecisionInformation + "{id}/{languageId}")]
        public Task<ApiResponse<GetApplicationVisaDecisionInformationApiResponse>> GetApplicationVisaDecisionInformation(int id, int languageId)
        {
            return Process<GetApplicationVisaDecisionInformationApiResponse, GetApplicationVisaDecisionInformationResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetApplicationVisaDecisionInformation(id, languageId));
        }

        [SwaggerOperation(
            Summary = "Provides clients to add new application visa decision",
            Description = "-")]
        [HttpPost(ApiMethodName.Appointment.AddApplicationVisaDecision)]
        public Task<ApiResponse<AddApiResponse>> AddApplicationVisaDecision([FromBody] AddApplicationVisaDecisionApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.AddApplicationVisaDecisionAsync(Mapper.Map<AddApplicationVisaDecisionRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get application visa decision",
            Description = "-")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedApplicationVisaDecisions)]
        public Task<ApiResponse<PaginationApiResponse<ApplicationVisaDecisionsApiResponse>>> GetPaginatedApplicationVisaDecisions([FromBody] PaginatedApplicationVisaDecisionsApiRequest request)
        {
            return Process<PaginationApiResponse<ApplicationVisaDecisionsApiResponse>, Pagination<ApplicationVisaDecisionsDto>>(request, () => _appointmentService.GetPaginatedApplicationVisaDecisionsAsync(Mapper.Map<PaginatedApplicationVisaDecisionsRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to delete existing application visa decision",
            Description = "Delete existing application visa decision by id.")]
        [HttpDelete(ApiMethodName.Appointment.DeleteApplicationVisaDecision + "{id}")]
        public Task<ApiResponse<DeleteApiResponse>> DeleteApplicationVisaDecision(int id)
        {
            return Process<DeleteApiResponse, DeleteResponseDto>(new EmptyApiRequest(), () => _appointmentService.DeleteApplicationVisaDecisionAsync(id));
        }

        #endregion

        #region ApplicationRelationalServices
        [HttpGet(ApiMethodName.Appointment.GetRelatedServicesApplications + "{id}")]
        public Task<ApiResponse<ApplicationRelatedServicesApiResponse>> GetRelatedServicesApplications(int id)
        {
            return Process<ApplicationRelatedServicesApiResponse, ApplicationRelatedServicesDto>(new EmptyApiRequest(), () => _appointmentService.GetRelatedServicesApplicationsAsync(id, LanguageId));
        }
        #endregion

        #region ApplicationSurvey


        [SwaggerOperation(
           Summary = "Provides clients to get application information survey for dubai details",
           Description = "Get information survey for dubai details by id.")]
        [HttpGet(ApiMethodName.Appointment.GetSanitizedApplicationDubaiSurvey + "{id}")]
        public Task<ApiResponse<ApplicationSurveyDubaiApiResponse>> GetSanitizedApplicationDubaiSurvey(int id)
        {
            var request = new ApplicationDubaiSurveyApiRequest { Id = id };
            return Process<ApplicationSurveyDubaiApiResponse, ApplicationSurveyDubaiDto>(request, () => _appointmentService.GetSanitizedApplicationDubaiSurveyAsync(Mapper.Map<ApplicationSurveyDubaiRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to add new application survey",
            Description = "-")]
        [HttpPost(ApiMethodName.Appointment.SaveApplicationSurveyDubai)]
        public Task<ApiResponse<AddApiResponse>> SaveApplicationSurveyDubai([FromBody] SaveApplicationSurveyDubaiApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.SaveApplicationSurveyDubaiAsync(Mapper.Map<SaveApplicationSurveyDubaiRequestDto>(request)));
        }

        [SwaggerOperation(
           Summary = "Provides clients to update application survey for dubai details",
           Description = "Update survey for dubai details by id.")]
        [HttpGet(ApiMethodName.Appointment.UpdateApplicationSurveyDubai + "{id}/{languageId}")]
        public Task<ApiResponse<ApplicationUpdateSurveyDubaiApiResponse>> UpdateApplicationSurveyDubai(int id, int languageId)
        {
            return Process<ApplicationUpdateSurveyDubaiApiResponse, ApplicationUpdateSurveyDubaiDto>(new EmptyApiRequest(), () => _appointmentService.UpdateApplicationSurveyDubaiAsync(id, languageId));
        }

        [SwaggerOperation(
            Summary = "Provides clients to get collection of paginated application survey",
            Description = "Get collection of paginated application survey by using filtering parameters")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedApplicationSurvey)]
        public Task<ApiResponse<PaginationApiResponse<ApplicationSurveyApiResponse>>> GetPaginatedApplicationSurvey([FromBody] PaginatedApplicationSurveyApiRequest request)
        {
            return Process<PaginationApiResponse<ApplicationSurveyApiResponse>, Pagination<ApplicationSurveyDto>>(request, () => _appointmentService.GetPaginatedApplicationSurveyAsync(Mapper.Map<PaginatedApplicationSurveyRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides clients to delete existing application survey",
            Description = "Delete existing application survey by id.")]
        [HttpDelete(ApiMethodName.Appointment.DeleteApplicationSurveyDubai + "{id}/{userId}")]
        public Task<ApiResponse<DeleteApiResponse>> DeleteApplicationSurveyDubai(int id, int userId)
        {
            return Process<DeleteApiResponse, DeleteResponseDto>(new EmptyApiRequest(), () => _appointmentService.DeleteApplicationSurveyDubaiAsync(id, userId));
        }

        [SwaggerOperation(
            Summary = "Provides clients to existince of application survey",
            Description = "Get existince of application survey by id.")]
        [HttpGet(ApiMethodName.Appointment.IsExistSurvey + "{id}")]
        public Task<ApiResponse<ApplicationIsSurveyExistApiResponse>> IsExistSurvey(int id)
        {
            return Process<ApplicationIsSurveyExistApiResponse, ApplicationIsExistSurveyDto>(new EmptyApiRequest(), () => _appointmentService.IsExistSurveyAsync(id));
        }

        [SwaggerOperation(
         Summary = "Visa rejection refund check for new application",
         Description = "Visa rejection refund check for new application")]
        [HttpPost(ApiMethodName.Appointment.VisaRejectionRefundForNewApplication)]
        public Task<ApiResponse<VisaRejectionRefundForNewApplicationApiResponse>> VisaRejectionRefundForNewApplication([FromBody] VisaRejectionRefundForNewApplicationApiRequest request)
        {
            return Process<VisaRejectionRefundForNewApplicationApiResponse, VisaRejectionRefundForNewApplicationDto>(request, () => _appointmentService.VisaRejectionRefundForNewApplicationAsync(Mapper.Map<VisaRejectionRefundForNewApplicationRequestDto>(request)));
        }
        #endregion

        #region ApplicationRejectionApprovalStatus

        [SwaggerOperation(
            Summary = "Provides client/s to add new rejection approval status to application",
            Description = "Provides client/s to add new rejection approval status to application")]
        [HttpGet(ApiMethodName.Appointment.GetApplicationRejectionApprovalStatus + "{id}")]
        public Task<ApiResponse<GetApplicationRejectionApprovalStatusApiResponse>> GetApplicationRejectionApprovalStatus(int id)
        {
            return Process<GetApplicationRejectionApprovalStatusApiResponse, GetApplicationRejectionApprovalStatusResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetApplicationRejectionApprovalStatusAsync(id));
        }

        [SwaggerOperation(
           Summary = "Provides client/s to complete rejection approval status",
           Description = "Provides client/s to complete rejection approval status")]
        [HttpPut(ApiMethodName.Appointment.CompleteRejectionApprovalHistory)]
        public Task<ApiResponse<UpdateApiResponse>> CompleteRejectionApprovalHistory([FromBody] CompleteRejectionApprovalHistoryApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(new EmptyApiRequest(), () => _appointmentService.CompleteRejectionApprovalHistoryAsync(Mapper.Map<CompleteRejectionApprovalHistoryRequestDto>(request)));
        }

        [SwaggerOperation(
          Summary = "Provides client/s to verify rejection approval status code",
          Description = "Provides client/s to verify rejection approval status code")]
        [HttpPost(ApiMethodName.Appointment.VerifyRejectionApprovalCode)]
        public Task<ApiResponse<VerifyRejectionApprovalCodeApiResponse>> VerifyRejectionApprovalCode([FromBody] VerifyRejectionApprovalCodeApiRequest request)
        {
            return Process<VerifyRejectionApprovalCodeApiResponse, VerifyRejectionApprovalCodeResponseDto>(request, () => _appointmentService.VerifyRejectionApprovalCodeAsync(Mapper.Map<VerifyRejectionApprovalCodeRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides client/s to check supervisor information in given branch",
            Description = "Provides client/s to check supervisor information in given branch")]
        [HttpPost(ApiMethodName.Appointment.CheckSupervisorInformation)]
        public Task<ApiResponse<CheckSupervisorInformationApiResponse>> CheckSupervisorInformation([FromBody] CheckSupervisorInformationApiRequest request)
        {
            return Process<CheckSupervisorInformationApiResponse, CheckSupervisorInformationResponseDto>(request, () => _appointmentService.CheckSupervisorInformationAsync(Mapper.Map<CheckSupervisorInformationRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides client/s to check supervisor information in given branch",
            Description = "Provides client/s to check supervisor information in given branch")]
        [HttpPost(ApiMethodName.Appointment.CheckSupervisorInformationWithLdap)]
        public Task<ApiResponse<CheckSupervisorInformationApiResponse>> CheckSupervisorInformationWithLdap([FromBody] CheckSupervisorInformationApiRequest request)
        {
            return Process<CheckSupervisorInformationApiResponse, CheckSupervisorInformationResponseDto>(request, () => _appointmentService.CheckSupervisorInformationWithLdapAsync(Mapper.Map<CheckSupervisorInformationRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Provides client/s to send rejection approval status notification",
            Description = "Provides client/s to send rejection approval status code")]
        [HttpPost(ApiMethodName.Appointment.SendRejectionApprovalNotification)]
        public Task<ApiResponse<SendRejectionApprovalNotificationApiResponse>> SendRejectionApprovalNotification([FromBody] SendRejectionApprovalNotificationApiRequest request)
        {
            return Process<SendRejectionApprovalNotificationApiResponse, SendRejectionApprovalNotificationResponseDto>(request, () => this.AddRejectionApprovalHistoryAndSendNotifications(Mapper.Map<SendRejectionApprovalNotificationRequestDto>(request)));
        }

        internal async Task<SendRejectionApprovalNotificationResponseDto> AddRejectionApprovalHistoryAndSendNotifications(SendRejectionApprovalNotificationRequestDto request)
        {
            request.Code = new Random().Next(1000, 9999).ToString();

            var resultApprovalHistory = await _appointmentService.AddRejectionApprovalHistoryAsync(Mapper.Map<AddRejectionApprovalHistoryRequestDto>(request)).ConfigureAwait(false);

            var resultBranch = await _managementService.GetBranchAsync(new Contracts.Entities.Dto.Management.Branch.Requests.BranchRequestDto
            {
                Id = request.BranchId,
            }).ConfigureAwait(false);

            if (request.SendNotification)
            {
                var applicationNotificationRequest = new ApplicationNotificationResponseDto()
                {
                    BranchCountryId = resultBranch.CountryId,
                    SmsList = request.SendAsSms
                        ? new List<ApplicationNotificationResponseDto.Notification>()
                        {
                            new()
                            {
                                IsSendByPrefix = resultBranch.IsSendByPrefix,
                                Text = resultApprovalHistory.SmsContent,
                                ApplicantList = new List<ApplicationNotificationResponseDto.Applicant>()
                                {
                                    new()
                                    {
                                        ProviderId = resultBranch.SmsProviderId,
                                        Sender = resultBranch.SmsSender,
                                        Contact = request.PhoneNumber,
                                        Id = request.ApplicationId
                                    }
                                }
                            }
                        }
                        : new List<ApplicationNotificationResponseDto.Notification>(),
                    EmailList = request.SendAsEmail
                        ? new List<ApplicationNotificationResponseDto.Notification>()
                        {
                            new()
                            {
                                Text = resultApprovalHistory.MailContent,
                                Subject = resultApprovalHistory.Subject,
                                ApplicantList = new List<ApplicationNotificationResponseDto.Applicant>()
                                {
                                    new()
                                    {
                                        ProviderId = resultBranch.EmailProviderId,
                                        Sender = string.Empty,
                                        Contact = request.Email,
                                        Id = request.ApplicationId
                                    }
                                }
                            }
                        }
                        : new List<ApplicationNotificationResponseDto.Notification>()
                };

                await SendNotifications(applicationNotificationRequest, resultApprovalHistory.FooterLanguageId, request.UserId, (int)NotificationRouteType.RejectionApproval, (int)NotificationType.RejectionApproval, request.BranchId, resultApprovalHistory.Id);
            }

            return new SendRejectionApprovalNotificationResponseDto()
            {
                Result = true,
            };
        }
        #endregion

        #region Inquiry


        [SwaggerOperation(
            Summary = "Provides client/s to get inquiry informations",
            Description = "Provides client/s to get inquiry informations")]
        [HttpGet(ApiMethodName.Appointment.GetApplicationInquiry + "{id}/{languageId}")]
        public Task<ApiResponse<GetApplicationInquiryApiResponse>> GetApplicationInquiry(int id, int languageId)
        {
            return Process<GetApplicationInquiryApiResponse, GetApplicationInquiryResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetApplicationInquiry(id, languageId));
        }

        [SwaggerOperation(
            Summary = "Provides client/s to get inquiry informations",
            Description = "Provides client/s to get inquiry informations")]
        [HttpGet(ApiMethodName.Appointment.GetInquiryInformation + "{id}/{languageId}")]
        public Task<ApiResponse<GetInquiryInformationApiResponse>> GetInquiryInformation(int id, int languageId)
        {
            return Process<GetInquiryInformationApiResponse, GetInquiryInformationResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetInquiryInformation(id, languageId));
        }

        [SwaggerOperation(
            Summary = "Provides client/s to get inquiry informations",
            Description = "Provides client/s to get inquiry informations")]
        [HttpDelete(ApiMethodName.Appointment.DeleteApplicationInquiry + "{id}/{inquiryId}")]
        public Task<ApiResponse<DeleteApiResponse>> DeleteInquiryInformation(int id, int inquiryId)
        {
            return Process<DeleteApiResponse, DeleteResponseDto>(new EmptyApiRequest(), () => _appointmentService.DeleteApplicationInquiry(id, inquiryId));
        }

        [SwaggerOperation(
            Summary = "Provides client/s to get inquiry informations",
            Description = "Provides client/s to get inquiry informations")]
        [HttpPost(ApiMethodName.Appointment.SaveApplicationInquiry)]
        public Task<ApiResponse<AddApiResponse>> SaveApplicationInquiry([FromBody] SaveApplicationInquiryApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.SaveApplicationInquiry(Mapper.Map<SaveApplicationInquiryRequestDto>(request)));
        }

        #endregion

        #region VisaType

        [SwaggerOperation(
            Summary = "Provides client/s to add new extra fee",
            Description = "Extra fee name must be unique, please check existing Extra Fees on before adding new one.")]
        [HttpPost(ApiMethodName.Appointment.AddVisaType)]
        public Task<ApiResponse<AddApiResponse>> AddVisaType([FromBody] AddVisaTypeApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.AddVisaTypeAsync(Mapper.Map<AddVisaTypeRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Update an existing Extra Fee",
            Description = "Extra fee name must be unique, please check existing Extra Fees on before updating with existing one.")]
        [HttpPut(ApiMethodName.Appointment.UpdateVisaType)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateVisaType([FromBody] UpdateVisaTypeApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.UpdateVisaTypeAsync(Mapper.Map<UpdateVisaTypeRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Get Extra Fee by Extra Fee id",
            Description = "Retrieve existing Extra Fee by id, returns name and id of Extra Fee.")]
        [HttpGet(ApiMethodName.Appointment.GetVisaType + "{id}")]
        public Task<ApiResponse<VisaTypeApiResponse>> GetVisaType(int id)
        {
            return Process<VisaTypeApiResponse, VisaTypeResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetVisaTypeAsync(id));
        }

        [SwaggerOperation(
            Summary = "Delete existing Extra Fee",
            Description = "Delete Extra Fee by id, if not found Extra Fee, it throws not found exception")]
        [HttpDelete(ApiMethodName.Appointment.DeleteVisaType + "{id}")]
        public Task<ApiResponse<DeleteApiResponse>> DeleteVisaType(int id)
        {
            return Process<DeleteApiResponse, DeleteResponseDto>(new EmptyApiRequest(), () => _appointmentService.DeleteVisaTypeAsync(id));
        }

        [SwaggerOperation(
            Summary = "Get Extra Fees with paginated",
            Description = "Retrieve paginated Extra Fees")]
        [HttpPost(ApiMethodName.Appointment.GetPaginatedVisaTypes)]
        public Task<ApiResponse<PaginationApiResponse<VisaTypesApiResponse>>> GetPaginatedVisaTypes([FromBody] PaginatedVisaTypesApiRequest request)
        {
            return Process<PaginationApiResponse<VisaTypesApiResponse>, Pagination<VisaTypesResponseDto>>(request, () => _appointmentService.GetPaginatedVisaTypesAsync(Mapper.Map<PaginatedVisaTypesRequestDto>(request)));
        }

        #endregion

        #region MainExtraFeeCategory

        [SwaggerOperation(
            Summary = "Provides client/s to add main extra fee category",
            Description = "Main extra fee category name must be unique, please check existing main extra fees on before adding new one.")]
        [HttpPost(ApiMethodName.Appointment.AddMainExtraFeeCategory)]
        public Task<ApiResponse<AddApiResponse>> AddMainExtraFeeCategory([FromBody] AddMainExtraFeeCategoryApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.AddMainExtraFeeCategoryAsync(Mapper.Map<AddMainExtraFeeCategoryRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Update an existing main extra fee category",
            Description = "Main extra fee category name must be unique, please check existing main extra fee category on before updating with existing one.")]
        [HttpPut(ApiMethodName.Appointment.UpdateMainExtraFeeCategory)]
        public Task<ApiResponse<UpdateApiResponse>> UpdateMainExtraFeeCategory([FromBody] UpdateMainExtraFeeCategoryApiRequest request)
        {
            return Process<UpdateApiResponse, UpdateResponseDto>(request, () => _appointmentService.UpdateMainExtraFeeCategoryAsync(Mapper.Map<UpdateMainExtraFeeCategoryRequestDto>(request)));
        }

        [SwaggerOperation(
            Summary = "Get main extra fee category by id",
            Description = "Retrieve existing main extra fee category by id, returns name and id of main extra fee category.")]
        [HttpGet(ApiMethodName.Appointment.GetMainExtraFeeCategory + "{id}")]
        public Task<ApiResponse<MainExtraFeeCategoryApiResponse>> GetMainExtraFeeCategory(int id)
        {
            return Process<MainExtraFeeCategoryApiResponse, MainExtraFeeCategoryResponseDto>(new EmptyApiRequest(), () => _appointmentService.GetMainExtraFeeCategoryAsync(id));
        }

        [SwaggerOperation(
            Summary = "Delete existing main extra fee category",
            Description = "Delete main extra fee category by id, if not found main extra fee category, it throws not found exception")]
        [HttpDelete(ApiMethodName.Appointment.DeleteMainExtraFeeCategory + "{id}")]
        public Task<ApiResponse<DeleteApiResponse>> DeleteMainExtraFeeCategory(int id)
        {
            return Process<DeleteApiResponse, DeleteResponseDto>(new EmptyApiRequest(), () => _appointmentService.DeleteMainExtraFeeCategoryAsync(id));
        }

        [SwaggerOperation(
            Summary = "Get main extra fee categories",
            Description = "Retrieve paginated main extra fee categories")]
        [HttpGet(ApiMethodName.Appointment.GetMainExtraFeeCategories)]
        public Task<ApiResponse<MainExtraFeeCategoriesApiResponse>> GetMainExtraFeeCategories()
        {
            var request = new GetMainExtraFeeCategoriesApiRequest();
            request.GetLanguageId();
            return Process<MainExtraFeeCategoriesApiResponse, MainExtraFeeCategoriesResponseDto>(request, () => _appointmentService.GetMainExtraFeeCategoriesAsync(Mapper.Map<GetMainExtraFeeCategoriesRequestDto>(request)));
        }

        #endregion

        #region IHBOrder

        [SwaggerOperation(
            Summary = "Adds new ihb order to job system",
            Description = "Adds new ihb order to job system")]
        [HttpPost(ApiMethodName.Appointment.AddIhbOrder)]
        public Task<ApiResponse<AddApiResponse>> AddIhbOrder([FromBody] AddIhbOrderApiRequest request)
        {
            return Process<AddApiResponse, AddResponseDto>(request, () => _appointmentService.AddIhbOrderAsync(Mapper.Map<AddIhbOrderRequestDto>(request)));
        }

        #endregion
    }
}
