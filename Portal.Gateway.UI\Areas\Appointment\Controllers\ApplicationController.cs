﻿using ClosedXML.Excel;
using Gateway.Http;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Accounting;
using Portal.Gateway.ApiModel.Requests.Appointment.Application;
using Portal.Gateway.ApiModel.Requests.DocumentManagement;
using Portal.Gateway.ApiModel.Requests.Insurance;
using Portal.Gateway.ApiModel.Requests.Insurance.EmaaHasarServisExcel;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.DocumentManagement;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.ApiModel.Responses.Insurance;
using Portal.Gateway.ApiModel.Responses.Management.ApplicationFormElement;
using Portal.Gateway.ApiModel.Responses.Management.RoleAction;
using Portal.Gateway.ApiModel.Responses.QueueMatic.InfoDesk;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Attributes;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using Portal.Gateway.UI.Areas.Appointment.Models;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.Application;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.EmaaHasarServisExcel;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.PDF;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Constants;
using Portal.Gateway.UI.Controllers;
using Portal.Gateway.UI.Extensions;
using Portal.Gateway.UI.Helpers;
using Portal.Gateway.UI.Models;
using Portal.Gateway.UI.Validators;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gateway.Extensions;
using DocumentType = Portal.Gateway.Contracts.Entities.Enums.DocumentType;
using System.Text.RegularExpressions;
using Portal.Gateway.UI.Areas.Cargo.Models.Cargo.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.Cargo;
using Portal.Gateway.ApiModel.Responses.DigitalSignature;
using Portal.Gateway.Contracts.Extensions;
using System.IO.Compression;
using JsonSerializer = System.Text.Json.JsonSerializer;
using Portal.Gateway.ApiModel.Requests.Appointment.PhotoBooth;
using Portal.Gateway.ApiModel.Requests.Appointment.PreApplication;
using Portal.Gateway.ApiModel.Responses.Appointment.PreApplication;
using Portal.Gateway.UI.Areas.Appointment.ViewModels.PreApplication;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Application;
using Portal.Gateway.UI.ViewModels;
using Portal.Gateway.ApiModel.Responses.Management.User;
using Gateway.ObjectStoring;
using Microsoft.Extensions.Configuration;
using Portal.Gateway.UI.Areas.QMS.Models.QMS.Requests;
using Portal.Gateway.UI.Areas.QMS.Models.QMS.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using AddUpdateApplicationViewModel = Portal.Gateway.UI.Areas.Appointment.ViewModels.Application.AddUpdateApplicationViewModel;
using IhbOrderDto = Portal.Gateway.ApiModel.Requests.Appointment.Application.IhbOrderDto;


namespace Portal.Gateway.UI.Areas.Appointment.Controllers
{
	[Area("Appointment")]
	public class ApplicationController : BaseController<ApplicationController>
	{
		private const string QMS_API_RESPONSE_SUCCESS = "SUCCESS";
		private readonly IFileStorage _fileStorage;
        private readonly SessionSettings _sessionSettings;
        private readonly IWebHostEnvironment _environment;

        public ApplicationController(
			IWebHostEnvironment environment,
			IFileStorage fileStorage,
            IOptions<SessionSettings> sessionSettings,
            IConfiguration configuration,
			IOptions<AppSettings> appSettings, IMemoryCache memoryCache) : base(appSettings, memoryCache)
		{

			_fileStorage = fileStorage;
			_sessionSettings = sessionSettings.Value;
			_environment = environment;

		}

		[ActionAttribute(IsMenuItem = true)]
		public async Task<IActionResult> SelectBranchApplicationCountry(string encryptedApplicantId = null, string encryptedTokenId = null, string encryptedApplicantTypeId = null, string encryptedWhiteListId = null)
		{
			if (UserSession.BranchId == null)
			{
				return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
			}

			var apiRequest = new BranchApplicationCountriesApiRequest
			{
				BranchId = UserSession.BranchId.Value
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<BranchApplicationCountriesApiResponse>>
				(apiRequest, ApiMethodName.Appointment.GetBranchApplicationCountries, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return View();

			var viewModel = new SelectBranchApplicationCountryViewModel
			{
				EncryptedApplicantId = encryptedApplicantId,
				EncryptedTokenId = encryptedTokenId,
				EncryptedApplicantTypeId = encryptedApplicantTypeId,
				EncryptedWhiteListId = encryptedWhiteListId,
				BranchApplicationCountries = apiResponse.Data.BranchApplicationCountries.Select(p => new SelectBranchApplicationCountryViewModel.BranchApplicationCountry
				{
					Id = p.Id,
					BranchName = p.BranchName,
					CountryName = p.CountryName
				}).ToList()
			};
			return View(viewModel);
		}

		public async Task<IActionResult> GetBranchApplicationCountryVisaCategorySelectList(int branchApplicationCountryId)
		{
			var apiRequest = new BranchApplicationCountryVisaCategoriesApiRequest
			{
				BranchApplicationCountryId = branchApplicationCountryId,
				IsDeactivatedDataIncluded = false
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<BranchApplicationCountryVisaCategoriesApiResponse>>
				(apiRequest, ApiMethodName.Appointment.GetBranchApplicationCountryVisaCategories, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var activeVisaCategoryIdList = apiResponse.Data.BranchVisaCategories.Select(p => p.Id);

			return Json(apiResponse.Data.VisaCategoryTypes.Where(p => activeVisaCategoryIdList.Contains(p.Id)).Select(x => new SelectListItem { Value = x.Id.ToString(), Text = x.Translations.First(s => s.LanguageId == LanguageId).Name.ToTitleCase() }).OrderBy(o => o.Text));
		}

		public IActionResult GetApplicationNumberOfEntrySelectList()
		{
			return Json(EnumHelper.GetEnumAsDictionary(typeof(NumberOfEntryType)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
		}

		public IActionResult GetApplicationVisaUsedYearSelectList()
		{
			return Json(EnumHelper.GetEnumAsDictionary(typeof(VisaIsUsedYear)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() }));
		}

		#region Application/New
		[ActionAttribute(IsMenuItem = true)]
		public async Task<IActionResult> New(string encryptedBranchApplicationCountryId, string encryptedApplicantId = null, string encryptedTokenId = null, string encryptedRelationalApplicationId = null, string encryptedApplicantTypeId = null, string encryptedWhiteListId = null)
		{
			
			CountriesApiResponse nationalities = new CountriesApiResponse();
			if (MemoryCache.TryGetValue(CacheKeys.CountryCache, out object value2))
			{
				nationalities = (CountriesApiResponse)value2;
			}
			
			var nationalitiesValue =nationalities.Countries.Select(x => new AddUpdateApplicationViewModel.NationalityViewModel{ Id = x.Id, Name = x.Name.ToTitleCase() }).OrderBy(p=>p.Name);

			ViewData["defaultNationalities"] = nationalitiesValue.First();

			if (UserSession.BranchId == null)
			{
				return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
			}

			if (string.IsNullOrEmpty(encryptedBranchApplicationCountryId))
			{
				return RedirectToAction("SelectBranchApplicationCountry", "Application", new { Area = "Appointment", EncryptedApplicantId = encryptedApplicantId, EncryptedTokenId = encryptedTokenId, EncryptedApplicantTypeId = encryptedApplicantTypeId, EncryptedWhiteListId = encryptedWhiteListId });
			}

			var apiRequest = new PreApplicationFormApiRequest
			{
				BranchApplicationCountryId = encryptedBranchApplicationCountryId.ToDecryptInt()
			};

			var applicantId = 0;

			if (!string.IsNullOrEmpty(encryptedApplicantId))
				applicantId = Convert.ToInt32(encryptedApplicantId.ToDecryptInt().ToString());

			if (!string.IsNullOrEmpty(encryptedRelationalApplicationId))
				apiRequest.RelationalApplicationId = encryptedRelationalApplicationId.ToDecryptInt();

            var apiResponsePreApplicationForm = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PreApplicationFormApiResponse>>
				(apiRequest, ApiMethodName.Appointment.GetPreApplicationForm, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponsePreApplicationForm.Validate(out ResultModel result).ConfigureAwait(false))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			var isShowAllowDeniedPassportControl = false;

			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object actions))
			{
				var roleActions = (RoleActionApiResponse)actions;

				isShowAllowDeniedPassportControl = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
					.Any(p => p.RoleActions.Any(q => q.Action.Method == "IsAllowedDeniedPassportNumber" && q.Action.IsActive));
			}
			
			var viewModel = new AddUpdateApplicationViewModel
			{
				BranchApplicationCountryId = encryptedBranchApplicationCountryId.ToDecryptInt(),
				BranchName = apiResponsePreApplicationForm.Data.BranchName,
				CountryId = apiResponsePreApplicationForm.Data.CountryId,
				BranchCountryId = apiResponsePreApplicationForm.Data.BranchCountryId,
				CargoProviderId = apiResponsePreApplicationForm.Data.CargoProviderId,
				IsCargoIntegrationActive = apiResponsePreApplicationForm.Data.IsCargoIntegrationActive,
				ShowCityDropdown = apiResponsePreApplicationForm.Data.ShowCityDropdown,
				IsPrinterIntegrationActive = apiResponsePreApplicationForm.Data.IsPrinterIntegrationActive,
				IsPhotoBoothIntegrationActive = apiResponsePreApplicationForm.Data.IsPhotoBoothIntegrationActive,
				IsPreApplicationConnectionActive = apiResponsePreApplicationForm.Data.IsPreApplicationConnectionActive,
				IsRelatedInsuranceForExemptActive = apiResponsePreApplicationForm.Data.IsRelatedInsuranceForExemptActive,
				ApplicantId = applicantId,
				ShowAllowDeniedPassportControl = isShowAllowDeniedPassportControl,
				CountryName = apiResponsePreApplicationForm.Data.CountryName,
				InformationNotes = apiResponsePreApplicationForm.Data.InformationNotes,
				IraqOpsiyonelSmsCheck = apiResponsePreApplicationForm.Data.IraqOpsiyonelSmsCheck,
				DisableContactInformationVerify = apiResponsePreApplicationForm.Data.DisableContactInformationVerify,
				IsMainApplicant = encryptedRelationalApplicationId == null,
                ShowPaymentMethods = apiResponsePreApplicationForm.Data.ShowPaymentMethods,
                ShowReleatedInvidual = apiResponsePreApplicationForm.Data.IsRelatedInsuranceForExemptActive,
                ApplicationFormElements = apiResponsePreApplicationForm.Data.ApplicationFormElements.Select(p => new AddUpdateApplicationViewModel.ApplicationFormElement
				{
					FormElementId = p.FormElementId,
					IsRequired = p.IsRequired
				}).ToList(),
				ExtraFees = apiResponsePreApplicationForm.Data.ExtraFees.Select(p => new AddUpdateApplicationViewModel.ExtraFee
				{
					ExtraFeeId = p.ExtraFeeId,
					TypeId = p.TypeId,
					MinimumItem = p.MinimumItem,
					CategoryTypeId = p.CategoryTypeId,
					ExtraFeeName = p.ExtraFeeName,
					IsChecked = p.IsAutoChecked,
					Price = p.Price,
					CurrencyId = p.CurrencyId,
					AgeRange = p.AgeRange,
					IsMainFee = p.IsMainFee,
                    IsForRejectedApplications = p.IsForRejectedApplications,
                    RejectionFeePolicyType = CheckRejectionFeePolicyTypeByPeriod(p.PolicyPeriod),
					IsSubFee = p.IsSubFee,
					MainFeeId = p.MainFeeId,
					RelatedMainFeeId = p.RelatedMainFeeId,
				}).ToList()
			};

            // For creating application from QMS
            if (!string.IsNullOrEmpty(encryptedApplicantId))
			{
				var preApplicantId = encryptedApplicantId.ToDecryptInt();
				var applicationType = Convert.ToInt32(encryptedApplicantTypeId?.ToDecrypt()) > 0 ? Convert.ToInt32(encryptedApplicantTypeId?.ToDecrypt()) : 0;

				var preApplicationResponse = await PortalHttpClientHelper
					.GetAsync<ApiResponse<PreApplicationApplicantApiResponse>>
					($"{ApiMethodName.QueueMatic.GetPreApplicationApplicant + preApplicantId}/{UserSession.BranchId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				var applicantInfo = await RestHttpClient.Create()
					.Get<GetApplicantInformationResult>(
						AppSettings.Qms.BaseApiUrl + QmsEndPoint.GetApplicantInformation + preApplicantId, QMSApiDefaultRequestHeaders);

				if (!await preApplicationResponse.Validate(out ResultModel preApplicationResult).ConfigureAwait(false))
					return Content(preApplicationResult.Message);

				viewModel.IsFromPreApplication = true;

				viewModel.EncryptedPreApplicationApplicantId = preApplicationResponse.Data.ApplicantId.ToEncrypt();
				viewModel.BranchApplicationCountryId = preApplicationResponse.Data.BranchApplicationCountryId;
				viewModel.EncryptedTokenId = applicantInfo.Data.GeneratedTokenId.ToEncrypt();
				viewModel.EncryptedWhiteListId = applicantInfo.Data.WhiteListId.ToEncrypt();

				if (applicantInfo?.Data != null)
				{
					viewModel.Name = applicantInfo.Data.Name;
					viewModel.Surname = applicantInfo.Data.Surname;
					viewModel.BirthDate = applicantInfo.Data.BirthDate;
					viewModel.GenderId = applicantInfo.Data.GenderId;
					viewModel.NationalityId = applicantInfo.Data.NationalityId;
					viewModel.PassportNumber = applicantInfo.Data.PassportNumber;
					viewModel.PassportExpireDate = applicantInfo.Data.PassportExpireDate;
				}
				else
				{
					viewModel.Name = preApplicationResponse.Data.Name;
					viewModel.Surname = preApplicationResponse.Data.Surname;
					viewModel.BirthDate = preApplicationResponse.Data.BirthDate;
					viewModel.GenderId = preApplicationResponse.Data.GenderId;
					viewModel.NationalityId = preApplicationResponse.Data.NationalityId;
					viewModel.PassportNumber = preApplicationResponse.Data.PassportNumber;
					viewModel.PassportExpireDate = preApplicationResponse.Data.PassportExpireDate;
				}
				viewModel.ApplicationTypeId = preApplicationResponse.Data.ApplicationTypeId;
				viewModel.ApplicantTypeId = applicationType;
				viewModel.VisaCategoryId = preApplicationResponse.Data.VisaCategoryId;
				viewModel.AdditionalServiceTypeId = preApplicationResponse.Data.AdditionalServiceTypeId;
				viewModel.CustomerId = preApplicationResponse.Data.CustomerId;
				viewModel.CountryId = preApplicationResponse.Data.CountryId;
				viewModel.AgencyId = preApplicationResponse.Data.AgencyId;
				viewModel.IsAgency = preApplicationResponse.Data.AgencyId.HasValue;
				viewModel.TitleId = preApplicationResponse.Data.GenderId;
				viewModel.Email = preApplicationResponse.Data.Email == "-" ? string.Empty : preApplicationResponse.Data.Email;
				viewModel.PhoneNumber1 = preApplicationResponse.Data.PhoneNumber == "0" ? string.Empty :
					preApplicationResponse.Data.PhoneNumber.StartsWith(preApplicationResponse.Data.CountryCallingCode) ? preApplicationResponse.Data.PhoneNumber.Substring(preApplicationResponse.Data.CountryCallingCode.Length) :
					  preApplicationResponse.Data.PhoneNumber;
				viewModel.VasTypeId = preApplicationResponse.Data.VasTypeId;
				viewModel.IsVipAppointment = preApplicationResponse.Data.VasTypeId == VasType.Vip.ToInt();

				if (preApplicationResponse.Data.VasTypeId == VasType.Vip.ToInt() && viewModel.ExtraFees.Any(q => q.ExtraFeeId == (int)ExtraFeeStaticType.Vip))
				{
					viewModel.ExtraFees.FirstOrDefault(q => q.ExtraFeeId == (int)ExtraFeeStaticType.Vip).IsChecked = true;
				}
			}

			viewModel.ApplicationCountCompleted = false;
			if (!string.IsNullOrEmpty(encryptedRelationalApplicationId) && apiResponsePreApplicationForm.Data.IsPrinterIntegrationActive)
			{
				var isMainApplication = await PortalHttpClientHelper
					.GetAsync<ApiResponse<ValidateApiResponse>>
					($"{ApiMethodName.Appointment.IsMainApplication + encryptedRelationalApplicationId.ToDecryptInt()}/{0}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (isMainApplication == null)
					return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

				viewModel.ApplicationCountCompleted = !isMainApplication.Data.Result;
			}

			int? relationalApplicationId = null;
			int? applicantTypeId = null;

			if (!string.IsNullOrEmpty(encryptedRelationalApplicationId))
				relationalApplicationId = encryptedRelationalApplicationId.ToDecryptInt();

			if (!string.IsNullOrEmpty(encryptedApplicantTypeId))
				applicantTypeId = encryptedApplicantTypeId.ToDecryptInt();

			if (apiResponsePreApplicationForm.Data.RelationalApplicationDetail != null)
			{
				if (relationalApplicationId == apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationId)
					viewModel.RelationalApplicationId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationId;

				if (applicantTypeId == apiResponsePreApplicationForm.Data.RelationalApplicationDetail.ApplicantTypeId)
				{
					viewModel.ApplicantTypeId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.ApplicantTypeId;
					viewModel.AgencyId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.AgencyId;
					viewModel.IsAgency = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.AgencyId.HasValue;
					viewModel.IsApplicantTypeSet = true;
				}

				if (apiResponsePreApplicationForm.Data.RelationalApplicationDetail.ApplicantTypeId == (int)ApplicantType.Family)
				{
					viewModel.Email = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.Email;
					viewModel.PhoneNumber1 = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.PhoneNumber1;
					viewModel.PhoneNumber2 = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.PhoneNumber2;
					viewModel.Address = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.Address;
					viewModel.ForeignCityId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.ForeignCityId;
					//viewModel.City = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.City;
					viewModel.PostalCode = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.PostalCode;
					viewModel.NameOfSecondContactPerson = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.NameOfSecondContactPerson;
					viewModel.AreaId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.AreaId;
					viewModel.AreaName = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.AreaName;
					viewModel.GovernorateId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.GovernorateId;
					viewModel.GovernorateName = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.GovernorateName;
					viewModel.IsCargoIntegrationSelected = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.IsCargoIntegrationSelected;
					viewModel.FamilyIraqSmsSelected = apiResponsePreApplicationForm.Data.BeforeRelationalApplicationDetail.FamilyIraqSmsSelected;
					viewModel.FamilyIraqOpsiyonelSmsSelected = apiResponsePreApplicationForm.Data.BeforeRelationalApplicationDetail.FamilyIraqOpsiyonelSmsSelected;
					viewModel.RelationalApplicationDetail = new AddUpdateApplicationViewModel.RelationalApplication
					{
						Email = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.Email,
						PhoneNumber1 = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.PhoneNumber1,
						PhoneNumber2 = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.PhoneNumber2,
						Address = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.Address,
						//City = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.City,
						ForeignCityId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.ForeignCityId,
						PostalCode = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.PostalCode,
						NameOfSecondContactPerson = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.NameOfSecondContactPerson,
						AreaId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.AreaId,
						AreaName = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.AreaName,
						GovernorateId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.GovernorateId,
						GovernorateName = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.GovernorateName,
						IsCargoIntegrationSelected = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.IsCargoIntegrationSelected,
						RelationalApplicationDocumentDetail = new AddUpdateApplicationViewModel.RelationalApplication.RelationalApplicationDocument
						{
							HasBankAccount = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.HasBankAccount,
							BankBalance = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.BankBalance,
							BankBalanceCurrencyId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.BankBalanceCurrencyId,
							HasDeed = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.HasDeed,
							VisaCategoryId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.VisaCategoryId,
							AdditionalServiceTypeId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.AdditionalServiceTypeId,
							EntryDate = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.EntryDate,
							ExitDate = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.ExitDate,
							Job = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.Job,
							OccupationId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.OccupationId,
							MonthlySalary = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.MonthlySalary,
							MonthlySalaryCurrencyId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.MonthlySalaryCurrencyId,
							CompanyName = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.CompanyName,
							TotalYearInCompany = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.TotalYearInCompany,
							ReimbursementSponsorDetail = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.ReimbursementSponsorDetail,
							HasPersonVisitedTurkeyBefore = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.HasPersonVisitedTurkeyBefore
						}
					};

					viewModel.VisaCategoryId = viewModel.RelationalApplicationDetail.RelationalApplicationDocumentDetail.VisaCategoryId;
					viewModel.EntryDate = viewModel.RelationalApplicationDetail.RelationalApplicationDocumentDetail.EntryDate;
					viewModel.ExitDate = viewModel.RelationalApplicationDetail.RelationalApplicationDocumentDetail.ExitDate;
				}

				if (apiResponsePreApplicationForm.Data.RelationalApplicationDetail.ApplicantTypeId == (int)ApplicantType.Group)
				{
					viewModel.RelationalApplicationDetail = new AddUpdateApplicationViewModel.RelationalApplication
					{
						RelationalApplicationDocumentDetail = new AddUpdateApplicationViewModel.RelationalApplication.RelationalApplicationDocument
						{
							VisaCategoryId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.VisaCategoryId,
							AdditionalServiceTypeId = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.AdditionalServiceTypeId,
							EntryDate = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.EntryDate,
							ExitDate = apiResponsePreApplicationForm.Data.RelationalApplicationDetail.RelationalApplicationDocumentDetail.ExitDate
						}
					};

					viewModel.VisaCategoryId = viewModel.RelationalApplicationDetail.RelationalApplicationDocumentDetail.VisaCategoryId;
					viewModel.EntryDate = viewModel.RelationalApplicationDetail.RelationalApplicationDocumentDetail.EntryDate;
					viewModel.ExitDate = viewModel.RelationalApplicationDetail.RelationalApplicationDocumentDetail.ExitDate;
				}
			}

            if (!viewModel.IsRelatedInsuranceForExemptActive)
                viewModel.ExtraFees = viewModel.ExtraFees.Where(s => s.CategoryTypeId != (int)ExtraFeeCategoryType.RelatedInsurance).ToList();

            if (MemoryCache.TryGetValue(CacheKeys.BranchCache, out object value))
			{
				var cacheItem = (BranchesApiResponse)value;
				var branch = cacheItem.Branches.FirstOrDefault(p => p.Id == UserSession.BranchId.Value);
				if (branch != null && branch.Country != null)
				{
					viewModel.CountryCallingCode = branch.Country.CallingCode;

					if (!string.IsNullOrEmpty(viewModel.PhoneNumber1) &&
						!string.IsNullOrEmpty(viewModel.CountryCallingCode) &&
						viewModel.PhoneNumber1.StartsWith(viewModel.CountryCallingCode))
					{
						viewModel.PhoneNumber1 = viewModel.PhoneNumber1.Substring(viewModel.CountryCallingCode.Length);
					}
					if (!string.IsNullOrEmpty(viewModel.PhoneNumber2) &&
						!string.IsNullOrEmpty(viewModel.CountryCallingCode) &&
						viewModel.PhoneNumber2.StartsWith(viewModel.CountryCallingCode))
					{
						viewModel.PhoneNumber2 = viewModel.PhoneNumber2.Substring(viewModel.CountryCallingCode.Length);
					}
				}
			}

			return View("AddUpdate", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> AddApplication(AddUpdateApplicationViewModel viewModel)
		{

			if (viewModel.AdditionalParam != null )
			{
				viewModel.ReleatedInsuranceApplicationDetail =
					JsonConvert.DeserializeObject<List<AddUpdateApplicationViewModel.ReleatedInsuranceApplication>>(
						viewModel.AdditionalParam);
			}

			var isApplicationDocumentIgnored = false;

			if (viewModel != null)
			{
				if (viewModel.ApplicationTypeId == (int)ApplicationType.NonApplicationPcr || viewModel.ApplicationTypeId == (int)ApplicationType.NonApplicationPrintOut || viewModel.ApplicationTypeId == (int)ApplicationType.NonApplicationPhotocopy || viewModel.ApplicationTypeId == (int)ApplicationType.NonApplicationPhotograph || viewModel.VisaCategoryId == 0)
				{
					isApplicationDocumentIgnored = true;

					ModelState.Remove(nameof(viewModel.VisaCategoryId));
					ModelState.Remove(nameof(viewModel.EntryDate));
					ModelState.Remove(nameof(viewModel.ExitDate));
				}
			}

			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var apiRequest = new AddUpdateApplicationApiRequest
			{
				BranchApplicationCountryId = viewModel.BranchApplicationCountryId,	
				ApplicantTypeId = viewModel.ApplicantTypeId,
				ApplicantCount = viewModel.ApplicantCount,
				ApplicationTypeId = viewModel.ApplicationTypeId,
				PassportNumber = viewModel.PassportNumber,
				PassportExpireDate = viewModel.PassportExpireDate,
				ApplicationPassportStatusId = viewModel.ApplicationPassportStatusId,
				AgencyId = viewModel.IsAgency ? viewModel.AgencyId : null,
				CustomerId = viewModel.CustomerId,
				Reason = viewModel.Reason,
				IsAllowDeniedPassport = viewModel.IsAllowDeniedPassport,
				TitleId = viewModel.TitleId,
				Name = viewModel.Name,
				Surname = viewModel.Surname,
				BirthDate = viewModel.BirthDate,
				GenderId = viewModel.GenderId,
				MaritalStatusId = viewModel.MaritalStatusId,
				NationalityId = viewModel.NationalityId,
				ResidenceNumber = viewModel.ResidenceNumber,
				MaidenName = viewModel.MaidenName,
				FatherName = viewModel.FatherName,
				MotherName = viewModel.MotherName,
				Email = viewModel.Email,
				PhoneNumber1 = !string.IsNullOrEmpty(viewModel.PhoneNumber1) ? $"{viewModel.CountryCallingCode}{viewModel.PhoneNumber1}" : "",
				PhoneNumber2 = !string.IsNullOrEmpty(viewModel.PhoneNumber2) ? $"{viewModel.CountryCallingCode}{viewModel.PhoneNumber2}" : "",
				Address = viewModel.Address,
				ForeignCityId = viewModel.ForeignCityId,
				PostalCode = viewModel.PostalCode,
				NameOfSecondContactPerson = viewModel.NameOfSecondContactPerson,
				AreaId = viewModel.AreaId,
				AreaName = viewModel.AreaName,
				GovernorateId = viewModel.GovernorateId,
				GovernorateName = viewModel.GovernorateName,
				IsCargoIntegrationSelected = viewModel.IsCargoIntegrationSelected,
				RelationalApplicationId = viewModel.RelationalApplicationId,
				RelationShipId = viewModel.RelationShipId.HasValue ? viewModel.RelationShipId : (int)ApplicantIndividualRelationship.HimselfHerself,
				Note = viewModel.Note,
				RequestedBy = UserSession.UserId,

				Document = new AddUpdateApplicationApiRequest.ApplicationDocument
				{
					TotalYearInCountry = viewModel.TotalYearInCountry,
					ReimbursementTypeId = viewModel.ReimbursementTypeId,
					ReimbursementSponsorDetail = viewModel.ReimbursementSponsorDetail,
					Job = viewModel.Job,
					OccupationId = viewModel.OccupationId,
					CompanyName = viewModel.CompanyName,
					TotalYearInCompany = viewModel.TotalYearInCompany,
					MonthlySalary = viewModel.MonthlySalary,
					MonthlySalaryCurrencyId = viewModel.MonthlySalaryCurrencyId,
					HasBankAccount = viewModel.HasBankAccount,
					BankBalance = viewModel.BankBalance,
					BankBalanceCurrencyId = viewModel.BankBalanceCurrencyId,
					HasDeed = viewModel.HasDeed,
					VisaCategoryId = viewModel.VisaCategoryId,
					AdditionalServiceTypeId = viewModel.AdditionalServiceTypeId,
					NumberOfEntryId = viewModel.NumberOfEntryId,
					HasEntryBan = viewModel.HasEntryBan,
					EntryDate = viewModel.EntryDate,
					ExitDate = viewModel.ExitDate,
					CityName = viewModel.CityName,
					AccomodationDetail = viewModel.AccomodationDetail,
					HasRelativeAbroad = viewModel.HasRelativeAbroad,
					RelativeLocation = viewModel.RelativeLocation,
					PersonTravelWith = viewModel.PersonTravelWith,
					PersonTravelWithHasVisa = viewModel.PersonTravelWithHasVisa,
					ProvidedWithHasRelatedInsurance = viewModel.ProvidedWithHasRelatedInsurance,
					ApplicationTogether = viewModel.ApplicationTogether,
					ApplicationTogetherFiftyYearCount = viewModel.ApplicationTogetherFiftyYearCount,
					ApplicationTogetherFifteenYearCount = viewModel.ApplicationTogetherFifteenYearCount,
					HasPersonVisitedTurkeyBefore = viewModel.HasPersonVisitedTurkeyBefore,
					VehicleTypeId = viewModel.VehicleTypeId,
					BrandModelId = viewModel.BrandModelId,
					PlateNo = viewModel.PlateNo,
					ModelYear = viewModel.ModelYear,
					ChassisNumber = viewModel.ChassisNumber,
					BrandText = viewModel.BrandText,
					ModelText = viewModel.ModelText,
					ResidenceApplicationToBeMade = viewModel.ResidenceApplicationToBeMade
				},

				VisaHistories = viewModel.VisaHistories.Where(p => p.CountryId != null && p.IsUsed != null)
									.Select(p => new AddUpdateApplicationApiRequest.ApplicationVisaHistory
									{
										CountryId = p.CountryId.Value,
										FromDate = p.FromDate,
										UntilDate = p.UntilDate,
										IsUsed = p.IsUsed,
										NumberOfEntryId = p.NumberOfEntryId,
										VisaIsUsedYear = p.VisaIsUsedYear,
										OldVisaDecisionId = p.OldVisaDecisionId
									}).ToList(),

				ExtraFees = viewModel.ExtraFees.Where(p => p.IsChecked).Select(p => new AddUpdateApplicationApiRequest.ApplicationExtraFee
				{
					ExtraFeeId = p.ExtraFeeId,
					Quantity = p.Quantity == 0 ? 1 : p.Quantity,
					CreatedBy = UserSession.UserId,
                    CreatedAt = DateTime.Now,
                    Cash = p.Cash,
                    Pos = p.Pos,
                    Online = p.Online
                }).ToList(),
				ReleatedInsuranceApplicationDetail = viewModel.ReleatedInsuranceApplicationDetail.Select(p =>
					new AddUpdateApplicationApiRequest.ReleatedInsuranceApplication
					{
						NationalityId = p.ReleatedInsuranceNationality.Id,
						Name = p.Name,
						PhoneNumber1 = p.PhoneNumber1,
						BirthDate = ((DateTime)p.BirthDate).AddHours(5).Date,
						Email = p.Email,
						Surname = p.Surname,
						PassportNumber = p.PassportNumber,
						CreatedBy = UserSession.UserId,
						CreatedAt = DateTime.Now
					}).ToList()
			};

			if (!string.IsNullOrEmpty(viewModel.EncryptedPreApplicationApplicantId))
			{
				apiRequest.PreApplicationApplicantId = viewModel.EncryptedPreApplicationApplicantId.ToDecryptInt();
				apiRequest.GeneratedTokenId = viewModel.EncryptedTokenId.ToDecryptInt();
				apiRequest.WhiteListId = viewModel.EncryptedWhiteListId.ToDecryptInt();
			}
			else if (!string.IsNullOrEmpty(viewModel.ConnectedEncryptedPreApplicationApplicantId))
			{
				apiRequest.PreApplicationApplicantId = viewModel.ConnectedEncryptedPreApplicationApplicantId.ToDecryptInt();
			}

			if (isApplicationDocumentIgnored)
			{
				apiRequest.Document = new AddUpdateApplicationApiRequest.ApplicationDocument
				{
					VisaCategoryId = (int)VisaCategoryTypeEnum.Unspecified,
					AdditionalServiceTypeId = viewModel.AdditionalServiceTypeId,
					CityName = viewModel.CityName,
					VehicleTypeId = viewModel.VehicleTypeId,
					BrandModelId = viewModel.BrandModelId,
					PlateNo = viewModel.PlateNo,
					ModelYear = viewModel.ModelYear,
					ChassisNumber = viewModel.ChassisNumber,
					BrandText = viewModel.BrandText,
					ModelText = viewModel.ModelText,
					EntryDate = new DateTime(9999, 1, 1),
					ExitDate = new DateTime(9999, 1, 1)
				};

				apiRequest.VisaHistories.Clear();
			}

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(apiRequest, ApiMethodName.Appointment.AddApplication, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			if (viewModel.IsPhotoBoothIntegrationActive && viewModel.ApplicationTypeId != (int)ApplicationType.NonApplicationPhotograph && viewModel.ExtraFees.Any(q => q.IsChecked && (q.ExtraFeeName.Contains("Fotoğraf") || (q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy")))))
			{
				var apiPhotoBoothRequest = new AddPhotoBoothApiRequest
				{
					BranchId = UserSession.BranchId.Value,
					ApplicationId = apiResponse.Data.Id,
					Name = viewModel.Name,
					Surname = viewModel.Surname,
					PassportNumber = viewModel.PassportNumber,
					Price = viewModel.ExtraFees.FirstOrDefault(q => (q.ExtraFeeName.Contains("Fotoğraf") || (q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy"))) && q.IsChecked).Price,
					CurrencyId = viewModel.ExtraFees.FirstOrDefault(q => (q.ExtraFeeName.Contains("Fotoğraf") || (q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy"))) && q.IsChecked).CurrencyId,
					Status = (int)PhotoBoothStatus.Ordered,
					RequestedBy = UserSession.UserId,
				};

				await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<AddApiResponse>>
					(apiPhotoBoothRequest, ApiMethodName.Appointment.AddPhotoBooth, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);
			}

			if (apiResponse.Data.Id > 0)
			{
				if (viewModel.ApplicantId > 0 || apiRequest.PreApplicationApplicantId.GetValueOrDefault() > 0)
				{
					var request = new ApplicationCompletedRequest
					{
						BranchId = UserSession.BranchId.GetValueOrDefault(),
						LineId = UserSession.QmsDropdownsModel.LineId.GetValueOrDefault(),
						RelationalApplicationId = viewModel.RelationalApplicationId,
						ApplicationId = apiResponse.Data.Id,
						BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
						ApplicantId = viewModel.ApplicantId == 0 ? apiRequest.PreApplicationApplicantId.GetValueOrDefault() : viewModel.ApplicantId,
						ApplicantTypeId = viewModel.ApplicantTypeId
					};

					var response = await RestHttpClient.Create().Post<ApplicationCompletedResult>(AppSettings.Qms.BaseApiUrl + QmsEndPoint.ApplicationCompleted, QMSApiDefaultRequestHeaders, request);

					if (response?.Status != QMS_API_RESPONSE_SUCCESS)
						return Json(new ResultModel { Message = response?.Message ?? $"{SiteResources.Application}{SiteResources.NotCompleted}", ResultType = ResultType.Warning });
				}

				return Json(new ResultModel { Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
			}

			else
				return Json(new ResultModel { Data = apiResponse.Data, Message = ResultMessage.InvalidOperation.ToDescription(), ResultType = ResultType.Danger });
		}

		[HttpPost]
		public async Task<IActionResult> ContactInformationGenerateVerificationCode([FromBody] ContactInformationGenerateVerificationCodeRequestViewModel model)
		{
            if (model == null || !ModelState.IsValid)
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

            var apiRequest = new ContactInformationGenerateVerificationCodeApiRequest()
			{
				ContactInformation = model.ContactInformation,
				Method = (ApiModel.Requests.Appointment.Application.VeriyfContactInformationMethod)model.Method,
				UserId = UserSession.UserId,
				PassportNumber = model.PassportNumber,
				LanguageId = LanguageId,
				BranchId = UserSession.BranchId.HasValue ? UserSession.BranchId.Value : 0
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<VeriyfContactInformationCreateCodeResponseDto>>
				(apiRequest, ApiMethodName.Appointment.ContactInformationGenerateVerificationCode, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			return Json(apiResponse);
		}

		[HttpPost]
		public async Task<IActionResult> ContactInformationVerifyVerificationCode([FromBody] ContactInformationVerifyVerificationCodeRequestViewModel model)
		{
			var apiRequest = new ContactInformationVerifyVerificationCodeApiRequest()
			{
				Code = model.Code,
				ExpirationTime = model.ExpirationTime,
				Hash = model.Hash,
				UserId = UserSession.UserId,
				LanguageId = base.LanguageId
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(apiRequest, ApiMethodName.Appointment.ContactInformationVerifyVerificationCode, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(result);

            return Json(apiResponse);

		}

		[HttpPost]
		public async Task<IActionResult> ContactInformationVerifyUser([FromBody] ContactInformationVerifyUserLoginViewModel model)
		{
			if (!model.Username.Contains("/"))
			{
				return Json(new ApiResponse { Message = SiteResources.UserNotFound.ToTitleCase(), IsSuccess = false });
			}
			var apiRequest = new ContactInformationVerifyUserApiRequest
			{
				CorporateId = model.Username.Split("/")[0],
				Email = model.Username.Split("/")[1],
				HashedPassword = model.Password.ToHash(),
				UserId = UserSession.UserId,
				PassportNumber = model.PassportNumber,
				BranchId = UserSession.BranchId.HasValue ? UserSession.BranchId.Value : 0,
				LanguageId = base.LanguageId
			};

			var headers = PortalGatewayApiDefaultRequestHeaders;
			if (headers.TryGetValue("corporateId", out string corporateId))
				headers["corporateId"] = model.Username.Split("/")[0];

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<UserLoginApiResponse>>
				(apiRequest, ApiMethodName.Appointment.ContactInformationVerifyUser, AppSettings.PortalGatewayApiUrl, headers)
				.ConfigureAwait(false);

			apiResponse.Data = null;
			return Json(apiResponse);
		}

		[HttpPost]
		public async Task<IActionResult> GetContactInformationVerificationHistory([DataSourceRequest] DataSourceRequest request, string passportNumber)
		{
			var headers = PortalGatewayApiDefaultRequestHeaders;

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<List<ContactInformationVerificationHistoryDto>>>
				(ApiMethodName.Appointment.GetContactInformationVerificationHistory + "?passportNumber=" + passportNumber, AppSettings.PortalGatewayApiUrl, headers)
				.ConfigureAwait(false);

			return Json(apiResponse);
		}

		#endregion

		#region Application/Update

		public async Task<IActionResult> Update(string encryptedApplicationId)
		{
			CountriesApiResponse nationalities = new CountriesApiResponse();
			if (MemoryCache.TryGetValue(CacheKeys.CountryCache, out object value2))
			{
				nationalities = (CountriesApiResponse)value2;
			}
			
			var nationalitiesValue =nationalities.Countries.Select(x => new AddUpdateApplicationViewModel.NationalityViewModel{ Id = x.Id, Name = x.Name.ToTitleCase() }).OrderBy(p=>p.Name);

			ViewData["defaultNationalities"] = nationalitiesValue.First();
			
			if (UserSession.BranchId == null)
			{
				return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
			}

			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationApiResponse>>
				(ApiMethodName.Appointment.GetApplication + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			if (UserSession.BranchId.HasValue && UserSession.BranchId.Value != apiResponse.Data.BranchId)
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			var apiRequest = new PreApplicationFormApiRequest
			{
				BranchApplicationCountryId = apiResponse.Data.BranchApplicationCountryId,
				ApplicationId = apiResponse.Data.Id,
			};

			var apiResponsePreApplicationForm = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PreApplicationFormApiResponse>>
				(apiRequest, ApiMethodName.Appointment.GetPreApplicationForm, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponsePreApplicationForm.Validate(out ResultModel resultPreApplicationForm).ConfigureAwait(false))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			if (!apiResponse.Data.IsApplicationUpdateAllowed)
			{
				TempData.Put("Notification", new ResultModel { Message = $"{SiteResources.Exception_ApplicationUpdateIsNotAllowedForThisApplicationStatus} ({SiteResources.ApplicationNumber}: {apiResponse.Data.Id.ToApplicationNumber()})", ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			var isShowAllowDeniedPassportControl = false;
			var IsApplicationUpdateStatusCheckRoleCheck = false;

			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object actions))
			{
				var roleActions = (RoleActionApiResponse)actions;

				isShowAllowDeniedPassportControl = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
					.Any(p => p.RoleActions.Any(q => q.Action.Method == "IsAllowedDeniedPassportNumber" && q.Action.IsActive));

				IsApplicationUpdateStatusCheckRoleCheck = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
					.Any(p => p.RoleActions.Any(q => q.Action.Method == "IsApplicationUpdateStatusCheckRoleCheck" && q.Action.IsActive)); // ilgili rol var ise kontrol etme
			}

            var viewModel = new AddUpdateApplicationViewModel
			{
				IsRelatedInsuranceForExemptActive = apiResponsePreApplicationForm.Data.IsRelatedInsuranceForExemptActive,
				IsContactInformationVerified = apiResponse.Data.IsContactInformationVerified != null ? (bool)apiResponse.Data.IsContactInformationVerified.Value : false,
				EncryptedApplicationId = apiResponse.Data.Id.ToEncrypt(),
				EncryptedPreApplicationApplicantId = apiResponse.Data.PreApplicationApplicantId?.ToEncrypt() ?? string.Empty,
				InformationNotes = apiResponsePreApplicationForm.Data.InformationNotes,
				BranchCountryId = apiResponsePreApplicationForm.Data.BranchCountryId,
				ShowAllowDeniedPassportControl = isShowAllowDeniedPassportControl,
				IsAllowDeniedPassport = apiResponse.Data.IsAllowDeniedPassport,
				IsPrinterIntegrationActive = apiResponsePreApplicationForm.Data.IsPrinterIntegrationActive,
				IsApplicationUpdateStatusCheckActive = IsApplicationUpdateStatusCheckRoleCheck ? false : apiResponsePreApplicationForm.Data.IsApplicationUpdateStatusCheckActive,
				IsPreApplicationConnectionActive = apiResponsePreApplicationForm.Data.IsPreApplicationConnectionActive,
				ApplicationTime = apiResponse.Data.ApplicationTime,
				Reason = apiResponse.Data.Reason,
				DisableContactInformationVerify = apiResponsePreApplicationForm.Data.DisableContactInformationVerify,
				ApplicationFormElements = apiResponsePreApplicationForm.Data.ApplicationFormElements.Select(p => new AddUpdateApplicationViewModel.ApplicationFormElement
				{
					FormElementId = p.FormElementId,
					IsRequired = p.IsRequired
				}).ToList(),
				BranchApplicationCountryId = apiResponse.Data.BranchApplicationCountryId,
				BranchName = apiResponse.Data.BranchName,
				CountryId = apiResponse.Data.CountryId,
				CountryName = apiResponse.Data.CountryName,
                ShowPaymentMethods = apiResponse.Data.ShowPaymentMethods,
                RelationalApplicationId = apiResponse.Data.RelationalApplicationId,
                IsMainApplicant = apiResponse.Data.IsMainApplicant,
                IsApplicantTypeSet = false,
                Note = apiResponse.Data.Note,
                IsSapApplicationOrderExists = apiResponse.Data.IsSapApplicationOrderExists,
                //Description
                ApplicantTypeId = apiResponse.Data.ApplicantTypeId,
				ApplicantCount = apiResponse.Data.ApplicantCount,
				ApplicationTypeId = apiResponse.Data.ApplicationTypeId,
				PassportNumber = apiResponse.Data.PassportNumber,
				PassportExpireDate = apiResponse.Data.PassportExpireDate,
				ApplicationPassportStatusId = apiResponse.Data.ApplicationPassportStatusId,
				RelationShipId = apiResponse.Data.RelationShipId,
				CustomerId = apiResponse.Data.CustomerId,
				AgencyId = apiResponse.Data.AgencyId,
				IsAgency = apiResponse.Data.AgencyId.HasValue,
				CargoProviderId = apiResponse.Data.CargoProviderId,
				IsCargoIntegrationActive = apiResponse.Data.IsCargoIntegrationActive,
				ShowCityDropdown = apiResponse.Data.ShowCityDropdown,
				//BasicInformation
				TitleId = apiResponse.Data.TitleId,
				Name = apiResponse.Data.Name,
				Surname = apiResponse.Data.Surname,
				BirthDate = apiResponse.Data.BirthDate,
				GenderId = apiResponse.Data.GenderId,
				MaritalStatusId = apiResponse.Data.MaritalStatusId,
				NationalityId = apiResponse.Data.NationalityId,
				ResidenceNumber = apiResponse.Data.ResidenceNumber,
				MaidenName = apiResponse.Data.MaidenName,
				FatherName = apiResponse.Data.FatherName,
				MotherName = apiResponse.Data.MotherName,
				Email = apiResponse.Data.Email,
				PhoneNumber1 = apiResponse.Data.PhoneNumber1,
				PhoneNumber2 = apiResponse.Data.PhoneNumber2,
				Address = apiResponse.Data.Address,
				ForeignCityId = apiResponse.Data.ForeignCityId,
				//City = apiResponse.Data.City,
				PostalCode = apiResponse.Data.PostalCode,
				NameOfSecondContactPerson = apiResponse.Data.NameOfSecondContactPerson,
				AreaId = apiResponse.Data.AreaId,
				AreaName = apiResponse.Data.AreaName,
				GovernorateId = apiResponse.Data.GovernorateId,
				GovernorateName = apiResponse.Data.GovernorateName,
				IsCargoIntegrationSelected = apiResponse.Data.IsCargoIntegrationSelected,
				//DocumentInformation
				TotalYearInCountry = apiResponse.Data.Document.TotalYearInCountry,
				ReimbursementTypeId = apiResponse.Data.Document.ReimbursementTypeId,
				ReimbursementSponsorDetail = apiResponse.Data.Document.ReimbursementSponsorDetail,
				Job = apiResponse.Data.Document.Job,
				OccupationId = apiResponse.Data.Document.OccupationId,
				CompanyName = apiResponse.Data.Document.CompanyName,
				TotalYearInCompany = apiResponse.Data.Document.TotalYearInCompany,
				MonthlySalary = apiResponse.Data.Document.MonthlySalary,
				MonthlySalaryCurrencyId = apiResponse.Data.Document.MonthlySalaryCurrencyId,
				HasBankAccount = apiResponse.Data.Document.HasBankAccount,
				BankBalance = apiResponse.Data.Document.BankBalance,
				BankBalanceCurrencyId = apiResponse.Data.Document.BankBalanceCurrencyId,
				HasDeed = apiResponse.Data.Document.HasDeed,
				VisaCategoryId = apiResponse.Data.Document.VisaCategoryId,
				AdditionalServiceTypeId = apiResponse.Data.Document.AdditionalServiceTypeId,
				NumberOfEntryId = apiResponse.Data.Document.NumberOfEntryId,
				HasEntryBan = apiResponse.Data.Document.HasEntryBan,
				EntryDate = apiResponse.Data.Document.EntryDate,
				ExitDate = apiResponse.Data.Document.ExitDate,
				CityName = apiResponse.Data.Document.CityName,
				AccomodationDetail = apiResponse.Data.Document.AccomodationDetail,
				HasRelativeAbroad = apiResponse.Data.Document.HasRelativeAbroad,
				RelativeLocation = apiResponse.Data.Document.RelativeLocation,
				PersonTravelWith = apiResponse.Data.Document.PersonTravelWith,
				PersonTravelWithHasVisa = apiResponse.Data.Document.PersonTravelWithHasVisa,
				ProvidedWithHasRelatedInsuranceId = apiResponse.Data.Document.ProvidedWithHasRelatedInsurance ? (int)YesNoQuestion.Yes : (int)YesNoQuestion.No,
				ShowReleatedInvidual = apiResponsePreApplicationForm.Data.IsRelatedInsuranceForExemptActive,
				ApplicationTogetherId = apiResponse.Data.Document.ApplicationTogether ? (int)YesNoQuestion.Yes : (int)YesNoQuestion.No,
				ApplicationTogetherFiftyYearCount = apiResponse.Data.Document.ApplicationTogetherFiftyYearCount,
				ApplicationTogetherFifteenYearCount = apiResponse.Data.Document.ApplicationTogetherFifteenYearCount,
				HasPersonVisitedTurkeyBefore = apiResponse.Data.Document.HasPersonVisitedTurkeyBefore,
				VehicleTypeId = apiResponse.Data.Document.VehicleTypeId,
				PlateNo = apiResponse.Data.Document.PlateNo,
				ModelYear = apiResponse.Data.Document.ModelYear,
				BrandModelId = apiResponse.Data.Document.BrandModelId,
				ChassisNumber = apiResponse.Data.Document.ChassisNumber,
				BrandText = apiResponse.Data.Document.BrandText,
				ModelText = apiResponse.Data.Document.ModelText,
				ResidenceApplication = apiResponse.Data.Document.ResidenceApplication,
				FamilyIraqSmsSelected = apiResponsePreApplicationForm.Data.BeforeRelationalApplicationDetail.FamilyIraqSmsSelected,
				FamilyIraqOpsiyonelSmsSelected = apiResponsePreApplicationForm.Data.BeforeRelationalApplicationDetail.FamilyIraqOpsiyonelSmsSelected,
				IsPhotoBoothIntegrationActive = apiResponsePreApplicationForm.Data.IsPhotoBoothIntegrationActive,
				VisaHistories = apiResponse.Data.VisaHistories.Select(p => new AddUpdateApplicationViewModel.VisaHistory
				{
					ApplicationVisaHistoryId = p.ApplicationVisaHistoryId,
					CountryId = p.CountryId,
					FromDate = p.FromDate,
					UntilDate = p.UntilDate,
					IsUsed = p.IsUsed,
					NumberOfEntryId = p.NumberOfEntryId,
					VisaIsUsedYear = p.VisaIsUsedYear,
					OldVisaDecisionId = p.OldVisaDecisionId
				}).ToList(),
				ExtraFees = apiResponsePreApplicationForm.Data.ExtraFees.Select(p => new AddUpdateApplicationViewModel.ExtraFee
				{
					ExtraFeeId = p.ExtraFeeId,
					TypeId = p.TypeId,
					MinimumItem = p.MinimumItem,
					CategoryTypeId = p.CategoryTypeId,
					ExtraFeeName = p.ExtraFeeName,
					IsChecked = p.IsAutoChecked,
					IsPreChecked = p.IsAutoChecked,
					IsPreQuantity = p.Quantity,
                    IsForRejectedApplications = p.IsForRejectedApplications,
                    RejectionFeePolicyType = CheckRejectionFeePolicyTypeByPeriod(p.PolicyPeriod),
                    Price = p.Price,
					CurrencyId = p.CurrencyId,
					AgeRange = p.AgeRange,
					Quantity = p.Quantity,
                    IsMainFee = p.IsMainFee,
                    IsSubFee = p.IsSubFee,
                    MainFeeId = p.MainFeeId,
                    RelatedMainFeeId = p.RelatedMainFeeId,
                }).ToList()
			};

			if (MemoryCache.TryGetValue(CacheKeys.BranchCache, out object value))
			{
				var cacheItem = (BranchesApiResponse)value;
				var branch = cacheItem.Branches.FirstOrDefault(p => p.Id == UserSession.BranchId.Value);
				if (branch != null && branch.Country != null)
				{
					viewModel.CountryCallingCode = branch.Country.CallingCode;

					if (!string.IsNullOrEmpty(viewModel.PhoneNumber1) &&
						!string.IsNullOrEmpty(viewModel.CountryCallingCode) &&
						viewModel.PhoneNumber1.StartsWith(viewModel.CountryCallingCode))
					{
						viewModel.PhoneNumber1 = viewModel.PhoneNumber1.Substring(viewModel.CountryCallingCode.Length);
					}
					if (!string.IsNullOrEmpty(viewModel.PhoneNumber2) &&
						!string.IsNullOrEmpty(viewModel.CountryCallingCode) &&
						viewModel.PhoneNumber2.StartsWith(viewModel.CountryCallingCode))
					{
						viewModel.PhoneNumber2 = viewModel.PhoneNumber2.Substring(viewModel.CountryCallingCode.Length);
					}
				}
			}

			for (int i = 0; i < viewModel.ExtraFees.Count; i++)
			{
				var viewModelExtraFee = viewModel.ExtraFees[i];
				var applicationExtraFee = apiResponse.Data.ExtraFees.FirstOrDefault(p => p.ExtraFeeId == viewModelExtraFee.ExtraFeeId);

				if (applicationExtraFee != null)
				{
					viewModelExtraFee.ApplicationExtraFeeId = applicationExtraFee.ApplicationExtraFeeId;
					viewModelExtraFee.IsChecked = true;
					viewModelExtraFee.Quantity = applicationExtraFee.Quantity;
					viewModelExtraFee.IsPreChecked = true;
					viewModelExtraFee.IsPreQuantity = applicationExtraFee.Quantity;
                    viewModelExtraFee.IsForRejectedApplications = applicationExtraFee.IsForRejectedApplications;
                    viewModelExtraFee.RejectionFeePolicyType = CheckRejectionFeePolicyTypeByPeriod(applicationExtraFee.PolicyPeriod);
                    viewModelExtraFee.Cash = applicationExtraFee.Cash;
                    viewModelExtraFee.Pos = applicationExtraFee.Pos;
                    viewModelExtraFee.Online = applicationExtraFee.Online;
                }
				else
				{
					viewModelExtraFee.IsChecked = false;
					viewModelExtraFee.IsPreChecked = false;
				}
			}

			viewModel.ApplicationCountCompleted = false;

            if (!viewModel.IsRelatedInsuranceForExemptActive && !viewModel.ExtraFees.Any(a => a.CategoryTypeId == (int)ExtraFeeCategoryType.RelatedInsurance && a.IsChecked))
                viewModel.ExtraFees = viewModel.ExtraFees.Where(s => s.CategoryTypeId != (int)ExtraFeeCategoryType.RelatedInsurance).ToList();

            if (apiResponse.Data.RelationalApplicationId != null && (apiResponse.Data.ApplicantTypeId == (int)ApplicantType.Family || apiResponse.Data.ApplicantTypeId == (int)ApplicantType.Group) && apiResponsePreApplicationForm.Data.IsPrinterIntegrationActive)
			{
				var isMainApplication = await PortalHttpClientHelper
					.GetAsync<ApiResponse<ValidateApiResponse>>
					($"{ApiMethodName.Appointment.IsMainApplication + apiResponse.Data.RelationalApplicationId}/{apiResponse.Data.Id}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (isMainApplication == null)
					return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

				viewModel.ApplicationCountCompleted = !isMainApplication.Data.Result;
			}

			return View("AddUpdate", viewModel);
		}
		[HttpPut]
		public async Task<IActionResult> UpdateApplication(AddUpdateApplicationViewModel viewModel)
		{
			if (viewModel.AdditionalParam != null )
			{
				viewModel.ReleatedInsuranceApplicationDetail =
					JsonConvert.DeserializeObject<List<AddUpdateApplicationViewModel.ReleatedInsuranceApplication>>(
						viewModel.AdditionalParam);
			}
			
			var isApplicationDocumentIgnored = false;

			if (viewModel != null)
			{
				if (viewModel.ApplicationTypeId == (int)ApplicationType.NonApplicationPcr || viewModel.ApplicationTypeId == (int)ApplicationType.NonApplicationPrintOut || viewModel.ApplicationTypeId == (int)ApplicationType.NonApplicationPhotocopy || viewModel.ApplicationTypeId == (int)ApplicationType.NonApplicationPhotograph || viewModel.VisaCategoryId == 0)
				{
					isApplicationDocumentIgnored = true;

					ModelState.Remove(nameof(viewModel.VisaCategoryId));
					ModelState.Remove(nameof(viewModel.EntryDate));
					ModelState.Remove(nameof(viewModel.ExitDate));
				}
			}

			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			if (string.IsNullOrEmpty(viewModel.EncryptedApplicationId))
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			int id = viewModel.EncryptedApplicationId.ToDecryptInt();

			var apiRequest = new AddUpdateApplicationApiRequest
			{
				Id = id,
				BranchApplicationCountryId = viewModel.BranchApplicationCountryId,
				ApplicantTypeId = viewModel.ApplicantTypeId,
				ApplicantCount = viewModel.ApplicantCount,
				ApplicationTypeId = viewModel.ApplicationTypeId,
				PassportNumber = viewModel.PassportNumber,
				PassportExpireDate = viewModel.PassportExpireDate,
				ApplicationPassportStatusId = viewModel.ApplicationPassportStatusId,
				AgencyId = viewModel.IsAgency ? viewModel.AgencyId : null,
				CustomerId = viewModel.CustomerId,
				Reason = viewModel.Reason,

				TitleId = viewModel.TitleId,
				Name = viewModel.Name,
				Surname = viewModel.Surname,
				BirthDate = viewModel.BirthDate,
				GenderId = viewModel.GenderId,
				MaritalStatusId = viewModel.MaritalStatusId,
				NationalityId = viewModel.NationalityId,
				ResidenceNumber = viewModel.ResidenceNumber,
				MaidenName = viewModel.MaidenName,
				FatherName = viewModel.FatherName,
				MotherName = viewModel.MotherName,
				Email = viewModel.Email,
				PhoneNumber1 = !string.IsNullOrEmpty(viewModel.PhoneNumber1) ? $"{viewModel.CountryCallingCode}{viewModel.PhoneNumber1}" : "",
				PhoneNumber2 = !string.IsNullOrEmpty(viewModel.PhoneNumber2) ? $"{viewModel.CountryCallingCode}{viewModel.PhoneNumber2}" : "",
				Address = viewModel.Address,
				//City = viewModel.City,
				ForeignCityId = viewModel.ForeignCityId,
				PostalCode = viewModel.PostalCode,
				NameOfSecondContactPerson = viewModel.NameOfSecondContactPerson,
				AreaId = viewModel.AreaId,
				AreaName = viewModel.AreaName,
				GovernorateId = viewModel.GovernorateId,
				GovernorateName = viewModel.GovernorateName,
				IsCargoIntegrationSelected = viewModel.IsCargoIntegrationSelected,
				RelationalApplicationId = viewModel.RelationalApplicationId,
				RelationShipId = viewModel.RelationShipId.HasValue ? viewModel.RelationShipId : (int)ApplicantIndividualRelationship.HimselfHerself,
				Note = viewModel.Note,
				RequestedBy = UserSession.UserId,

				Document = new AddUpdateApplicationApiRequest.ApplicationDocument
				{
					TotalYearInCountry = viewModel.TotalYearInCountry,
					ReimbursementTypeId = viewModel.ReimbursementTypeId,
					ReimbursementSponsorDetail = viewModel.ReimbursementSponsorDetail,
					Job = viewModel.Job,
					OccupationId = viewModel.OccupationId,
					CompanyName = viewModel.CompanyName,
					TotalYearInCompany = viewModel.TotalYearInCompany,
					MonthlySalary = viewModel.MonthlySalary,
					MonthlySalaryCurrencyId = viewModel.MonthlySalaryCurrencyId,
					HasBankAccount = viewModel.HasBankAccount,
					BankBalance = viewModel.BankBalance,
					BankBalanceCurrencyId = viewModel.BankBalanceCurrencyId,
					HasDeed = viewModel.HasDeed,
					VisaCategoryId = viewModel.VisaCategoryId,
					AdditionalServiceTypeId = viewModel.AdditionalServiceTypeId,
					NumberOfEntryId = viewModel.NumberOfEntryId,
					HasEntryBan = viewModel.HasEntryBan,
					EntryDate = viewModel.EntryDate,
					ExitDate = viewModel.ExitDate,
					CityName = viewModel.CityName,
					AccomodationDetail = viewModel.AccomodationDetail,
					HasRelativeAbroad = viewModel.HasRelativeAbroad,
					RelativeLocation = viewModel.RelativeLocation,
					PersonTravelWith = viewModel.PersonTravelWith,
					PersonTravelWithHasVisa = viewModel.PersonTravelWithHasVisa,
					ProvidedWithHasRelatedInsurance = viewModel.ProvidedWithHasRelatedInsurance,
					ApplicationTogether = viewModel.ApplicationTogether,
					ApplicationTogetherFiftyYearCount = viewModel.ApplicationTogetherFiftyYearCount,
					ApplicationTogetherFifteenYearCount = viewModel.ApplicationTogetherFifteenYearCount,
					HasPersonVisitedTurkeyBefore = viewModel.HasPersonVisitedTurkeyBefore,
					VehicleTypeId = viewModel.VehicleTypeId,
					PlateNo = viewModel.PlateNo,
					ModelYear = viewModel.ModelYear,
					BrandModelId = viewModel.BrandModelId,
					ChassisNumber = viewModel.ChassisNumber,
					BrandText = viewModel.BrandText,
					ModelText = viewModel.ModelText,
					ResidenceApplicationToBeMade = viewModel.ResidenceApplicationToBeMade
				},

				VisaHistories = viewModel.VisaHistories.Where(p => p.CountryId != null && p.IsUsed != null)
									.Select(p => new AddUpdateApplicationApiRequest.ApplicationVisaHistory
									{
										CountryId = p.CountryId.Value,
										FromDate = p.FromDate,
										UntilDate = p.UntilDate,
										IsUsed = p.IsUsed,
										NumberOfEntryId = p.NumberOfEntryId,
										VisaIsUsedYear = p.VisaIsUsedYear,
										ApplicationVisaHistoryId = p.ApplicationVisaHistoryId,
										OldVisaDecisionId = p.OldVisaDecisionId
									}).ToList(),

				ExtraFees = viewModel.ExtraFees.Where(p => p.IsChecked).Select(p => new AddUpdateApplicationApiRequest.ApplicationExtraFee
				{
					ExtraFeeId = p.ExtraFeeId,
                    Quantity = p.Quantity == 0 ? 1 : p.Quantity,
                    Price = p.Price,
					CurrencyId = p.CurrencyId,
                    Cash = p.Cash,
                    Pos = p.Pos,
                    Online = p.Online
                }).ToList(),
				ReleatedInsuranceApplicationDetail = viewModel.ReleatedInsuranceApplicationDetail.Select(p =>
					new AddUpdateApplicationApiRequest.ReleatedInsuranceApplication
					{
                        Id = p.Id,
						NationalityId = p.ReleatedInsuranceNationality.Id,
						Name = p.Name,
						PhoneNumber1 = p.PhoneNumber1,
						BirthDate = ((DateTime)p.BirthDate).AddHours(5).Date,
						Email = p.Email,
						Surname = p.Surname,
						PassportNumber = p.PassportNumber,
						CreatedBy = UserSession.UserId,
						CreatedAt = DateTime.Now
					}).ToList()
			};

			if (!string.IsNullOrEmpty(viewModel.EncryptedPreApplicationApplicantId))
			{
				apiRequest.PreApplicationApplicantId = viewModel.EncryptedPreApplicationApplicantId.ToDecryptInt();
			}

			if (isApplicationDocumentIgnored)
			{
				apiRequest.Document = new AddUpdateApplicationApiRequest.ApplicationDocument
				{
					VisaCategoryId = (int)VisaCategoryTypeEnum.Unspecified,
					EntryDate = new DateTime(9999, 1, 1),
					ExitDate = new DateTime(9999, 1, 1),
					AdditionalServiceTypeId = apiRequest.Document.AdditionalServiceTypeId,
					CityName = apiRequest.Document.CityName,
					VehicleTypeId = apiRequest.Document.VehicleTypeId,
					PlateNo = apiRequest.Document.PlateNo,
					ModelYear = apiRequest.Document.ModelYear,
					BrandModelId = apiRequest.Document.BrandModelId,
					ChassisNumber = apiRequest.Document.ChassisNumber,
					BrandText = apiRequest.Document.BrandText,
					ModelText = apiRequest.Document.ModelText,
				};

				apiRequest.VisaHistories.Clear();
			}

			var apiResponse = await PortalHttpClientHelper
				.PutAsJsonAsync<ApiResponse<UpdateApplicationApiResponse>>
				(apiRequest, ApiMethodName.Appointment.UpdateApplication, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			if (apiResponse.Data.WhiteListUpdatedProperties != null && apiResponse.Data.WhiteListUpdatedProperties.Any())
			{
				var apiWhiteListRequest = new UpdateWhiteListPropertyRequest()
				{
					Properties = apiResponse.Data.WhiteListUpdatedProperties.Select(s => new UpdateWhiteListPropertyRequest.UpdatePropertyDto
					{
						PropertyName = s.Key,
						PropertyValue = s.Value.ToString()
					}).ToList()
				};

				_ = await RestHttpClient.Create().Put<UpdateWhiteListPropertyResult>(AppSettings.Qms.BaseApiUrl + QmsManagementEndPoint.UpdateWhiteListProperty.Replace("{whiteListId}", apiResponse.Data.WhiteListId.ToString()), QMSApiDefaultRequestHeaders, apiWhiteListRequest);
			}

			var sendToSapOrderStatus = await PortalHttpClientHelper
			.GetAsync<ApiResponse<GetSendToSapOrderGroupIdResponse>>
			(ApiMethodName.Appointment.GetSendToSapOrder + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
			.ConfigureAwait(false);

			if (!sendToSapOrderStatus.Data.sapGroupId.Equals(""))
			{
				/* SAP Exchange*/
				if (viewModel.ExtraFees.Any(p => p.IsChecked && !p.IsPreChecked) && viewModel.ExtraFees.Any(p => !p.IsChecked && p.IsPreChecked && p.ExtraFeeId != 1))
				{
					bool orderPhotoExtraFeeCheck = viewModel.ExtraFees.Where(p => p.IsChecked && !p.IsPreChecked)
										.Any(p => p.ExtraFeeName.Contains("Fotoğraf")) || viewModel.ExtraFees.Where(p => p.IsChecked && !p.IsPreChecked)
										.Any(p => (p.ExtraFeeName.Contains("Photo") && !p.ExtraFeeName.Contains("Photocopy")));
					bool refundPhotoExtraFeeCheck = viewModel.ExtraFees.Where(p => !p.IsChecked && p.IsPreChecked)
											.Any(p => p.ExtraFeeName.Contains("Fotoğraf")) || viewModel.ExtraFees.Where(p => !p.IsChecked && p.IsPreChecked)
											.Any(p => (p.ExtraFeeName.Contains("Photo") && !p.ExtraFeeName.Contains("Photocopy")));
					if (orderPhotoExtraFeeCheck && refundPhotoExtraFeeCheck)
					{

						var apiResponseUpdatePhotoBoothApplicationDelete = await PortalHttpClientHelper
						   .GetAsync<ApiResponse<UpdateApiResponse>>
						   ($"{ApiMethodName.Appointment.UpdatePhotoBoothApplicationDelete + id}/{false}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
						   .ConfigureAwait(false);

						if (viewModel.IsPhotoBoothIntegrationActive && viewModel.ApplicationTypeId != (int)ApplicationType.NonApplicationPhotograph && viewModel.ExtraFees.Any(q => q.IsChecked && (q.ExtraFeeName.Contains("Fotoğraf") || q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy"))))
						{
							var apiPhotoBoothRequest = new AddPhotoBoothApiRequest
							{
								BranchId = UserSession.BranchId.Value,
								ApplicationId = id,
								Name = viewModel.Name,
								Surname = viewModel.Surname,
								PassportNumber = viewModel.PassportNumber,
								Price = viewModel.ExtraFees.FirstOrDefault(q => (q.ExtraFeeName.Contains("Fotoğraf") || q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy")) && q.IsChecked).Price,
								CurrencyId = viewModel.ExtraFees.FirstOrDefault(q => (q.ExtraFeeName.Contains("Fotoğraf") || q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy")) && q.IsChecked).CurrencyId,
								Status = (int)PhotoBoothStatus.Expired,
								RequestedBy = UserSession.UserId,
							};

							var apiPhotoBoothResponse = await PortalHttpClientHelper
								.PostAsJsonAsync<ApiResponse<AddApiResponse>>
								(apiPhotoBoothRequest, ApiMethodName.Appointment.AddPhotoBooth, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
								.ConfigureAwait(false);
						}
					}

					var sapApiRequest = new ExchangeOrderApiRequest()
					{
						AppointmentId = viewModel.RelationalApplicationId != null ? (int)viewModel.RelationalApplicationId : id,
						RequestAppointmentId = id,
						CancelOrderReasonId = 1, // TODO
						ExtraFeeIds = viewModel.ExtraFees.Where(p => p.IsChecked).Select(p => p.ExtraFeeId).ToList(),
						OrderExtraFeeIds = viewModel.ExtraFees.Where(p => p.IsChecked && !p.IsPreChecked)
											.Select(p => p.ExtraFeeId).ToList(),
						RefundExtraFeeIds = viewModel.ExtraFees.Where(p => !p.IsChecked && p.IsPreChecked)
											.Select(p => p.ExtraFeeId).ToList()
					};

					var sapExchangeApiResponse = await PortalHttpClientHelper
						.PostAsJsonAsync<ApiResponse<AddApiResponse>>
						(sapApiRequest, ApiMethodName.Accounting.ExchangeOrder, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
						.ConfigureAwait(false);
				}

				/* SAP Create */
				else if (viewModel.ExtraFees.Any(p => p.IsChecked && !p.IsPreChecked) || viewModel.ExtraFees.Any(p => !p.IsChecked && p.IsPreChecked))
				{
					var apiRequestCancelOrder = new CancelOrderApiRequest
					{
						AppointmentId = id,
						CancelOrderReasonId = 11,
						ExtraFeeIds = viewModel.ExtraFees.Where(p => !p.IsChecked && p.IsPreChecked && p.ExtraFeeId != 1 || (!p.IsChecked && p.IsPreChecked && p.Quantity > 1)).Select(p => p.ExtraFeeId).ToList()
					};

					if (apiRequestCancelOrder.ExtraFeeIds.Count > 0)
					{
						var sapApiRequest = new ExchangeOrderApiRequest()
						{
							AppointmentId = viewModel.RelationalApplicationId != null ? (int)viewModel.RelationalApplicationId : id,
							RequestAppointmentId = id,
							CancelOrderReasonId = 1, // TODO
							ExtraFeeIds = viewModel.ExtraFees.Where(p => p.IsChecked).Select(p => p.ExtraFeeId).ToList(),
							OrderExtraFeeIds = viewModel.ExtraFees.Where(p => p.IsChecked)
								.Select(p => p.ExtraFeeId).ToList(),
							RefundExtraFeeIds = viewModel.ExtraFees.Where(p => !p.IsChecked && p.IsPreChecked)
								.Select(p => p.ExtraFeeId).ToList()
						};

						var sapExchangeApiResponse = await PortalHttpClientHelper
							.PostAsJsonAsync<ApiResponse<AddApiResponse>>
								(sapApiRequest, ApiMethodName.Accounting.ExchangeOrder, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
							.ConfigureAwait(false);

					}
					else
					{
						var sapCreateApiResponse = await PortalHttpClientHelper
						 .GetAsync<ApiResponse<AddApiResponse>>
						 (ApiMethodName.Accounting.CreateOrder + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
						 .ConfigureAwait(false);
					}



					if (viewModel.IsPhotoBoothIntegrationActive && viewModel.ApplicationTypeId != (int)ApplicationType.NonApplicationPhotograph && viewModel.ExtraFees.Any(q => q.IsChecked && !q.IsPreChecked && (q.ExtraFeeName.Contains("Fotoğraf") || q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy"))))
					{
						var apiPhotoBoothRequest = new AddPhotoBoothApiRequest
						{
							BranchId = UserSession.BranchId.Value,
							ApplicationId = id,
							Name = viewModel.Name,
							Surname = viewModel.Surname,
							PassportNumber = viewModel.PassportNumber,
							Price = viewModel.ExtraFees.FirstOrDefault(q => (q.ExtraFeeName.Contains("Fotoğraf") || q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy")) && q.IsChecked).Price,
							CurrencyId = viewModel.ExtraFees.FirstOrDefault(q => (q.ExtraFeeName.Contains("Fotoğraf") || q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy")) && q.IsChecked).CurrencyId,
							Status = (int)PhotoBoothStatus.Expired,
							RequestedBy = UserSession.UserId,
						};

						var apiPhotoBoothResponse = await PortalHttpClientHelper
							.PostAsJsonAsync<ApiResponse<AddApiResponse>>
								(apiPhotoBoothRequest, ApiMethodName.Appointment.AddPhotoBooth, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
							.ConfigureAwait(false);
					}
				}
				else if (viewModel.ExtraFees.Any(p => p.IsChecked && p.IsPreChecked && p.Quantity > 1))
				{
					var sapApiRequest = new ExchangeOrderApiRequest()
					{
						AppointmentId = viewModel.RelationalApplicationId != null ? (int)viewModel.RelationalApplicationId : id,
						RequestAppointmentId = id,
						CancelOrderReasonId = 11, // TODO
						ExtraFeeIds = viewModel.ExtraFees.Where(p => p.IsChecked).Select(p => p.ExtraFeeId).ToList(),
						OrderExtraFeeIds = viewModel.ExtraFees.Where(p => p.IsChecked && p.IsPreChecked)
							.Select(p => p.ExtraFeeId).ToList(),
						RefundExtraFeeIds = viewModel.ExtraFees.Where(p => p.IsChecked && p.IsPreChecked)
							.Select(p => p.ExtraFeeId).ToList()
					};

					var sapExchangeApiResponse = await PortalHttpClientHelper
						.PostAsJsonAsync<ApiResponse<AddApiResponse>>
							(sapApiRequest, ApiMethodName.Accounting.ExchangeOrder, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
						.ConfigureAwait(false);
				}
				/* EOF SAP */
			}
			else
			{
				if (viewModel.ExtraFees.Any(p => p.IsChecked && !p.IsPreChecked) || viewModel.ExtraFees.Any(p => !p.IsChecked && p.IsPreChecked && p.ExtraFeeId != 1))
				{
					bool orderPhotoExtraFeeCheck = viewModel.ExtraFees.Where(p => p.IsChecked && !p.IsPreChecked)
											.Any(p => p.ExtraFeeName.Contains("Fotoğraf")) || viewModel.ExtraFees.Where(p => p.IsChecked && !p.IsPreChecked)
											.Any(p => (p.ExtraFeeName.Contains("Photo") && !p.ExtraFeeName.Contains("Photocopy")));
					bool refundPhotoExtraFeeCheck = viewModel.ExtraFees.Where(p => !p.IsChecked && p.IsPreChecked)
											.Any(p => p.ExtraFeeName.Contains("Fotoğraf")) || viewModel.ExtraFees.Where(p => !p.IsChecked && p.IsPreChecked)
											.Any(p => (p.ExtraFeeName.Contains("Photo") && !p.ExtraFeeName.Contains("Photocopy")));
					if (orderPhotoExtraFeeCheck && refundPhotoExtraFeeCheck)
					{

						var apiResponseUpdatePhotoBoothApplicationDelete = await PortalHttpClientHelper
						   .GetAsync<ApiResponse<UpdateApiResponse>>
						   ($"{ApiMethodName.Appointment.UpdatePhotoBoothApplicationDelete + id}/{false}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
						   .ConfigureAwait(false);

						if (viewModel.IsPhotoBoothIntegrationActive && viewModel.ApplicationTypeId != (int)ApplicationType.NonApplicationPhotograph && viewModel.ExtraFees.Any(q => q.IsChecked && (q.ExtraFeeName.Contains("Fotoğraf") || q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy"))))
						{
							var apiPhotoBoothRequest = new AddPhotoBoothApiRequest
							{
								BranchId = UserSession.BranchId.Value,
								ApplicationId = id,
								Name = viewModel.Name,
								Surname = viewModel.Surname,
								PassportNumber = viewModel.PassportNumber,
								Price = viewModel.ExtraFees.FirstOrDefault(q => (q.ExtraFeeName.Contains("Fotoğraf") || q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy")) && q.IsChecked).Price,
								CurrencyId = viewModel.ExtraFees.FirstOrDefault(q => (q.ExtraFeeName.Contains("Fotoğraf") || q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy")) && q.IsChecked).CurrencyId,
								Status = (int)PhotoBoothStatus.Expired,
								RequestedBy = UserSession.UserId,
							};

							var apiPhotoBoothResponse = await PortalHttpClientHelper
								.PostAsJsonAsync<ApiResponse<AddApiResponse>>
								(apiPhotoBoothRequest, ApiMethodName.Appointment.AddPhotoBooth, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
								.ConfigureAwait(false);
						}

						if (viewModel.IsPhotoBoothIntegrationActive && viewModel.ApplicationTypeId == (int)ApplicationType.NonApplicationPhotograph)
						{

							int applicationId = Int32.Parse(viewModel.Note != "" ? viewModel.Note : "1");


							var apiRequestCancelOrder = new CancelOrderApiRequest
							{
								AppointmentId = applicationId,
								CancelOrderReasonId = 111,
								ExtraFeeIds = viewModel.ExtraFees
									.Where(p => !p.IsChecked && p.IsPreChecked && p.ExtraFeeId != 1 ||
												(!p.IsChecked && p.IsPreChecked && p.Quantity > 1))
									.Select(p => p.ExtraFeeId).ToList()
							};

							if (apiRequestCancelOrder.ExtraFeeIds.Count > 0)
							{
								var apiResponseCancelOrder = await PortalHttpClientHelper
									.PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
									(apiRequestCancelOrder, ApiMethodName.Accounting.CancelOrder,
										AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
									.ConfigureAwait(false);

								if (!await apiResponseCancelOrder.Validate(out ResultModel resultCancelOrder)
										.ConfigureAwait(false))
									return Json(resultCancelOrder);
							}
						}
					}
					else
					{
						if (viewModel.IsPhotoBoothIntegrationActive && orderPhotoExtraFeeCheck && viewModel.ApplicationTypeId != (int)ApplicationType.NonApplicationPhotograph && viewModel.ExtraFees.Any(q => q.IsChecked && (q.ExtraFeeName.Contains("Fotoğraf") || q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy"))))
						{
							var apiPhotoBoothRequest = new AddPhotoBoothApiRequest
							{
								BranchId = UserSession.BranchId.Value,
								ApplicationId = id,
								Name = viewModel.Name,
								Surname = viewModel.Surname,
								PassportNumber = viewModel.PassportNumber,
								Price = viewModel.ExtraFees.FirstOrDefault(q => (q.ExtraFeeName.Contains("Fotoğraf") || q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy")) && q.IsChecked).Price,
								CurrencyId = viewModel.ExtraFees.FirstOrDefault(q => (q.ExtraFeeName.Contains("Fotoğraf") || q.ExtraFeeName.Contains("Photo") && !q.ExtraFeeName.Contains("Photocopy")) && q.IsChecked).CurrencyId,
								Status = (int)PhotoBoothStatus.Expired,
								RequestedBy = UserSession.UserId,
							};

							var apiPhotoBoothResponse = await PortalHttpClientHelper
								.PostAsJsonAsync<ApiResponse<AddApiResponse>>
								(apiPhotoBoothRequest, ApiMethodName.Appointment.AddPhotoBooth, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
								.ConfigureAwait(false);
						}
					}

				}

				if (apiResponse.Data.BranchCountryId == 152) // KSA
				{

					if (viewModel.ExtraFees.Any(p => p.IsChecked && !p.IsPreChecked))
					{
						var apiRequestSendKsa = new SendKsaIcrGenerateEInvoiceApiRequest
						{
							ApplicationId = id,
							CreatedBy = UserSession.UserId,
							CreatedAt = DateTime.Now,
							ExtraFeeIds = viewModel.ExtraFees.Where(p => p.IsChecked && !p.IsPreChecked).Select(p => p.ExtraFeeId).ToList(),
						};

						var apiResponseSendKsa = await PortalHttpClientHelper
							.PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
								(apiRequestSendKsa, ApiMethodName.Appointment.SendKsaIcrGenerateEInvoice, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
							.ConfigureAwait(false);

						if (!await apiResponseSendKsa.Validate(out ResultModel resultKsa).ConfigureAwait(false))
							return Json(resultKsa);

					}

					if (viewModel.ExtraFees.Any(p => !p.IsChecked && p.IsPreChecked))
					{
						var apiRequestKsaIcrPartialOrCancelEInvoice = new SendKsaIcrGenerateEInvoiceApiRequest
						{
							ApplicationId = id,
							CreatedAt = DateTime.Now,
							CreatedBy = UserSession.UserId,
							ExtraFeeIds = viewModel.ExtraFees.Where(p => !p.IsChecked && p.IsPreChecked).Select(p => p.ExtraFeeId).ToList(),
						};

						var apiResponseSendKsaIcrPartialOrCancelEInvoice = await PortalHttpClientHelper
							.PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
								(apiRequestKsaIcrPartialOrCancelEInvoice, ApiMethodName.Appointment.SendKsaIcrPartialOrCancelEInvoice, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
							.ConfigureAwait(false);

						if (!await apiResponseSendKsaIcrPartialOrCancelEInvoice.Validate(out ResultModel resultKsaIcrPartialOrCancel).ConfigureAwait(false))
							return Json(resultKsaIcrPartialOrCancel);
					}

					if (viewModel.ExtraFees.Any(p => p.IsChecked && p.IsPreChecked && p.Quantity != p.IsPreQuantity))
					{
						List<SendKsaIcrGenerateEInvoiceApiRequest.ExtraFeeQuantity> extraFeeQuantityList =
							new List<SendKsaIcrGenerateEInvoiceApiRequest.ExtraFeeQuantity>();

						foreach (var item in viewModel.ExtraFees.Where(p => p.IsChecked && p.IsPreChecked && p.Quantity != p.IsPreQuantity))
						{
							var extraFeeQuantity = new SendKsaIcrGenerateEInvoiceApiRequest.ExtraFeeQuantity()
							{
								ExtraFeeId = item.ExtraFeeId,
								IsPreQuantity = item.IsPreQuantity
							};
							extraFeeQuantityList.Add(extraFeeQuantity);
						}

						var apiRequestSendKsa = new SendKsaIcrGenerateEInvoiceApiRequest
						{
							ApplicationId = id,
							CreatedBy = UserSession.UserId,
							CreatedAt = DateTime.Now,
							ExtraFeeIds = viewModel.ExtraFees.Where(p => p.IsChecked && p.IsPreChecked && p.Quantity != p.IsPreQuantity).Select(p => p.ExtraFeeId).ToList(),
							QuantityExtraFeeIds = extraFeeQuantityList
						};

						var apiResponseSendKsa = await PortalHttpClientHelper
							.PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
								(apiRequestSendKsa, ApiMethodName.Appointment.SendKsaIcrGenerateEInvoice, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
							.ConfigureAwait(false);

						if (!await apiResponseSendKsa.Validate(out ResultModel resultKsa).ConfigureAwait(false))
							return Json(resultKsa);

						var apiResponseSendKsaIcrPartialOrCancelEInvoice = await PortalHttpClientHelper
							.PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
								(apiRequestSendKsa, ApiMethodName.Appointment.SendKsaIcrPartialOrCancelEInvoice, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
							.ConfigureAwait(false);

						if (!await apiResponseSendKsaIcrPartialOrCancelEInvoice.Validate(out ResultModel resultKsaIcrPartialOrCancel).ConfigureAwait(false))
							return Json(resultKsaIcrPartialOrCancel);
					}

				}
			}

			return Json(new ResultModel
			{
				Data = apiResponse.Data,
				Message = ResultMessage.OperationIsSuccessful.ToDescription(),
				ResultType = ResultType.Success
			});
		}

		[HttpPut]
		public async Task<IActionResult> UpdateApplicationEasyUse(ApplicationEasyUseViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			if (string.IsNullOrEmpty(viewModel.EncryptedId))
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			int id = viewModel.EncryptedId.ToDecryptInt();

			var apiRequest = new UpdateApplicationEasyUseApiRequest
			{
				Id = id,
				IsActive = viewModel.IsActive,
				IsDeleted = viewModel.IsDeleted,
				UpdateApplicationTime = viewModel.UpdateApplicationTime,
				RequestedBy = UserSession.UserId,
				ResetApplicationStatus = viewModel.ResetApplicationStatus,
				ApplicationTime = viewModel.ApplicationTime,
				StatusId = viewModel.ResetApplicationStatus ? (int)ApplicationStatus.Active : viewModel.StatusId,
				ApplicationInsurances = viewModel.ApplicationInsurances?.Select(p2 => new UpdateApplicationEasyUseApiRequest.ApplicationInsurance
				{
					Id = p2.Id,
					IsActive = p2.IsActive,
					IsDeleted = p2.IsDeleted
				}).ToList()
			};

			var apiResponse = await PortalHttpClientHelper
				.PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
				(apiRequest, ApiMethodName.Appointment.UpdateApplicationEasyUse, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel
			{
				Data = apiResponse.Data,
				Message = ResultMessage.OperationIsSuccessful.ToDescription(),
				ResultType = ResultType.Success
			});
		}

		[HttpGet]
		public async Task<IActionResult> IsRequired(int branchApplicationCountryId)
		{
			bool isRequired = false;
			var apiResponse = await PortalHttpClientHelper
			.GetAsync<ApiResponse<ApplicationFormElementsApiResponse>>
			($"{ApiMethodName.Appointment.IsRequired + branchApplicationCountryId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
			.ConfigureAwait(false);

			List<int> FormElementIds = new List<int>();

			if (apiResponse.Data.ApplicationFormElements.Count != 0)
			{
				FormElementIds = apiResponse.Data.ApplicationFormElements.Select(p => p.FormElementId).ToList();
			}
			else
				FormElementIds = null;

			return Json(FormElementIds);
		}

		[HttpGet]
		public async Task<IActionResult> IsInsuranceAllowsForMoreThanOneYear(string extraFee)
		{
			var apiResponse = await PortalHttpClientHelper
			.GetAsync<ApiResponse<ApplicationIsInsuranceAllowsForMoreThanOneYearApiResponse>>
			($"{ApiMethodName.Appointment.IsInsuranceAllowsForMoreThanOneYear + extraFee}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
			.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(apiResponse.Data.ExtraFeeIds);
		}

		[HttpPut]
		public async Task<IActionResult> UpdateApplicationLocalAuthorityStatus(ApplicationLocalAuthorityStatusViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			if (string.IsNullOrEmpty(viewModel.EncryptedId))
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			int id = viewModel.EncryptedId.ToDecryptInt();

			var apiRequest = new UpdateApplicationLocalAuthorityStatusApiRequest
			{
				Id = id,
				LocalAuthorityStatusId = viewModel.LocalAuthorityStatusId
			};

			var apiResponse = await PortalHttpClientHelper
				.PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
				(apiRequest, ApiMethodName.Appointment.UpdateApplicationLocalAuthorityStatus, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel
			{
				Data = apiResponse.Data,
				Message = ResultMessage.OperationIsSuccessful.ToDescription(),
				ResultType = ResultType.Success
			});
		}

		[HttpGet]
		public async Task<IActionResult> CheckContactInformationUpdateAllowed(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
						  .GetAsync<ApiResponse<CheckContactInformationUpdateAllowedApiResponse>>
						  (ApiMethodName.Appointment.CheckContactInformationUpdateAllowed + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
						  .ConfigureAwait(false);

			if (apiResponse == null)
				return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		public IActionResult IsExtraPackageUpdateAllowed()
		{
			return Json(new { Result = true });
		}

		public IActionResult IsPassiveDeletedApplicationsAllowed()
		{
			return Json(new { Result = true });
		}

		[ActionAttribute(IsMenuItem = true)]
		public IActionResult List()
		{
			var isAuthorizedInterviewChecbox = false;

			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object value))
			{
				var roleActions = (RoleActionApiResponse)value;

				isAuthorizedInterviewChecbox = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
					.Any(p => p.RoleActions.Any(q => q.Action.Method == "IsAuthorizedInterviewChecbox" && q.Action.IsActive));
			}

			var viewModel = new FilterApplicationViewModel()
			{
				IsAuthorizedInterviewChecbox = isAuthorizedInterviewChecbox,
			};

			return View(viewModel);
		}

		public IActionResult ListAll()
		{
			var viewModel = new FilterApplicationViewModel()
			{
				FilterAllBranchs = true
			};

			return View(viewModel);
		}

		public IActionResult ListTurkmenistan()
		{
			var viewModel = new FilterApplicationViewModel()
			{
				FilterAllBranchs = false,
				FilterBranchId = 37,
				FilterApplicationStatusIds = new List<int?>
				{
					ApplicationStatusType.WaitingForIHB.ToInt(),
					ApplicationStatusType.IHBUploaded.ToInt(),
				},
				FilterIsTurkmenistanPage = true,
				FilterAllList = false,
				FilterSuitable = false,
				FilterNotSuitable = false,
				FilterWaiting = false,
			};

            return View(viewModel);
		}

		public IActionResult ListRejection()
		{
			var viewModel = new FilterApplicationViewModel()
			{
				FilterAllBranchs = true,
				FilterStatusHistoryStatusIds = new List<int>() { ApplicationStatusType.Rejection.ToInt(), ApplicationStatusType.IstizanRejection.ToInt(), ApplicationStatusType.UnrealDocument.ToInt(), ApplicationStatusType.RejectionWithCountryEntryBanned.ToInt() }
			};

			return View(viewModel);
		}

		public async Task<IActionResult> GetPaginatedApplications([DataSourceRequest] DataSourceRequest request, FilterApplicationViewModel filterViewModel)
		{
			var paginationFilter = request.GetPaginationFilter(filterViewModel);

			var isAuthorizedForEarlyApplicationAuthorization = false;

			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object actions))
			{
				var roleActions = (RoleActionApiResponse)actions;

				isAuthorizedForEarlyApplicationAuthorization = roleActions.RoleActionSites.Where(r => UserSession.RoleIds.Contains(r.Role.Id))
					.Any(p => p.RoleActions.Any(q => (q.Action.ActionTranslations.Any(r => r.Name == SiteResources.IndiaEarlyApplicationAuthorization) || q.Action.Method == "IsAuthorizedForNepal") && q.Action.IsActive));
			}

			var apiRequest = new PaginatedApplicationsApiRequest
			{
				BranchIds = filterViewModel.FilterBranchId.HasValue ? new List<int>() { filterViewModel.FilterBranchId.GetValueOrDefault() } :
							(filterViewModel.FilterAllBranchs ? UserSession.BranchIds
							: UserSession.BranchId.HasValue ? new List<int>() { UserSession.BranchId.Value }
							: null),
				CountryIds = filterViewModel.FilterAllBranchs ? UserSession.CountryIds
							: (UserSession.CountryId.HasValue ? new List<int>() { UserSession.CountryId.Value }
							 : null),
				CountryId = filterViewModel.FilterCountryId,
				FilterResidingCountryId = filterViewModel.FilterResidingCountryId,
				FilterVerificationTypeId = filterViewModel.FilterVerificationTypeId,
				AgencyId = filterViewModel.FilterAgencyId,
				ApplicationNumber = filterViewModel.FilterApplicationNumber,
				ApplicantTypeId = filterViewModel.FilterApplicantTypeId,
				ApplicationTypeIds = filterViewModel.FilterApplicationTypeId.HasValue ? new List<int>() { filterViewModel.FilterApplicationTypeId.Value } : null,
				Name = filterViewModel.FilterName,
				Surname = filterViewModel.FilterSurname,
				PassportNumber = filterViewModel.FilterPassportNumber,
				NationalityId = filterViewModel.FilterNationalityId,
				Email = filterViewModel.FilterEmail,
				PhoneNumber = filterViewModel.FilterPhoneNumber,
				VisaCategoryId = filterViewModel.FilterVisaCategoryId,
				IngonreCancelledApplications = !filterViewModel.FilterAllowPassiveDeletedApplications,
				AllowPassiveDeletedApplications = filterViewModel.FilterAllowPassiveDeletedApplications,
				FilterStartDate = filterViewModel.FilterStartDate,
				FilterEndDate = filterViewModel.FilterEndDate,
				ApplicationStatusHistoryStatusIds = filterViewModel.FilterStatusHistoryStatusIds != null ? filterViewModel.FilterStatusHistoryStatusIds : null,
				IncludeApplicationStatusHistories = filterViewModel.FilterStatusHistoryStatusIds != null ? true : false,
				ApplicationDate = filterViewModel.FilterApplicationDate,
				RejectionStartDate = filterViewModel.FilterRejectionStartDate,
				RejectionEndDate = filterViewModel.FilterRejectionEndDate,
				IgnoreEarlyApplications = isAuthorizedForEarlyApplicationAuthorization,
				Pagination = new PaginationApiRequest
				{
					Page = paginationFilter.Page,
					PageSize = paginationFilter.PageSize,
					OrderBy = paginationFilter.OrderBy,
					SortDirection = paginationFilter.SortDirection
				},
				isRefundable = true
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.GetPaginatedApplications, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var paginatedData = new List<ApplicationViewModel>();

			var visaCategories = GetCachedVisaCategories();

			if (apiResponse.Data != null && apiResponse.Data.Items.Any())
			{
				paginatedData = apiResponse.Data.Items.First().Applications
					.Select(p => new ApplicationViewModel
					{
						EncryptedId = p.Id.ToEncrypt(),
						BranchApplicationCountryId = p.BranchApplicationCountryId,
						BranchName = p.BranchName,
						CountryName = p.CountryName,
						ResidingCountryName = p.ResidingCountryName,

						GatewayServiceFeeName = p.GatewayServiceFeeName,
						GatewayServicePrice = p.GatewayServicePrice != 0 ? p.GatewayServicePrice : null,
						GatewayServiceFeeCurrencyId = p.GatewayServiceFeeCurrencyId,
						VisaFeeName = p.VisaFeeName,
						VisaPrice = p.VisaPrice != 0 ? p.VisaPrice : null,
						ClaimNo = p.ClaimNo,
						SendVisaRejection = p.SendVisaRejection,
						ApplicantTypeId = p.ApplicantTypeId,
						ApplicationTypeId = p.ApplicationTypeId,
						PassportNumber = p.PassportNumber,
						PassportExpireDate = p.PassportExpireDate,
						ApplicationPassportStatusId = p.ApplicationPassportStatusId,
						AgencyId = p.AgencyId,

						TitleId = p.TitleId,
						Name = p.Name,
						Surname = p.Surname,
						BirthDate = p.BirthDate,
						GenderId = p.GenderId,
						MaritalStatusId = p.MaritalStatusId,
						NationalityId = p.NationalityId,
						Nationality = p.Nationality,
						MaidenName = p.MaidenName,
						FatherName = p.FatherName,
						MotherName = p.MotherName,
						Email = p.Email,
						PhoneNumber1 = p.PhoneNumber1,
						PhoneNumber2 = p.PhoneNumber2,
						Address = p.Address,

						ApplicationTime = p.ApplicationTime,
						ApplicationTimeForListRejection = p.ApplicationTime.ToString("d"),
						ApplicationStatusId = p.ApplicationStatusId,
						ApplicationStatus = p.ApplicationStatus,
						Note = p.Note,
						RelationalApplicationId = p.RelationalApplicationId,

						CreatedBy = p.CreatedBy,
						CreatedByNameSurname = p.CreatedByNameSurname,
						StatusId = p.StatusId,
						HasApplicationNotes = p.HasApplicationNotes,
						IsActive = p.IsActive,
						IsDeleted = p.IsDeleted,

						Insurance = new ApplicationViewModel.ApplicationInsurance()
						{
							Number = p.Insurance.Number[0],
							StartDate = p.Insurance.StartDate?.ToString("d"),
							EndDate = p.Insurance.EndDate?.ToString("d"),
							Provider = EnumHelper.GetEnumDescription(typeof(ProviderType), p.Insurance.ProviderId.ToString())
						},

						Document = new ApplicationViewModel.ApplicationDocument
						{
							VisaCategoryId = p.Document.VisaCategoryId,
							VisaCategory = GetVisaCategoryNameFromId(p.Document.VisaCategoryId,visaCategories)
						},

						StatusHistories = p.StatusHistories.Select(x => new ApplicationViewModel.ApplicationStatusHistory()
						{
							ApplicationStatusId = x.ApplicationStatusId,
							StatusDate = x.StatusDate/*.Date*/
						}),

						ExtraFees = p.ExtraFees.Select(p => new ApplicationViewModel.ApplicationExtraFee
						{
							TypeId = p.TypeId,
							ExtraFeeName = p.ExtraFeeName,
							Price = p.Price,
							Tax = p.Tax,
							CurrencyId = p.CurrencyId,
							Quantity = p.Quantity,
							Category = p.Category
						}).ToList(),

						VerificationTypeId = p.RejectionApprovalHistories.Any() ? p.RejectionApprovalHistories.OrderByDescending(o => o.CreatedAt).First().NotificationTypeId : null,

                    }).ToList();
			}
			return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
		}
		public async Task<FileContentResult> GetApplicationsVisaRejectionReport(FilterApplicationViewModel filterViewModel)
		{
			var reportName = SiteResources.ApplicationVisaRejection.ToTitleCase();

			filterViewModel.FilterAllBranchs = true;
			filterViewModel.FilterStatusHistoryStatusIds = new List<int>() { ApplicationStatusType.Rejection.ToInt(), ApplicationStatusType.IstizanRejection.ToInt(), ApplicationStatusType.UnrealDocument.ToInt(), ApplicationStatusType.RejectionWithCountryEntryBanned.ToInt() };

			var apiRequest = new GetApplicationsVisaRejectionReportApiRequest
			{
				BranchIds = filterViewModel.FilterBranchId.HasValue ? new List<int>() { filterViewModel.FilterBranchId.GetValueOrDefault() } :
							(filterViewModel.FilterAllBranchs ? UserSession.BranchIds
							: UserSession.BranchId.HasValue ? new List<int>() { UserSession.BranchId.Value }
							: null),
				CountryIds = filterViewModel.FilterAllBranchs ? UserSession.CountryIds
							: (UserSession.CountryId.HasValue ? new List<int>() { UserSession.CountryId.Value }
							 : null),
				BranchId = filterViewModel.FilterBranchId,
				CountryId = filterViewModel.FilterCountryId,
				FilterResidingCountryId = filterViewModel.FilterResidingCountryId,
				FilterVerificationTypeId = filterViewModel.FilterVerificationTypeId,
				AgencyId = filterViewModel.FilterAgencyId,
				ApplicationId = filterViewModel.FilterApplicationNumber != null ? Int32.Parse(filterViewModel.FilterApplicationNumber) : null,
				ApplicantTypeId = filterViewModel.FilterApplicantTypeId,
				ApplicationTypeIds = filterViewModel.FilterApplicationTypeId.HasValue ? new List<int>() { filterViewModel.FilterApplicationTypeId.Value } : null,
				Name = filterViewModel.FilterName,
				Surname = filterViewModel.FilterSurname,
				PassportNumber = filterViewModel.FilterPassportNumber,
				NationalityId = filterViewModel.FilterNationalityId,
				PhoneNumber = filterViewModel.FilterPhoneNumber,
				VisaCategoryId = filterViewModel.FilterVisaCategoryId,
				IngonreCancelledApplications = !filterViewModel.FilterAllowPassiveDeletedApplications,
				AllowPassiveDeletedApplications = filterViewModel.FilterAllowPassiveDeletedApplications,
				FilterStartDate = filterViewModel.FilterStartDate,
				FilterEndDate = filterViewModel.FilterEndDate,
				ApplicationStatusHistoryStatusIds = filterViewModel.FilterStatusHistoryStatusIds != null ? filterViewModel.FilterStatusHistoryStatusIds : null,
				ApplicationDate = filterViewModel.FilterApplicationDate,
				RejectionStartDate = filterViewModel.FilterRejectionStartDate,
				RejectionEndDate = filterViewModel.FilterRejectionEndDate,
				isRefundable = true
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<GetApplicationsVisaRejectionReportApiResponse>>
				(apiRequest, ApiMethodName.Appointment.GetApplicationsVisaRejectionReport, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			using (var workbook = new XLWorkbook())
			{
				var worksheet = workbook.Worksheets.Add(reportName);
				var currentRow = 1;
				int orderNumber = 0;
				worksheet.Cell(currentRow, 1).Value = SiteResources.ApplicationNumber.ToTitleCase();
				worksheet.Cell(currentRow, 2).Value = SiteResources.PassportNumber.ToTitleCase();
				worksheet.Cell(currentRow, 3).Value = SiteResources.ApplicationTime.ToTitleCase();
				worksheet.Cell(currentRow, 4).Value = SiteResources.BranchName.ToTitleCase();
				worksheet.Cell(currentRow, 5).Value = SiteResources.ResidingCountry.ToTitleCase();
				worksheet.Cell(currentRow, 6).Value = SiteResources.Name.ToTitleCase();
				worksheet.Cell(currentRow, 7).Value = SiteResources.Surname.ToTitleCase();
				worksheet.Cell(currentRow, 8).Value = SiteResources.PolicyNumber.ToTitleCase();
				worksheet.Cell(currentRow, 9).Value = SiteResources.RejectedDate.ToTitleCase();
				worksheet.Cell(currentRow, 10).Value = SiteResources.RefundDate.ToTitleCase();
				worksheet.Cell(currentRow, 11).Value = SiteResources.VerificationType.ToTitleCase();
				worksheet.Cell(currentRow, 12).Value = SiteResources.RefundedFeeName.ToTitleCase();
				worksheet.Cell(currentRow, 13).Value = SiteResources.RefundedFeeAmount.ToTitleCase();
				worksheet.Cell(currentRow, 14).Value = SiteResources.RefundedFeeName.ToTitleCase();
				worksheet.Cell(currentRow, 15).Value = SiteResources.RefundedFeeAmount.ToTitleCase();
				worksheet.Cell(currentRow, 16).Value = SiteResources.ClaimNo.ToTitleCase();
				worksheet.Cell(currentRow, 17).Value = SiteResources.InsuranceStartDate.ToTitleCase();
				worksheet.Cell(currentRow, 18).Value = SiteResources.InsuranceEndDate.ToTitleCase();
				var rangeTableHeader = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 18));
				rangeTableHeader.Style.Font.SetBold();

				currentRow++;
				for (int i = 0; i < apiResponse.Data.Appointments.Count(); i++)
				{
					StringBuilder bld = new StringBuilder();
					var data = apiResponse.Data.Appointments.ElementAt(i);

					orderNumber += 1;
					worksheet.Cell(currentRow, 1).Value = data.Id.ToApplicationNumber();
					worksheet.Cell(currentRow, 2).Value = data.PassportNumber;
					worksheet.Cell(currentRow, 2).DataType = XLDataType.Text;
					worksheet.Cell(currentRow, 3).Value = data.ApplicationTime;
					worksheet.Cell(currentRow, 3).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
					worksheet.Cell(currentRow, 4).Value = data.BranchName;
					worksheet.Cell(currentRow, 5).Value = data.ResidingCountryName;
					worksheet.Cell(currentRow, 6).Value = data.Name;
					worksheet.Cell(currentRow, 7).Value = data.Surname;
					worksheet.Cell(currentRow, 8).Value = data.InsuranceNumber;
					worksheet.Cell(currentRow, 9).Value = data.RejectedDate;
					worksheet.Cell(currentRow, 9).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
					worksheet.Cell(currentRow, 10).Value = data.RefundDate;
					worksheet.Cell(currentRow, 10).Style.DateFormat.Format = SiteResources.DatePickerFirstMonthFormatView;
                    worksheet.Cell(currentRow, 11).Value = data.VerificationTypeId != null 
                        ? EnumHelper.GetEnumDescription(typeof(Portal.Gateway.Contracts.Entities.Dto.Appointment.Application.VeriyfContactInformationMethod), data.VerificationTypeId.ToString())
                        : "-";
                    worksheet.Cell(currentRow, 12).Value = data.GatewayServiceFeeName;
					worksheet.Cell(currentRow, 13).Value = data.GatewayServicePrice != 0 ? data.GatewayServicePrice : "";
					worksheet.Cell(currentRow, 14).Value = data.VisaFeeName;
					worksheet.Cell(currentRow, 15).Value = data.VisaPrice != 0 ? data.VisaPrice : "";
					worksheet.Cell(currentRow, 16).Value = data.ClaimNo;
					worksheet.Cell(currentRow, 16).DataType = XLDataType.Text;
					worksheet.Cell(currentRow, 17).Value = data.InsuranceStartDate;
					worksheet.Cell(currentRow, 17).Style.DateFormat.Format = SiteResources.DatePickerFormatView;
					worksheet.Cell(currentRow, 18).Value = data.InsuranceEndDate;
					worksheet.Cell(currentRow, 18).Style.DateFormat.Format = SiteResources.DatePickerFormatView;

					currentRow++;
				}
				var rangeTable = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, 18));
				rangeTable.Style.Border.SetTopBorder(XLBorderStyleValues.Thin);
				rangeTable.Style.Border.SetBottomBorder(XLBorderStyleValues.Thin);
				rangeTable.Style.Border.SetLeftBorder(XLBorderStyleValues.Thin);
				rangeTable.Style.Border.SetRightBorder(XLBorderStyleValues.Thin);
				rangeTable.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);
				rangeTable.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

				var rangeAlignment = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, 18));
				rangeAlignment.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

				for (int i = 1; i <= 18; i++)
				{
					worksheet.Column(i).AdjustToContents();
				}

				using (var stream = new MemoryStream())
				{
					workbook.SaveAs(stream);
					var content = stream.ToArray();
					return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{reportName} - {DateTime.UtcNow.ToString("ddMMyyyy")}.xlsx");
				}
			}
		}
		public async Task<IActionResult> GetSanitizedPaginatedApplications([DataSourceRequest] DataSourceRequest request, FilterApplicationViewModel filterViewModel)
		{
			var paginationFilter = request.GetPaginationFilter(filterViewModel);

			var isAuthorizedForEarlyApplicationAuthorization = false;

			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object actions))
			{
				var roleActions = (RoleActionApiResponse)actions;

				isAuthorizedForEarlyApplicationAuthorization = roleActions.RoleActionSites.Where(r => UserSession.RoleIds.Contains(r.Role.Id))
					.Any(p => p.RoleActions.Any(q => (q.Action.ActionTranslations.Any(r => r.Name == SiteResources.IndiaEarlyApplicationAuthorization) || q.Action.Method == "IsAuthorizedForNepal") && q.Action.IsActive));
			}

            var ihbStatusList = EnumHelper.GetEnumAsDictionary(typeof(IhbStatus)).Select(x => new SelectListItem { Value = x.Key.ToString(), Text = x.Value.ToTitleCase() });

            var apiRequest = new PaginatedApplicationsApiRequest
			{
				BranchIds = filterViewModel.FilterBranchId.HasValue ? new List<int>() { filterViewModel.FilterBranchId.GetValueOrDefault() } :
							(filterViewModel.FilterAllBranchs ? UserSession.BranchIds
							: UserSession.BranchId.HasValue ? new List<int>() { UserSession.BranchId.Value }
							: null),
				CountryIds = filterViewModel.FilterAllBranchs ? UserSession.CountryIds
							: (UserSession.CountryId.HasValue ? new List<int>() { UserSession.CountryId.Value }
							 : null),
				CountryId = filterViewModel.FilterCountryId,
				AgencyId = filterViewModel.FilterAgencyId,
				ApplicationNumber = filterViewModel.FilterApplicationNumber,
				ApplicantTypeId = filterViewModel.FilterApplicantTypeId,
				ApplicationTypeIds = filterViewModel.FilterApplicationTypeId.HasValue ? new List<int>() { filterViewModel.FilterApplicationTypeId.Value } : null,
				Name = filterViewModel.FilterName,
				Surname = filterViewModel.FilterSurname,
				PassportNumber = filterViewModel.FilterPassportNumber,
				NationalityId = filterViewModel.FilterNationalityId,
				Email = filterViewModel.FilterEmail,
				PhoneNumber = filterViewModel.FilterPhoneNumber,
				VisaCategoryId = filterViewModel.FilterVisaCategoryId,
				IngonreCancelledApplications = !filterViewModel.FilterAllowPassiveDeletedApplications,
				AllowPassiveDeletedApplications = filterViewModel.FilterAllowPassiveDeletedApplications,
				FilterStartDate = filterViewModel.FilterStartDate,
				FilterEndDate = filterViewModel.FilterEndDate,
				FilterResidenceApplication = filterViewModel.FilterResidenceApplication,
				ApplicationStatusHistoryStatusIds = filterViewModel.FilterStatusHistoryStatusIds != null ? filterViewModel.FilterStatusHistoryStatusIds : null,
				IncludeApplicationStatusHistories = filterViewModel.FilterStatusHistoryStatusIds != null ? true : false,
				DocumentWaitingTimeId = filterViewModel.FilterWaitingTimeForDocument,
				ApplicationDate = filterViewModel.FilterApplicationDate,
				RejectionStartDate = filterViewModel.FilterRejectionStartDate,
				RejectionEndDate = filterViewModel.FilterRejectionEndDate,
				LocalAuthorityStatusId = filterViewModel.FilterLocalAuthorityStatusId,
				ApplicationStatusIds = filterViewModel.FilterApplicationStatusIds,
				FilterIsTurkmenistanPage = filterViewModel.FilterIsTurkmenistanPage,
				Pagination = new PaginationApiRequest
				{
					Page = paginationFilter.Page,
					PageSize = paginationFilter.PageSize,
					OrderBy = paginationFilter.OrderBy,
					SortDirection = paginationFilter.SortDirection
				},
				EvaluationDate = filterViewModel.FilterEvaluationDate,
				TurkmenPageAllList = filterViewModel.FilterAllList,
				FilterSuitable = filterViewModel.FilterSuitable,
				FilterNotSuitable = filterViewModel.FilterNotSuitable,
				FilterWaiting = filterViewModel.FilterWaiting,
				IgnoreEarlyApplications = isAuthorizedForEarlyApplicationAuthorization && !filterViewModel.FilterIsTurkmenistanPage,
                FilterBirthDate = filterViewModel.FilterBirthDate
            };

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.GetSanitizedPaginatedApplications, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var paginatedData = new List<ApplicationViewModel>();

			if (apiResponse.Data != null && apiResponse.Data.Items.Any())
			{
				paginatedData = apiResponse.Data.Items.First().Applications
					.Select(p => new ApplicationViewModel
					{
						EncryptedId = p.Id.ToEncrypt(),
						BranchApplicationCountryId = p.BranchApplicationCountryId,
						BranchName = p.BranchName,
						BranchCountryIso3 = p.BranchCountryIso3,
						CountryName = p.CountryName,

						ExtraGatewayServiceFeeName = p.ExtraGatewayServiceFeeName,
						ExtraGatewayServicePrice = p.ExtraGatewayServicePrice,
						ExtraVisaFeeName = p.ExtraVisaFeeName,
						ExtraVisaPrice = p.ExtraVisaPrice,

						ApplicantTypeId = p.ApplicantTypeId,
						ApplicationTypeId = p.ApplicationTypeId,
						PassportNumber = p.PassportNumber,
						PassportExpireDate = p.PassportExpireDate,
						ApplicationPassportStatusId = p.ApplicationPassportStatusId,
						AgencyId = p.AgencyId,
						LocalAuthorityStatusId = p.LocalAuthorityStatusId,

						TitleId = p.TitleId,
						Name = p.Name,
						Surname = p.Surname,
						BirthDate = p.BirthDate,
						BirthDateText = p.BirthDate.ToString("dd/MM/yyyy"),
						GenderId = p.GenderId,
						MaritalStatusId = p.MaritalStatusId,
						NationalityId = p.NationalityId,
						Nationality = p.Nationality,
						MaidenName = p.MaidenName,
						FatherName = p.FatherName,
						MotherName = p.MotherName,
						Email = p.Email,
						PhoneNumber1 = p.PhoneNumber1,
						PhoneNumber2 = p.PhoneNumber2,
						Address = p.Address,
						ForeignCityId = p.ForeignCityId,
						CargoProviderId = p.CargoProviderId,
						//City = p.City,
						PostalCode = p.PostalCode,
						AreaId = p.AreaId,
						AreaName = p.AreaName,
						GovernorateId = p.GovernorateId,
						GovernorateName = p.GovernorateName,
						IsCargoIntegrationSelected = p.IsCargoIntegrationSelected,
						HasApplicationFile = p.HasApplicationFile,
						Document = new ApplicationViewModel.ApplicationDocument
						{
							ProvidedWithHasRelatedInsurance = p.Document.ProvidedWithHasRelatedInsurance
						},
						ApplicationTime = p.ApplicationTime,
						ApplicationTimeText = p.ApplicationTime.DateTime.ToString("dd/MM/yyyy HH:mm:ss"),
						ApplicationTimeForListRejection = p.ApplicationTime.ToString("d"),
						ApplicationStatusId = p.ApplicationStatusId,
						ApplicationStatus = p.ApplicationStatus,
						Note = p.Note,
						ApplicationNote = p.ApplicationNote,
						RelationalApplicationId = p.RelationalApplicationId,

						CreatedBy = p.CreatedBy,
						CreatedByNameSurname = p.CreatedByNameSurname,
						StatusId = p.StatusId,
						HasApplicationNotes = p.HasApplicationNotes,
						IsActive = p.IsActive,
						IsDeleted = p.IsDeleted,
						IhbNumber = p.IhbNumber,
						IhbDocumentNumber = p.IhbDocumentNumber,
						EvaluationTime = p.EvaluationTime?.ToString("dd/MM/yyyy HH:mm:ss"),
						IhbStatusId = p.IHBStatusId,
                        IhbStatus = new ApplicationIhbStatusViewModel()
                        {
                            Id = p.IHBStatusId,
                            Name = ihbStatusList.FirstOrDefault(s => s.Value.ToInt() == p.IHBStatusId)?.Text ?? string.Empty
                        },
                        EncryptedIhbDocumentId = p.EncryptedIhbDocumentId,
						EncryptedIhbOrderId = p.EncryptedIhbOrderId,
                        IsInterviewRequired = p.IsInterviewRequired,
						IsInterviewDone = p.IsInterviewDone
					}).ToList();
			}
			return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
		}
        public IActionResult GetIhbStatus()
        {
            return Json(EnumHelper.GetEnumAsDictionary(typeof(IhbStatus)).Select(x => new ApplicationCurrentStatusViewModel { Id = x.Key, Name = x.Value.ToTitleCase() }));
        }

        public async Task<IActionResult> GetDetailedInformationAboutExemptPersons([DataSourceRequest] DataSourceRequest request, FilterApplicationExemptPersonViewModel filterViewModel)
        {
            var paginationFilter = request.GetPaginationFilter(filterViewModel);

            var apiRequest = new PaginatedApplicationExemptPersonApiRequest
            {
                ApplicationId = filterViewModel.EncryptedApplicationId.ToDecryptInt(),
                Pagination = new PaginationApiRequest
                {
                    Page = paginationFilter.Page,
                    PageSize = paginationFilter.PageSize,
                    OrderBy = paginationFilter.OrderBy,
                    SortDirection = paginationFilter.SortDirection
                }
            };

            var apiResponse = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationDetailExemptPersonsApiResponse>>>
                (apiRequest, ApiMethodName.Appointment.GetDetailedInformationAboutExemptPersons, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);

            var paginatedData = new List<ApplicationDetailExemptPersonsViewModel>();

            if (apiResponse.Data != null && apiResponse.Data.Items.Any())
            {
                paginatedData = apiResponse.Data.Items.First().ApplicationDetailExemptPersonResponses
                    .Select(p => new ApplicationDetailExemptPersonsViewModel
                    {
						EncryptedId = p.Id.ToEncrypt(),
                        PassportNumber = p.PassportNumber,
                        Name = p.Name,
                        Surname = p.Surname,
						NationalityId = p.NationalityId,
                        Nationality = p.Nationality,
                        Email = p.Email,
						PhoneNumber = p.PhoneNumber,
                    }).ToList();
            }
            return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
        }

        [HttpGet]
		public async Task<IActionResult> GetApplicationVasType(string passportNumber, int branchApplicationCountryId)
		{
            if (string.IsNullOrEmpty(passportNumber) || branchApplicationCountryId == 0)
            {
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
            }

			var vasTypeId = -1;

            var apiResponse = await PortalHttpClientHelper
			.GetAsync<ApiResponse<AddApiResponse>>
			($"{ApiMethodName.Appointment.GetApplicationVasType + passportNumber}/{branchApplicationCountryId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
			.ConfigureAwait(false);

			if (apiResponse.Data != null)
				vasTypeId = apiResponse.Data.Id;

			return Json(vasTypeId);
		}

		[HttpGet]
		public IActionResult GetEmailFormatCheckTrue(string email)
		{
			bool emailFormatCheck = false;
			if (email != null)
			{
				emailFormatCheck = Regex.IsMatch(email,
					@"\A(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?)\Z",
					RegexOptions.IgnoreCase | RegexOptions.CultureInvariant);
			}

			return Json(emailFormatCheck);
		}

        [HttpGet]
        public async Task<IActionResult> ImportApplicationDataFromHistory(string encryptedBranchApplicationCountryId, string encryptedApplicationId)
        {
            if (string.IsNullOrEmpty(encryptedApplicationId))
            {
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
            }

            int id = encryptedApplicationId.ToDecryptInt();

            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationApiResponse>>
                (ApiMethodName.Appointment.GetApplication + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
            {
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
            }

            if (UserSession.BranchId.HasValue && UserSession.BranchId.Value != apiResponse.Data.BranchId)
            {
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
            }

            var apiRequest = new PreApplicationFormApiRequest
            {
                BranchApplicationCountryId = apiResponse.Data.BranchApplicationCountryId,
                ApplicationId = apiResponse.Data.Id,
            };

            var apiResponsePreApplicationForm = await PortalHttpClientHelper
                .PostAsJsonAsync<ApiResponse<PreApplicationFormApiResponse>>
                    (apiRequest, ApiMethodName.Appointment.GetPreApplicationForm, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponsePreApplicationForm.Validate(out ResultModel resultPreApplicationForm).ConfigureAwait(false))
            {
                return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
            }

            var isShowAllowDeniedPassportControl = false;
            var IsApplicationUpdateStatusCheckRoleCheck = false;

            if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object actions))
            {
                var roleActions = (RoleActionApiResponse)actions;

                isShowAllowDeniedPassportControl = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                    .Any(p => p.RoleActions.Any(q => q.Action.Method == "IsAllowedDeniedPassportNumber" && q.Action.IsActive));

                IsApplicationUpdateStatusCheckRoleCheck = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                    .Any(p => p.RoleActions.Any(q => q.Action.Method == "IsApplicationUpdateStatusCheckRoleCheck" && q.Action.IsActive)); // ilgili rol var ise kontrol etme
            }

            var viewModel = new AddUpdateApplicationViewModel
            {
                BranchApplicationCountryId = encryptedBranchApplicationCountryId.ToDecryptInt(),
                BranchName = apiResponsePreApplicationForm.Data.BranchName,
                CountryId = apiResponsePreApplicationForm.Data.CountryId,
                CountryName = apiResponsePreApplicationForm.Data.CountryName,
                BranchCountryId = apiResponsePreApplicationForm.Data.BranchCountryId,
                IsRelatedInsuranceForExemptActive = apiResponsePreApplicationForm.Data.IsRelatedInsuranceForExemptActive,
                InformationNotes = apiResponsePreApplicationForm.Data.InformationNotes,
                ShowAllowDeniedPassportControl = isShowAllowDeniedPassportControl,
                IsAllowDeniedPassport = apiResponse.Data.IsAllowDeniedPassport,
                IsPrinterIntegrationActive = apiResponsePreApplicationForm.Data.IsPrinterIntegrationActive,
                IsApplicationUpdateStatusCheckActive = IsApplicationUpdateStatusCheckRoleCheck ? false : apiResponsePreApplicationForm.Data.IsApplicationUpdateStatusCheckActive,
                IsPreApplicationConnectionActive = apiResponsePreApplicationForm.Data.IsPreApplicationConnectionActive,
                Reason = apiResponse.Data.Reason,
                DisableContactInformationVerify = apiResponsePreApplicationForm.Data.DisableContactInformationVerify,
                IsContactInformationVerified = apiResponsePreApplicationForm.Data.DisableContactInformationVerify,
                ShowPaymentMethods = apiResponsePreApplicationForm.Data.ShowPaymentMethods,
                //Description
                ApplicantTypeId = apiResponse.Data.ApplicantTypeId,
                ApplicationTypeId = apiResponse.Data.ApplicationTypeId,
                PassportNumber = apiResponse.Data.PassportNumber,
                PassportExpireDate = apiResponse.Data.PassportExpireDate,
                ApplicationPassportStatusId = apiResponse.Data.ApplicationPassportStatusId,
                NationalityId = apiResponse.Data.NationalityId,
                //BasicInformation
                TitleId = apiResponse.Data.TitleId,
                Name = apiResponse.Data.Name,
                Surname = apiResponse.Data.Surname,
                BirthDate = apiResponse.Data.BirthDate,
                GenderId = apiResponse.Data.GenderId,
                MaritalStatusId = apiResponse.Data.MaritalStatusId,
                ResidenceNumber = apiResponse.Data.ResidenceNumber,
                MaidenName = apiResponse.Data.MaidenName,
                FatherName = apiResponse.Data.FatherName,
                MotherName = apiResponse.Data.MotherName,
                Email = apiResponse.Data.Email,
                PhoneNumber1 = apiResponse.Data.PhoneNumber1,
                PhoneNumber2 = apiResponse.Data.PhoneNumber2,
                Address = apiResponse.Data.Address,
                ForeignCityId = apiResponse.Data.ForeignCityId,
                //City = apiResponse.Data.City,
                PostalCode = apiResponse.Data.PostalCode,
                NameOfSecondContactPerson = apiResponse.Data.NameOfSecondContactPerson,
                AreaId = apiResponse.Data.AreaId,
                AreaName = apiResponse.Data.AreaName,
                GovernorateId = apiResponse.Data.GovernorateId,
                GovernorateName = apiResponse.Data.GovernorateName,
                IsCargoIntegrationSelected = apiResponse.Data.IsCargoIntegrationSelected,
                //DocumentInformation
                TotalYearInCountry = apiResponse.Data.Document.TotalYearInCountry,
                ReimbursementTypeId = apiResponse.Data.Document.ReimbursementTypeId,
                ReimbursementSponsorDetail = apiResponse.Data.Document.ReimbursementSponsorDetail,
                Job = apiResponse.Data.Document.Job,
                OccupationId = apiResponse.Data.Document.OccupationId,
                CompanyName = apiResponse.Data.Document.CompanyName,
                TotalYearInCompany = apiResponse.Data.Document.TotalYearInCompany,
                MonthlySalary = apiResponse.Data.Document.MonthlySalary,
                MonthlySalaryCurrencyId = apiResponse.Data.Document.MonthlySalaryCurrencyId,
                HasBankAccount = apiResponse.Data.Document.HasBankAccount,
                BankBalance = apiResponse.Data.Document.BankBalance,
                BankBalanceCurrencyId = apiResponse.Data.Document.BankBalanceCurrencyId,
                HasDeed = apiResponse.Data.Document.HasDeed,
                VisaCategoryId = apiResponse.Data.Document.VisaCategoryId,
                AdditionalServiceTypeId = apiResponse.Data.Document.AdditionalServiceTypeId,
                NumberOfEntryId = apiResponse.Data.Document.NumberOfEntryId,
                HasEntryBan = apiResponse.Data.Document.HasEntryBan,
                CityName = apiResponse.Data.Document.CityName,
                AccomodationDetail = apiResponse.Data.Document.AccomodationDetail,
                HasRelativeAbroad = apiResponse.Data.Document.HasRelativeAbroad,
                RelativeLocation = apiResponse.Data.Document.RelativeLocation,
                PersonTravelWith = apiResponse.Data.Document.PersonTravelWith,
                PersonTravelWithHasVisa = apiResponse.Data.Document.PersonTravelWithHasVisa,
                ProvidedWithHasRelatedInsuranceId = apiResponse.Data.Document.ProvidedWithHasRelatedInsurance ? (int)YesNoQuestion.Yes : (int)YesNoQuestion.No,
                ShowReleatedInvidual = apiResponsePreApplicationForm.Data.IsRelatedInsuranceForExemptActive,
                ApplicationTogetherId = apiResponse.Data.Document.ApplicationTogether ? (int)YesNoQuestion.Yes : (int)YesNoQuestion.No,
                ApplicationTogetherFiftyYearCount = apiResponse.Data.Document.ApplicationTogetherFiftyYearCount,
                ApplicationTogetherFifteenYearCount = apiResponse.Data.Document.ApplicationTogetherFifteenYearCount,
                HasPersonVisitedTurkeyBefore = apiResponse.Data.Document.HasPersonVisitedTurkeyBefore,
                VehicleTypeId = apiResponse.Data.Document.VehicleTypeId,
                PlateNo = apiResponse.Data.Document.PlateNo,
                ModelYear = apiResponse.Data.Document.ModelYear,
                BrandModelId = apiResponse.Data.Document.BrandModelId,
                ChassisNumber = apiResponse.Data.Document.ChassisNumber,
                BrandText = apiResponse.Data.Document.BrandText,
                ModelText = apiResponse.Data.Document.ModelText,
                ResidenceApplication = apiResponse.Data.Document.ResidenceApplication,
                IsPhotoBoothIntegrationActive = apiResponsePreApplicationForm.Data.IsPhotoBoothIntegrationActive,
                CargoProviderId = apiResponsePreApplicationForm.Data.CargoProviderId,
                IsCargoIntegrationActive = apiResponsePreApplicationForm.Data.IsCargoIntegrationActive,
                ShowCityDropdown = apiResponsePreApplicationForm.Data.ShowCityDropdown,
                VisaHistories = apiResponse.Data.VisaHistories.Select(p => new AddUpdateApplicationViewModel.VisaHistory
                {
                    CountryId = p.CountryId,
                    FromDate = p.FromDate,
                    UntilDate = p.UntilDate,
                    IsUsed = p.IsUsed,
                    NumberOfEntryId = p.NumberOfEntryId,
                    VisaIsUsedYear = p.VisaIsUsedYear,
                    OldVisaDecisionId = p.OldVisaDecisionId
                }).ToList(),
            };

            if (MemoryCache.TryGetValue(CacheKeys.BranchCache, out object value))
            {
                var cacheItem = (BranchesApiResponse)value;
                var branch = cacheItem.Branches.FirstOrDefault(p => p.Id == UserSession.BranchId.Value);
                if (branch != null && branch.Country != null)
                {
                    viewModel.CountryCallingCode = branch.Country.CallingCode;

                    if (!string.IsNullOrEmpty(viewModel.PhoneNumber1) &&
                        !string.IsNullOrEmpty(viewModel.CountryCallingCode) &&
                        viewModel.PhoneNumber1.StartsWith(viewModel.CountryCallingCode))
                    {
                        viewModel.PhoneNumber1 = viewModel.PhoneNumber1.Substring(viewModel.CountryCallingCode.Length);
                    }
                    if (!string.IsNullOrEmpty(viewModel.PhoneNumber2) &&
                        !string.IsNullOrEmpty(viewModel.CountryCallingCode) &&
                        viewModel.PhoneNumber2.StartsWith(viewModel.CountryCallingCode))
                    {
                        viewModel.PhoneNumber2 = viewModel.PhoneNumber2.Substring(viewModel.CountryCallingCode.Length);
                    }
                }
            }

            return Json(new ResultModel { Data = viewModel, Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
        }

        [HttpPost]
		public ActionResult ExcelExport(string contentType, string base64, string fileName)
		{
			var fileContents = Convert.FromBase64String(base64);

			return File(fileContents, contentType, fileName);
		}

		public async Task<IActionResult> Completed(ApplicationCompletedViewModel viewModel)
		{
			if (string.IsNullOrEmpty(viewModel.EncryptedBranchApplicationCountryId) ||
				string.IsNullOrEmpty(viewModel.EncryptedApplicationId) ||
				string.IsNullOrEmpty(viewModel.EncryptedApplicantTypeId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			try
			{
				if (viewModel.IsUpdate) //cargo Check
				{
					var applicationId = viewModel.EncryptedApplicationId.ToDecrypt();

					var apiCargoShipmentResponse = await PortalHttpClientHelper
						.GetAsync<ApiResponse<GetCargoShipmentResponse>>
						(ApiMethodName.Appointment.GetCargoShipment + applicationId, AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
						.ConfigureAwait(false);

					if (!await apiCargoShipmentResponse.Validate(out ResultModel cargoShipmentResult).ConfigureAwait(false))
					{
						TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
						return RedirectToAction("List", "Application", new { Area = "Appointment" });
					}

					if (apiCargoShipmentResponse.Data.HasCargoBefore)
						viewModel.ShowCargoReprintMessage = !apiCargoShipmentResponse.Data.IsExist;
				}

				viewModel.ApplicationCountCompleted = false;

				if ((viewModel.EncryptedApplicantTypeId.ToDecryptInt() == (int)ApplicantType.Family || viewModel.EncryptedApplicantTypeId.ToDecryptInt() == (int)ApplicantType.Group) && viewModel.IsPrinterIntegrationActive)
				{
					var isLastApplication = await PortalHttpClientHelper
						.GetAsync<ApiResponse<LastApplicationApiResponse>>
						(ApiMethodName.Appointment.IsLastApplication + viewModel.EncryptedApplicationId.ToDecrypt(), AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
						.ConfigureAwait(false);

					if (isLastApplication == null)
						return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

					viewModel.ApplicationCountCompleted = isLastApplication.Data.Result;
				}
			}
			catch (Exception)
			{
				//
			}


			return View(viewModel);
		}

		public async Task<IActionResult> Detail(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationApiResponse>>
				(ApiMethodName.Appointment.GetApplication + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			var visaCategories = GetCachedVisaCategories();

			//if (UserSession.BranchId.HasValue && UserSession.BranchId.Value != apiResponse.Data.BranchId)
			//{
			//    TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
			//    return RedirectToAction("List", "Application", new { Area = "Appointment" });
			//}

			if (!MemoryCache.TryGetValue(CacheKeys.BranchCache, out BranchesApiResponse cacheItemBranch))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurredInApplicationStatusCheck });

			var moduleCountryId = cacheItemBranch.Branches.FirstOrDefault(p => p.Id == apiResponse.Data.BranchId).Country.Id;

			var entryFormAvailableLanguages = new List<EntryFormLanguage>();
			if (moduleCountryId == 3) // algeria
			{
				entryFormAvailableLanguages.Add(EntryFormLanguage.English);
				entryFormAvailableLanguages.Add(EntryFormLanguage.Arabic);
				entryFormAvailableLanguages.Add(EntryFormLanguage.French);
			}
			else if (moduleCountryId == 80 || moduleCountryId == 185 || moduleCountryId == 152 || moduleCountryId == 93 || moduleCountryId == 100)
			{
				entryFormAvailableLanguages.Add(EntryFormLanguage.English);
				entryFormAvailableLanguages.Add(EntryFormLanguage.Arabic);
			}
			else
			{
				entryFormAvailableLanguages.Add(EntryFormLanguage.English);
			}
			var viewModel = new ApplicationViewModel
			{
				EncryptedId = apiResponse.Data.Id.ToEncrypt(),
				BranchApplicationCountryId = apiResponse.Data.BranchApplicationCountryId,
				CountryId = apiResponse.Data.CountryId,
				CountryName = apiResponse.Data.CountryName,
				ApplicantTypeId = apiResponse.Data.ApplicantTypeId,
				ApplicationTypeId = apiResponse.Data.ApplicationTypeId,
				PassportNumber = apiResponse.Data.PassportNumber,
				PassportExpireDate = apiResponse.Data.PassportExpireDate,
				ApplicationPassportStatusId = apiResponse.Data.ApplicationPassportStatusId,
				AgencyId = apiResponse.Data.AgencyId,
				AgencyName = apiResponse.Data.AgencyName,
				IsAllowDeniedPassport = apiResponse.Data.IsAllowDeniedPassport,
				Reason = apiResponse.Data.Reason,
				TitleId = apiResponse.Data.TitleId,
				Name = apiResponse.Data.Name,
				Surname = apiResponse.Data.Surname,
				BirthDate = apiResponse.Data.BirthDate,
				GenderId = apiResponse.Data.GenderId,
				MaritalStatusId = apiResponse.Data.MaritalStatusId,
				NationalityId = apiResponse.Data.NationalityId,
				Nationality = apiResponse.Data.Nationality,
				MaidenName = apiResponse.Data.MaidenName,
				FatherName = apiResponse.Data.FatherName,
				MotherName = apiResponse.Data.MotherName,
				Email = apiResponse.Data.Email,
				PhoneNumber1 = apiResponse.Data.PhoneNumber1,
				PhoneNumber2 = apiResponse.Data.PhoneNumber2,
				Address = apiResponse.Data.Address,
				Note = apiResponse.Data.Note,
				RelationalApplicationId = apiResponse.Data.RelationalApplicationId,
				ApplicationTime = apiResponse.Data.ApplicationTime,
				CreatedBy = apiResponse.Data.CreatedBy,
				CreatedByNameSurname = apiResponse.Data.CreatedByNameSurname,
				StatusId = apiResponse.Data.StatusId,
				IsSameBranch = !(UserSession.BranchId.HasValue && UserSession.BranchId.Value != apiResponse.Data.BranchId),
				EntryFormAvailableLanguages = entryFormAvailableLanguages,

				Insurance = new ApplicationViewModel.ApplicationInsurance()
				{
					Number = apiResponse.Data.Insurance?.Number[0]
				},

				Document = new ApplicationViewModel.ApplicationDocument
				{
					TotalYearInCountry = apiResponse.Data.Document.TotalYearInCountry,
					ReimbursementTypeId = apiResponse.Data.Document.ReimbursementTypeId,
					ReimbursementSponsorDetail = apiResponse.Data.Document.ReimbursementSponsorDetail,
					Job = apiResponse.Data.Document.Job,
					OccupationId = apiResponse.Data.Document.OccupationId,
					CompanyName = apiResponse.Data.Document.CompanyName,
					TotalYearInCompany = apiResponse.Data.Document.TotalYearInCompany,
					MonthlySalary = apiResponse.Data.Document.MonthlySalary,
					MonthlySalaryCurrencyId = apiResponse.Data.Document.MonthlySalaryCurrencyId,
					HasBankAccount = apiResponse.Data.Document.HasBankAccount,
					BankBalance = apiResponse.Data.Document.BankBalance,
					BankBalanceCurrencyId = apiResponse.Data.Document.BankBalanceCurrencyId,
					HasDeed = apiResponse.Data.Document.HasDeed,
					VisaCategoryId = apiResponse.Data.Document.VisaCategoryId,
					VisaCategory = GetVisaCategoryNameFromId(apiResponse.Data.Document.VisaCategoryId, visaCategories),
					HasEntryBan = apiResponse.Data.Document.HasEntryBan,
					EntryDate = apiResponse.Data.Document.EntryDate,
					ExitDate = apiResponse.Data.Document.ExitDate,
					CityName = apiResponse.Data.Document.CityName,
					AccomodationDetail = apiResponse.Data.Document.AccomodationDetail,
					HasRelativeAbroad = apiResponse.Data.Document.HasRelativeAbroad,
					RelativeLocation = apiResponse.Data.Document.RelativeLocation,
					PersonTravelWith = apiResponse.Data.Document.PersonTravelWith,
					PersonTravelWithHasVisa = apiResponse.Data.Document.PersonTravelWithHasVisa,
					ProvidedWithHasRelatedInsurance = apiResponse.Data.Document.ProvidedWithHasRelatedInsurance,
					ApplicationTogether = apiResponse.Data.Document.ApplicationTogether,
					HasPersonVisitedTurkeyBefore = apiResponse.Data.Document.HasPersonVisitedTurkeyBefore
				},

				VisaHistories = apiResponse.Data.VisaHistories.Select(p => new ApplicationViewModel.ApplicationVisaHistory
				{
					CountryId = p.CountryId,
					CountryName = p.CountryName,
					FromDate = p.FromDate,
					UntilDate = p.UntilDate,
					IsUsed = p.IsUsed,
					NumberOfEntryId = p.NumberOfEntryId,
					VisaIsUsedYear = p.VisaIsUsedYear
				}).ToList(),

				ExtraFees = apiResponse.Data.ExtraFees.Select(p => new ApplicationViewModel.ApplicationExtraFee
				{
					TypeId = p.TypeId,
					ExtraFeeName = p.ExtraFeeName,
					Price = p.Price,
					Tax = p.Tax,
					CurrencyId = p.CurrencyId,
					Quantity = p.Quantity,
					Category = p.Category
				}).ToList(),

				StatusHistories = apiResponse.Data.StatusHistories.Select(p => new ApplicationViewModel.ApplicationStatusHistory()
				{
					Order = p.Order,
					Status = p.Status,
					NameSurname = p.NameSurname,
					ApplicationCreatedAt = apiResponse.Data.ApplicationTime,
					StatusDate = p.StatusDate
				}).ToList(),

				ApplicationHistories = apiResponse.Data.ApplicationHistories.Select(p => new ApplicationViewModel.ApplicationHistory
				{
					ApplicationHistoryId = p.ApplicationHistoryId,
					PropertyName = p.PropertyName,
					PreviousValue = p.PreviousValue,
					CurrentValue = p.CurrentValue,
					CreatedBy = p.CreatedBy,
					CreatedByNameSurname = p.CreatedByNameSurname,
					CreatedAt = p.CreatedAt
				}).ToList()
			};

            foreach (var item in viewModel.ApplicationHistories)
			{
				item.PropertyNameDetail = GetApplicationHistoryPropertyDetail(item.PropertyName);
				item.PreviousValueDetail = GetApplicationHistoryValueDetail(item.PropertyName, item.PreviousValue, visaCategories);
				item.CurrentValueDetail = GetApplicationHistoryValueDetail(item.PropertyName, item.CurrentValue, visaCategories);
			}

			return View(viewModel);
		}

		public async Task<IActionResult> DetailTurkmenistan(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationSummaryApiResponse>>
				(ApiMethodName.Appointment.GetSanitizedApplicationSummary + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			if (!MemoryCache.TryGetValue(CacheKeys.BranchCache, out BranchesApiResponse cacheItemBranch))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurredInApplicationStatusCheck });

			var moduleCountryId = cacheItemBranch.Branches.FirstOrDefault(p => p.Id == apiResponse.Data.BranchId).Country.Id;

			var entryFormLanguages = new List<EntryFormLanguage>();
			entryFormLanguages.Add(EntryFormLanguage.English);
			entryFormLanguages.Add(EntryFormLanguage.Turkish);
			entryFormLanguages.Add(EntryFormLanguage.Russian);

			var viewModel = new ApplicationViewModel
			{
				BranchCountryIso3 = apiResponse.Data.BranchCountryIso3,
				EncryptedId = apiResponse.Data.Id.ToEncrypt(),
				BranchApplicationCountryId = apiResponse.Data.BranchApplicationCountryId,
				CountryId = apiResponse.Data.CountryId,
				CountryName = apiResponse.Data.CountryName,
				ApplicantTypeId = apiResponse.Data.ApplicantTypeId,
				ApplicationTypeId = apiResponse.Data.ApplicationTypeId,
				PassportNumber = apiResponse.Data.PassportNumber,
				PassportExpireDate = apiResponse.Data.PassportExpireDate,
				ApplicationPassportStatusId = apiResponse.Data.ApplicationPassportStatusId,
				AgencyId = apiResponse.Data.AgencyId,
				AgencyName = apiResponse.Data.AgencyName,
				IsAllowDeniedPassport = apiResponse.Data.IsAllowDeniedPassport,
				Reason = apiResponse.Data.Reason,
				TitleId = apiResponse.Data.TitleId,
				Name = apiResponse.Data.Name,
				Surname = apiResponse.Data.Surname,
				BirthDate = apiResponse.Data.BirthDate,
				GenderId = apiResponse.Data.GenderId,
				MaritalStatusId = apiResponse.Data.MaritalStatusId,
				NationalityId = apiResponse.Data.NationalityId,
				Nationality = apiResponse.Data.Nationality,
				MaidenName = apiResponse.Data.MaidenName,
				FatherName = apiResponse.Data.FatherName,
				MotherName = apiResponse.Data.MotherName,
				Email = apiResponse.Data.Email,
				PhoneNumber1 = apiResponse.Data.PhoneNumber1,
				PhoneNumber2 = apiResponse.Data.PhoneNumber2,
				Address = apiResponse.Data.Address,
				Note = apiResponse.Data.Note,
				RelationalApplicationId = apiResponse.Data.RelationalApplicationId,
				ApplicationTime = apiResponse.Data.ApplicationTime,
				CreatedBy = apiResponse.Data.CreatedBy,
				CreatedByNameSurname = apiResponse.Data.CreatedByNameSurname,
				StatusId = apiResponse.Data.StatusId,
				IsRelationalApplicant = apiResponse.Data.IsRelationalApplicant,
				IsSameBranch = !(UserSession.BranchId.HasValue && UserSession.BranchId.Value != apiResponse.Data.BranchId),
				EntryFormAvailableLanguages = entryFormLanguages,

				Insurance = new ApplicationViewModel.ApplicationInsurance()
				{
					Number = apiResponse.Data.Insurance.Number
				},

				ExtraFees = apiResponse.Data.ExtraFees.Select(p => new ApplicationViewModel.ApplicationExtraFee
				{
					TypeId = p.TypeId,
					ExtraFeeName = p.ExtraFeeName,
					Price = p.Price,
					Tax = p.Tax,
					CurrencyId = p.CurrencyId,
					Quantity = p.Quantity,
					Category = p.Category
				}).ToList()
			};

			return View(viewModel);
		}

		public async Task<IActionResult> GetApplicationDetailPartialView(string type, string encryptedId, string BranchCountryIso3)
		{
			switch (type)
			{
				case "applicationDocument":
					return await DetailDocumentSummary(encryptedId);
				case "applicationStatusHistories":
					return await DetailStatusHistory(encryptedId);
				case "applicationCargoStatusHistories":
					return await DetailCargoStatusHistory(encryptedId);
				case "applicationHistory":
					return await DetailApplicationHistory(encryptedId);
				case "applicationSurveyDubai":
					return await DetailSurveyDubai(encryptedId, BranchCountryIso3);
				case "applicationSmsHistory":
					return await DetailApplicationSmsHistory(encryptedId);
				case "applicationEmailHistory":
					return await DetailApplicationEmailHistory(encryptedId);
				case "applicationInquiry":
					return await DetailApplicationInquiry(encryptedId);
				default:
					return null;
			}
		}

		[HttpPost]
		public async Task<IActionResult> ResendSms(ResendViewModel history)
		{
			var resendApplicationSmsApiRequest = new ResendApplicationSmsApiRequest()
			{
				SendNotification = AppSettings.Notification,
				BranchId = history.BranchId,
				ApplicationStatusId = history.ApplicationStatusId,
				ApplicationId = history.ApplicationId,
				IsApproved = history.IsApproved,
				IsRejected = history.IsRejected,
				BranchCountryId = history.BranchCountryId,
				SmsId = history.SmsId,
				UserId = UserSession.UserId,
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
					(resendApplicationSmsApiRequest, ApiMethodName.Appointment.ResendApplicationSms, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel addApplicationVisaDecisionResult).ConfigureAwait(false))
				return Json(addApplicationVisaDecisionResult);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		public async Task<IActionResult> DetailApplicationSmsHistory(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationSmsHistoryApiResponse>>
				(ApiMethodName.Appointment.GetApplicationSmsHistory + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var viewModel = new ApplicationSmsHistoryViewModel();

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return PartialView("_ApplicationSmsHistory", viewModel);

			var hasResendRole = false;

			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object value))
			{
				var roleActions = (RoleActionApiResponse)value;

				hasResendRole = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
					.Any(p => p.RoleActions.Any(q => q.Action.Method == "IsAuthorizedForResendSms" && q.Action.IsActive));
			}

			var order = 0;

			viewModel.ApplicationSmsHistories = apiResponse.Data.ApplicationSmsHistories.Select(r =>
				new ApplicationSmsHistoryViewModel.ApplicationSmsHistory
				{
					Order = order++,
					ApplicationStatus = r.IsApproved is true ? SiteResources.ApproveSms :
						r.IsRejected is true ? SiteResources.RejectionSms : r.ApplicationStatus,
					ApplicationStatusId = r.ApplicationStatusId,
					ApplicationId = r.ApplicationId,
					ApplicationSmsHistoryId = r.ApplicationSmsHistoryId,
					SendDate = r.CreatedAt,
					SendStatus = true, // burayı tartış dışarıdan alabilir bu bilgiyi,
					AvailableForResend = r.IsAvailableForResend && hasResendRole,
					IsApproved = r.IsApproved,
					IsRejected = r.IsRejected,
					IsResended = r.ParentId != null
				}).ToList();

			var orderRejection = 0;

			viewModel.RejectionSmsHistories = apiResponse.Data.RejectionSmsHistories.Select(s => new ApplicationSmsHistoryViewModel.RejectionSmsHistory()
			{
				Order = orderRejection++,
				CodeSendDate = s.CodeSendDate.ToString("dd/MM/yyyy HH:mm"),
				ProcessBy = s.ProcessBy,
				Status = s.Status,
				RejectionApproveCompletedDate = s.RejectionApproveCompletedDate.HasValue ? s.RejectionApproveCompletedDate.Value.ToString("dd/MM/yyyy HH:mm") : "-",
				ResendCount = s.ResendCount
			}).ToList();

			viewModel.BranchId = apiResponse.Data.BranchId;
			viewModel.BranchCountryId = apiResponse.Data.BranchCountryId;

			return PartialView("_ApplicationSmsHistory", viewModel);
		}

		public async Task<IActionResult> DetailApplicationEmailHistory(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationEmailHistoryApiResponse>>
				(ApiMethodName.Appointment.GetApplicationEmailHistory + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var viewModel = new ApplicationEmailHistoryViewModel();

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return PartialView("_ApplicationEmailHistory", viewModel);

			var order = 0;

			viewModel.ApplicationEmailHistories = apiResponse.Data.ApplicationEmailHistories.Select(r =>
				new ApplicationEmailHistoryViewModel.ApplicationEmailHistory
				{
					Order = order++,
					ApplicationStatus = r.IsApproved is true ? SiteResources.ApproveEmail :
						r.IsRejected is true ? SiteResources.RejectionEmail : r.ApplicationStatus,
					ApplicationStatusId = r.ApplicationStatusId,
					ApplicationId = r.ApplicationId,
					ApplicationEmailHistoryId = r.ApplicationEmailHistoryId,
					SendDate = r.CreatedAt,
					SendStatus = true,
					IsApproved = r.IsApproved,
					IsRejected = r.IsRejected,
				}).ToList();

			var orderRejection = 0;

			viewModel.RejectionEmailHistories = apiResponse.Data.RejectionEmailHistories.Select(s => new ApplicationEmailHistoryViewModel.RejectionEmailHistory()
			{
				Order = orderRejection++,
				CodeSendDate = s.CodeSendDate.ToString("dd/MM/yyyy HH:mm"),
				ProcessBy = s.ProcessBy,
				Status = s.Status,
				RejectionApproveCompletedDate = s.RejectionApproveCompletedDate.HasValue ? s.RejectionApproveCompletedDate.Value.ToString("dd/MM/yyyy HH:mm") : "-",
				ResendCount = s.ResendCount
			}).ToList();

			viewModel.BranchId = apiResponse.Data.BranchId;
			viewModel.BranchCountryId = apiResponse.Data.BranchCountryId;

			return PartialView("_ApplicationEmailHistory", viewModel);
		}

		public async Task<IActionResult> DetailApplicationSummary(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification",
					new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationSummaryApiResponse>>
				(ApiMethodName.Appointment.GetSanitizedApplicationSummary + id, AppSettings.PortalGatewayApiUrl,
					PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
			{
				TempData.Put("Notification",
					new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			if (!MemoryCache.TryGetValue(CacheKeys.BranchCache, out BranchesApiResponse cacheItemBranch))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurredInApplicationStatusCheck });

			var moduleCountryId = cacheItemBranch.Branches.FirstOrDefault(p => p.Id == apiResponse.Data.BranchId)
				.Country.Id;

			var entryFormAvailableLanguages = GetEntryFormLanguage(moduleCountryId);

			var explicitConsentTextLanguages = GetExplicitConsentTextLanguage(moduleCountryId);

			var hasAuthorizedRoleForWorkPermitReferanceNumber = false;
			var hasAuthorizedRoleForERASMUSReferanceNumber = false;
			var hasAuthorizedRoleForIAESTEReferanceNumber = false;
			var hasAuthorizedRoleForAIESECReferanceNumber = false;
			if (apiResponse.Data.VisaCategoryId == (int)VisaCategoryTypeEnum.WorkPermit || apiResponse.Data.VisaCategoryId == (int)VisaCategoryTypeEnum.ERASMUS || apiResponse.Data.VisaCategoryId == (int)VisaCategoryTypeEnum.IAESTE || apiResponse.Data.VisaCategoryId == (int)VisaCategoryTypeEnum.AIESEC)
			{
				if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache,
					    out object value)) //buralar detail açıldığında girilen metoda koyulucak
				{
					var roleActions = (RoleActionApiResponse)value;

					if (apiResponse.Data.VisaCategoryId == (int)VisaCategoryTypeEnum.WorkPermit)
						hasAuthorizedRoleForWorkPermitReferanceNumber = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id)).Any(p => p.RoleActions.Any(q => q.Action.Method == "IsAuthorizedForWorkPermitReferanceNumber" && q.Action.IsActive));
					else if (apiResponse.Data.VisaCategoryId == (int)VisaCategoryTypeEnum.ERASMUS)
						hasAuthorizedRoleForERASMUSReferanceNumber = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id)).Any(p => p.RoleActions.Any(q => q.Action.Method == "IsAuthorizedForERASMUSReferanceNumber" && q.Action.IsActive));
					else if (apiResponse.Data.VisaCategoryId == (int)VisaCategoryTypeEnum.IAESTE)
						hasAuthorizedRoleForIAESTEReferanceNumber = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id)).Any(p => p.RoleActions.Any(q => q.Action.Method == "IsAuthorizedForIAESTEReferanceNumber" && q.Action.IsActive));
					else if (apiResponse.Data.VisaCategoryId == (int)VisaCategoryTypeEnum.AIESEC)
						hasAuthorizedRoleForAIESECReferanceNumber = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id)).Any(p => p.RoleActions.Any(q => q.Action.Method == "IsAuthorizedForAIESECReferanceNumber" && q.Action.IsActive));
				}
			}

			var hasAuthorizedRoleForUpdatePolicy = false;

			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object value2))
			{
				var roleActions = (RoleActionApiResponse)value2;

				hasAuthorizedRoleForUpdatePolicy = roleActions.RoleActionSites
					.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
					.Any(p => p.RoleActions.Any(q => q.Action.Method == "CancelInsurance" && q.Action.IsActive));
			}

			var hasAuthorizedRoleForTsCreatePolicy = false;

			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object value3))
			{
				var roleActions = (RoleActionApiResponse)value3;

				hasAuthorizedRoleForTsCreatePolicy = roleActions.RoleActionSites
					.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
					.Any(p => p.RoleActions.Any(q => q.Action.Method == "TsCreateInsurance" && q.Action.IsActive));
			}

			var hasAuthorizedRoleForWorkPermitUpdatePolicy = false;

			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object value4))
			{
				var roleActions = (RoleActionApiResponse)value4;

				hasAuthorizedRoleForWorkPermitUpdatePolicy = roleActions.RoleActionSites
					.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
					.Any(p => p.RoleActions.Any(q => q.Action.Method == "UpdateWorkPermitPolicy" && q.Action.IsActive));
			}

            var hasAuthorizedRoleForICRWithoutCreatingInsurancePolicy = false;
            var hasAuthorizedRoleForSendingSms = false;
            var hasAuthorizedRoleForSeeingSmsLink = false;

            if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object value5))
            {
                var roleActions = (RoleActionApiResponse)value5;

                hasAuthorizedRoleForICRWithoutCreatingInsurancePolicy = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                    .Any(p => p.RoleActions.Any(q => q.Action.Method == "ICRWithoutCreatingInsurance" && q.Action.IsActive));

                hasAuthorizedRoleForSendingSms = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                    .Any(p => p.RoleActions.Any(q => q.Action.Method == "RelatedInsuranceSendingSmsRoleCheck" && q.Action.IsActive));

                hasAuthorizedRoleForSeeingSmsLink = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                    .Any(p => p.RoleActions.Any(q => q.Action.Method == "RelatedInsuranceShowSmsLinkRoleCheck" && q.Action.IsActive));
            }

            List<int> searchOfficialRoleIds = new List<int>() { 1, 2, 5, 7, 8, 13 }; //

			var apiResponse2 = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationRelatedServicesApiResponse>>
				(ApiMethodName.Appointment.GetRelatedServicesApplications + id,
					AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse2.Validate(out ResultModel result2).ConfigureAwait(false))
				return Json(null);

			Boolean releatedInsuranceForPolicyCheck = false;

			if (apiResponse.Data.Insurance.PolicyCount == apiResponse2.Data.RelatedServices.Count() &&
			    apiResponse2.Data.RelatedServices.Count() > 0)
				releatedInsuranceForPolicyCheck = true;
			
			Boolean releatedInsuranceForSmsHistoryCheck = false;

			if (releatedInsuranceForPolicyCheck)
			{
				var smsHistoryApiResponse = await PortalHttpClientHelper
					.GetAsync<ApiResponse<ApplicationSmsHistoryApiResponse>>
					(ApiMethodName.Appointment.GetApplicationSmsHistory + id, AppSettings.PortalGatewayApiUrl,
						PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (smsHistoryApiResponse.Data.ApplicationSmsHistories.Where(s => s.IsRelatedInsurance && !s.IsDeleted).Count() > 1)
				{
                    if(hasAuthorizedRoleForSendingSms)
                    {
                        releatedInsuranceForSmsHistoryCheck = false;
                    }
                    else
                    {
                        releatedInsuranceForSmsHistoryCheck = true;
                    }
                }
			}
		

			var viewModel = new ApplicationViewModel
			{
				BranchCountryIso3 = apiResponse.Data.BranchCountryIso3,
				EncryptedId = apiResponse.Data.Id.ToEncrypt(),
				BranchApplicationCountryId = apiResponse.Data.BranchApplicationCountryId,
				BranchId = apiResponse.Data.BranchId,
                ShowMontageVisaType = apiResponse.Data.ShowMontageVisaType,
				IsCargoIntegrationActive = apiResponse.Data.IsCargoIntegrationActive,
				IsInquiryActive = apiResponse.Data.IsInquiryActive,
				ShowCityDropdown = apiResponse.Data.ShowCityDropdown,
				CountryId = apiResponse.Data.CountryId,
				CountryName = apiResponse.Data.CountryName,
				ApplicantTypeId = apiResponse.Data.ApplicantTypeId,
				ApplicationTypeId = apiResponse.Data.ApplicationTypeId,
				PassportNumber = apiResponse.Data.PassportNumber,
				PassportExpireDate = apiResponse.Data.PassportExpireDate,
				ApplicationPassportStatusId = apiResponse.Data.ApplicationPassportStatusId,
				AgencyId = apiResponse.Data.AgencyId,
				AgencyName = apiResponse.Data.AgencyName,
				IsAllowDeniedPassport = apiResponse.Data.IsAllowDeniedPassport,
				Reason = apiResponse.Data.Reason,
				TitleId = apiResponse.Data.TitleId,
				Name = apiResponse.Data.Name,
				Surname = apiResponse.Data.Surname,
				BirthDate = apiResponse.Data.BirthDate,
				GenderId = apiResponse.Data.GenderId,
				MaritalStatusId = apiResponse.Data.MaritalStatusId,
				NationalityId = apiResponse.Data.NationalityId,
				Nationality = apiResponse.Data.Nationality,
				ResidenceNumber = apiResponse.Data.ResidenceNumber,
				MaidenName = apiResponse.Data.MaidenName,
				FatherName = apiResponse.Data.FatherName,
				MotherName = apiResponse.Data.MotherName,
				Email = apiResponse.Data.Email,
				PhoneNumber1 = apiResponse.Data.PhoneNumber1,
				PhoneNumber2 = apiResponse.Data.PhoneNumber2,
				Address = apiResponse.Data.Address,
				ForeignCityId = apiResponse.Data.ForeignCityId,
				CargoProviderId = apiResponse.Data.CargoProviderId,
				City = apiResponse.Data.City,
				PostalCode = apiResponse.Data.PostalCode,
				NameOfSecondContactPerson = apiResponse.Data.NameOfSecondContactPerson,
				AreaId = apiResponse.Data.AreaId,
				AreaName = apiResponse.Data.AreaName,
				GovernorateId = apiResponse.Data.GovernorateId,
				GovernorateName = apiResponse.Data.GovernorateName,
				IsPrintAllIntegrationActive = apiResponse.Data.IsPrintAllIntegrationActive,
				IsPhotoBoothIntegrationActive = apiResponse.Data.IsPhotoBoothIntegrationActive,
				IsDigitalSignatureIntegrationActive = apiResponse.Data.IsDigitalSignatureIntegrationActive,
				IsCargoIntegrationSelected = apiResponse.Data.IsCargoIntegrationSelected,
				ShowEsimQrCode = apiResponse.Data.ShowEsimQrCode,
				Note = apiResponse.Data.Note,
				RelationalApplicationId = apiResponse.Data.RelationalApplicationId,
				ApplicationTime = apiResponse.Data.ApplicationTime,
				CreatedBy = apiResponse.Data.CreatedBy,
				CreatedByNameSurname = apiResponse.Data.CreatedByNameSurname,
				StatusId = apiResponse.Data.StatusId,
				IsRelationalApplicant = apiResponse.Data.IsRelationalApplicant,
				IsSameBranch = !(UserSession.BranchId.HasValue &&
								 UserSession.BranchId.Value != apiResponse.Data.BranchId),
				EntryFormAvailableLanguages = entryFormAvailableLanguages,
				ExplicitConsentTextLanguages = explicitConsentTextLanguages,
				IsInsuranceFeeSelected = apiResponse.Data.IsInsuranceFeeSelected,
				VisaCategoryId = apiResponse.Data.VisaCategoryId,
				ReferenceNumberType = apiResponse.Data.VisaCategoryId == (int)VisaCategoryTypeEnum.WorkPermit && hasAuthorizedRoleForWorkPermitReferanceNumber ? EnumResources.WorkPermit
				: (apiResponse.Data.VisaCategoryId == (int)VisaCategoryTypeEnum.ERASMUS && hasAuthorizedRoleForERASMUSReferanceNumber ? EnumResources.ERASMUS
				: (apiResponse.Data.VisaCategoryId == (int)VisaCategoryTypeEnum.IAESTE && hasAuthorizedRoleForIAESTEReferanceNumber ? EnumResources.IAESTE
				: (apiResponse.Data.VisaCategoryId == (int)VisaCategoryTypeEnum.AIESEC && hasAuthorizedRoleForAIESECReferanceNumber ? EnumResources.AIESEC : String.Empty))),
				AvailableToShowReferenceNumber = apiResponse.Data.AvailableToShowReferenceNumber,
				OfficialCheck = UserSession.RoleIds.Any(q => searchOfficialRoleIds.Contains(q)),
				IsUserSysdmin = apiResponse.Data.IsUserSysdmin,
				SapGroupId = apiResponse.Data.SapGroupId,
				KsaTaxSendCheck = apiResponse.Data.KsaTaxSendCheck,
				IsCargoIntegrationExists = apiResponse.Data.IsCargoIntegrationExists,
				IsPrinterIntegrationActive = apiResponse.Data.IsPrinterIntegrationActive,
				Insurance = new ApplicationViewModel.ApplicationInsurance()
				{
					Number = apiResponse.Data.Insurance.Number,
					ReleatedPolicyNumber = apiResponse.Data.Insurance.ReleatedInsuranceNumber,
					UpdatePolicy = apiResponse.Data.Insurance.UpdatePolicy,
					UpdateReleatedPolicy = apiResponse.Data.Insurance.UpdateReleatedPolicy,
					UpdateWorkPermitPolicy = apiResponse.Data.Insurance.UpdateWorkPermitPolicy,
					RoleCheckUpdatePolicy = hasAuthorizedRoleForUpdatePolicy,
					RoleChecTsCreatePolicy = hasAuthorizedRoleForTsCreatePolicy,
					RoleChecWorkPermitCreatePolicy = hasAuthorizedRoleForWorkPermitUpdatePolicy,
					RoleCheckICRWithoutCreatingInsurancePolicy = hasAuthorizedRoleForICRWithoutCreatingInsurancePolicy,
                    RoleCheckShowRelatedInsuranceSmsLink = hasAuthorizedRoleForSeeingSmsLink,
                    CanUpdateMontagePolicy = apiResponse.Data.Insurance.CanUpdateMontagePolicy
                },
				Document = new ApplicationViewModel.ApplicationDocument()
				{
                    AdditionalServiceTypeId = apiResponse.Data.Document.AdditionalServiceTypeId,
                    ReleatedInsuranceCheck = apiResponse2.Data.RelatedServices.Count() > 0,
                    ReleatedInsuranceForPolicyCheck = releatedInsuranceForPolicyCheck,
                    ReleatedInsuranceForSmsHistoryCheck = releatedInsuranceForSmsHistoryCheck
                },
				ExtraFees = apiResponse.Data.ExtraFees.Select(p => new ApplicationViewModel.ApplicationExtraFee
				{
					TypeId = p.TypeId,
					ExtraFeeName = p.ExtraFeeName,
					Price = p.Price,
					Tax = p.Tax,
					CurrencyId = p.CurrencyId,
					Quantity = p.Quantity,
					Category = p.Category,
					PaymentMethod = p.PaymentMethod,
					PolicyPeriod = p.PolicyPeriod
				}).ToList()
			};

			viewModel.ApplicationCountCompleted = false;
			if ((viewModel.ApplicantTypeId == (int)ApplicantType.Family || viewModel.ApplicantTypeId == (int)ApplicantType.Group) && apiResponse.Data.IsPrinterIntegrationActive)
			{
				var isLastApplication = await PortalHttpClientHelper
					.GetAsync<ApiResponse<LastApplicationApiResponse>>
					(ApiMethodName.Appointment.IsLastApplication + apiResponse.Data.Id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (isLastApplication == null)
					return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

				viewModel.ApplicationCountCompleted = isLastApplication.Data.Result;
			}

			return View("Detail", viewModel);
		}

		[HttpGet]
		public async Task<IActionResult> ForeignHealthInsuranceCheck(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ForeignHealthInsuranceCheckApiResponse>>
				(ApiMethodName.Appointment.ForeignHealthInsuranceCheck + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(new ResultModel { Message = $"{SiteResources.NotFound}", ResultType = ResultType.Info });

			if (!MemoryCache.TryGetValue(CacheKeys.BranchCache, out BranchesApiResponse cacheItemBranch))
				return Json(new ResultModel { ResultType = ResultType.Danger, Message = SiteResources.ErrorOccurredInApplicationStatusCheck });

			var viewModel = new YssCheckViewModel
			{
				EncryptedId = apiResponse.Data.ApplicationNumber.ToEncrypt(),
				Name = apiResponse.Data.Name,
				Surname = apiResponse.Data.Surname,
				BirthDate = apiResponse.Data.BirthDate.ToString("dd/MM/yyy"),
				Nationality = apiResponse.Data.Nationality,
				PassportNumber = apiResponse.Data.PassportNumber,
				TypeOfInsurancePolicy = apiResponse.Data.ExtraFees?.FirstOrDefault(r => r.Category == (int)ExtraFeeCategoryType.YSS)?.ExtraFeeName,
				StartDate = apiResponse.Data.StartDate,
				EndDate = apiResponse.Data.EndDate,
				Status = apiResponse.Data.Status
			};

			return PartialView("_YssCheck", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> AddNonApplicationYss(AddNonApplicationYssViewModel viewModel)
		{
			if (viewModel is null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var apiRequest = new AddNonApplicationYssApiRequest
			{
				EncryptedId = viewModel.EncryptedId,
				StartDate = viewModel.StartDate,
				EndDate = viewModel.EndDate
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
					(apiRequest, ApiMethodName.Appointment.AddNonApplicationYss, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		public List<EntryFormLanguage> GetEntryFormLanguage(int moduleCountry)
		{

			var entryFormLanguage = new List<EntryFormLanguage>();

			if (moduleCountry == 3) // algeria
			{
				entryFormLanguage.Add(EntryFormLanguage.English);
				entryFormLanguage.Add(EntryFormLanguage.Arabic);
				entryFormLanguage.Add(EntryFormLanguage.French);
			}

			else if (moduleCountry == 80 || moduleCountry == 185 || moduleCountry == 152 || moduleCountry == 93 || moduleCountry == 100)
			{
				entryFormLanguage.Add(EntryFormLanguage.English);
				entryFormLanguage.Add(EntryFormLanguage.Arabic);
			}

			else
			{
				entryFormLanguage.Add(EntryFormLanguage.English);
			}

			return entryFormLanguage;
		}

		#region Digital Signature

		[HttpPost]
		public async Task<IActionResult> PartialDigitalSignature(PartialDigitalSignatureViewModel model)
		{
			var age = DateTime.Now.Year - model.BirthDate.Year;

			if (DateTime.Now.DayOfYear < model.BirthDate.DayOfYear)
				age--;

			var viewModel = new DigitalSignatureViewModel
			{
				EncryptedApplicationId = model.EncryptedApplicationId,
				EncryptedTabletId = UserSession?.EncryptedTabletId ?? string.Empty,
				ApplicantNameAndSurname = model.ApplicantNameAndSurname,
				MotherName = model.MotherName,
				FatherName = model.FatherName,
				UnderEighteenYearsOld = age < 18
			};

			var apiRequest = new PaginatedApplicationFilesApiRequest { ApplicationId = model.EncryptedApplicationId.ToDecryptInt(), IsDigitalSignature = true};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<ApplicationFilesApiResponse>>
					(apiRequest, ApiMethodName.Appointment.GetApplicationFiles, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			var documentsApiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<GetBranchDigitalSignatureDocumentsApiResponse>>
					(ApiMethodName.Management.GetBranchDigitalSignatureDocuments + model.BranchApplicationCountryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await documentsApiResponse.Validate(out ResultModel documentsResult).ConfigureAwait(false))
				return Json(documentsResult);

			viewModel.DigitalSignatureDocuments = documentsApiResponse.Data.DigitalSignatureDocuments
				.Where(w => w.UsesDigitalSignatureDocument)
				.Select(s =>
				new DigitalSignatureDocumentsViewModel()
				{
					DigitalSignatureDocumentId = (byte)s.DigitalSignatureDocumentId,
					DocumentLanguageIds = s.Languages.Select(language => (byte)language).ToList()
				}).ToList();

			var signedDocuments = viewModel.DigitalSignatureDocuments
				.Where(p => apiResponse.Data.ApplicationFiles.Select(q => q.FileTypeId).Contains(p.DigitalSignatureDocumentId)).ToList();

            if (signedDocuments?.Count > 0)
			{
				foreach (var document in signedDocuments)
				{
					viewModel.DigitalSignatureDocuments.Remove(document);
				}
			}

            if (!apiResponse.Data.IsCargoExist)
            {
                var cargoReceiptDocument = viewModel.DigitalSignatureDocuments.Find(s => s.DigitalSignatureDocumentId == (byte)DigitalSignatureDocument.CargoReceipt);

                if (cargoReceiptDocument != null) { viewModel.DigitalSignatureDocuments.Remove(cargoReceiptDocument); }
            }

            if (viewModel.DigitalSignatureDocuments.Count == 0)
				return Json(new ResultModel { Message = SiteResources.ThereIsNoAppropriateDocumentation, ResultType = ResultType.Warning });

			return PartialView("_DigitalSignature", viewModel);
		}

		public async Task<IActionResult> PartialApplicationFiles(string encryptedApplicationId)
		{
			var apiRequest = new PaginatedApplicationFilesApiRequest { ApplicationId = encryptedApplicationId.ToDecryptInt() };

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<ApplicationFilesApiResponse>>
					(apiRequest, ApiMethodName.Appointment.GetApplicationFiles, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			var viewModel = new DigitalSignatureViewModel
			{
				EncryptedApplicationId = encryptedApplicationId,
				DigitalSignatureDocuments = apiResponse.Data.ApplicationFiles.Select(x => new DigitalSignatureDocumentsViewModel
				{
					DigitalSignatureDocumentId = (byte)x.FileTypeId,
					EncryptedDocumentId = x.DocumentId.ToEncrypt()
				}).ToList()
			};

			return PartialView("_PartialApplicationFiles", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> DownloadApplicationFiles(DigitalSignatureViewModel viewModel)
		{
			var documentsApiRequest = new DocumentsApiRequest
			{
				Ids = viewModel.DigitalSignatureDocuments.Select(p => p.EncryptedDocumentId.ToDecryptInt()).ToList(),
				IsFileContentIncluded = true
			};

			var getDocumentsApiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<DocumentsApiResponse>>
					(documentsApiRequest, ApiMethodName.DocumentManagement.GetDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await getDocumentsApiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			var downloadAllFilesViewModel = new DownloadAllFilesViewModel
			{
				ApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
				Documents = getDocumentsApiResponse.Data.Documents.Select(p => new DocumentViewModel
				{
					FileContent = p.FileContent,
					UniqueFileNameWithExtension = p.UniqueFileNameWithExtension
				}).ToList()
			};

			return Json(downloadAllFilesViewModel);
		}

		public IActionResult DownloadAllFiles()
		{
			var viewModel = JsonSerializer.Deserialize<DownloadAllFilesViewModel>(Request.Form["model"].ToString());

			if (viewModel == null)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var zipName = $"{viewModel.ApplicationId}-files.zip";
			using (MemoryStream ms = new MemoryStream())
			{
				using (var zip = new ZipArchive(ms, ZipArchiveMode.Create, true))
				{
					viewModel.Documents.ToList().ForEach(file =>
					{
						var entry = zip.CreateEntry(file.UniqueFileNameWithExtension);
						using (var fileStream = new MemoryStream(file.FileContent))
						using (var entryStream = entry.Open())
						{
							fileStream.CopyTo(entryStream);
						}
					});
				}
				return File(ms.ToArray(), "application/zip", zipName);
			}
		}

		public IActionResult SetTabletIdToSession(string encryptedTabletId)
		{
			try
			{
				var userModel = UserSession;
				userModel.EncryptedTabletId = encryptedTabletId;

				Extensions.SessionExtensions.Set(HttpContext.Session, SessionKeys.UserSession, userModel);

				return Json(new ResultModel
				{
					Message = EnumResources.OperationIsSuccessful,
					ResultType = ResultType.Success
				});
			}
			catch
			{
				return Json(new ResultModel
				{
					Message = EnumResources.InvalidOperation,
					ResultType = ResultType.Danger
				});
			}
		}

		[HttpPost]
		public async Task<IActionResult> SendDigitalSignature(DigitalSignatureViewModel viewModel)
		{
			if (viewModel == null)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			if (viewModel.EncryptedTabletId == null)
				return Json(new ResultModel { Message = SiteResources.PleaseSelectTablet, ResultType = ResultType.Warning });

			var apiRequest = new SendToTabletApiRequest
			{
				ApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
				TabletId = viewModel.EncryptedTabletId.ToDecryptInt(),
				CreatedBy = UserSession.UserId,
				DigitalSignatureDocuments = viewModel.DigitalSignatureDocuments.Select(p => new DigitalSignatureDocumentApiRequest
				{
					DigitalSignatureDocumentId = p.DigitalSignatureDocumentId,
					DocumentLanguageId = p.DocumentLanguageId
				}).ToList(),
				MinorApplicantParentTypeId = viewModel.MinorApplicantParentTypeId,
				MinorApplicantParents = viewModel.MinorApplicantParents.Select(q => new MinorApplicantParentApiRequest
				{
					Name = q.Name
				}).ToList()
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<SendDigitalSignatureApiResponse>>
					(apiRequest, ApiMethodName.DigitalSignature.SendToTablet, AppSettings.PrintData.BaseApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region PrintAll

		public IActionResult PartialPrintAll(string encryptedId, string encryptedMainApplicationId, bool isCargoIntegrationExists, string branchCountryIso3)
		{
			int id = encryptedId.ToDecryptInt();
			int relationalId = encryptedMainApplicationId.ToDecryptInt();

			var isAuthorizedPrintAll = false;
			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object value))
			{
				var roleActions = (RoleActionApiResponse)value;

				isAuthorizedPrintAll = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
					.Any(p => p.RoleActions.Any(q => q.Action.Method == "IsAuthorizedPrintAll" && q.Action.IsActive));
			}

			var viewModel = new ApplicationPrintAllViewModel
			{
				ApplicationId = encryptedId,
				EncryptedApplicationId = id,
				ICRTypeId = branchCountryIso3 == "RUS" ? (int)PDFDocumentTypes.RussianTaxed : (branchCountryIso3 == "SAU" ? (int)PDFDocumentTypes.ArabicTaxed : (branchCountryIso3 == "TKM" ? (int)PDFDocumentTypes.Turkmen : (branchCountryIso3 == "ARE" ? (int)PDFDocumentTypes.EnglishTaxed : (int)PDFDocumentTypes.English))),
				encryptedMainApplicationId = relationalId,
                entryInformationFormTypeId = branchCountryIso3 == "RUS" ? (int)PDFDocumentTypes.Russian : (branchCountryIso3 == "SAU" ? (int)PDFDocumentTypes.Arabic : (branchCountryIso3 == "TKM" ? (int)PDFDocumentTypes.Turkmen : (int)PDFDocumentTypes.English)),
				cargo = isCargoIntegrationExists,
				cargoTypeId = branchCountryIso3 == "RUS" ? (int)PDFDocumentTypes.Russian : (branchCountryIso3 == "SAU" ? (int)PDFDocumentTypes.Arabic : (int)PDFDocumentTypes.English),
				applicationPageTypeId = (int)PDFDocumentTypes.English,
				isAuthorizedPrintAll = isAuthorizedPrintAll,
				LanguageId = LanguageId,
				BranchCountryIso3 = branchCountryIso3
			};

			return PartialView("_ApplicationPrintAll", viewModel);
		}

		#endregion

		public List<ExplicitConsentLanguage> GetExplicitConsentTextLanguage(int moduleCountry)
		{
			var explicitConsentLanguage = new List<ExplicitConsentLanguage>();

			if (moduleCountry == 181) // Turkmenistan
			{
				explicitConsentLanguage.Add(ExplicitConsentLanguage.English);
				explicitConsentLanguage.Add(ExplicitConsentLanguage.Turkish);
				explicitConsentLanguage.Add(ExplicitConsentLanguage.Turkmen);
				explicitConsentLanguage.Add(ExplicitConsentLanguage.Russian);
			}

			else if (moduleCountry == 3) // Algeria
			{
				explicitConsentLanguage.Add(ExplicitConsentLanguage.English);
				explicitConsentLanguage.Add(ExplicitConsentLanguage.Arabic);
				explicitConsentLanguage.Add(ExplicitConsentLanguage.French);
			}

			else if (moduleCountry == 185 /*United Arab Emirates*/ || moduleCountry == 152 /*Saudi Arabia*/ || moduleCountry == 93 /*Kuwait*/ || moduleCountry == 100 /*Libya*/)
			{
				explicitConsentLanguage.Add(ExplicitConsentLanguage.English);
				explicitConsentLanguage.Add(ExplicitConsentLanguage.Arabic);
			}

			else if (moduleCountry == 80) // Iraq
			{
				explicitConsentLanguage.Add(ExplicitConsentLanguage.IraqEnglish);
				explicitConsentLanguage.Add(ExplicitConsentLanguage.IraqArabic);
			}

			else
				explicitConsentLanguage.Add(ExplicitConsentLanguage.English);

			return explicitConsentLanguage;
		}

		public async Task<IActionResult> DetailDocumentSummary(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationDocumentApiResponse>>
				(ApiMethodName.Appointment.GetSanitizedApplicationDocument + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var visaCategories = GetCachedVisaCategories();

			var viewModel = new ApplicationDocumentViewModel
			{
				Document = new ApplicationDocumentViewModel.ApplicationDocument
				{
					TotalYearInCountry = apiResponse.Data.TotalYearInCountry,
					ReimbursementTypeId = apiResponse.Data.ReimbursementTypeId,
					ReimbursementSponsorDetail = apiResponse.Data.ReimbursementSponsorDetail,
					Job = apiResponse.Data.Job,
					OccupationId = apiResponse.Data.OccupationId,
					CompanyName = apiResponse.Data.CompanyName,
					TotalYearInCompany = apiResponse.Data.TotalYearInCompany,
					MonthlySalary = apiResponse.Data.MonthlySalary,
					MonthlySalaryCurrencyId = apiResponse.Data.MonthlySalaryCurrencyId,
					HasBankAccount = apiResponse.Data.HasBankAccount,
					BankBalance = apiResponse.Data.BankBalance,
					BankBalanceCurrencyId = apiResponse.Data.BankBalanceCurrencyId,
					HasDeed = apiResponse.Data.HasDeed,
					VisaCategoryId = apiResponse.Data.VisaCategoryId,
					VisaCategory = GetVisaCategoryNameFromId(apiResponse.Data.VisaCategoryId, visaCategories),
					AdditionalServiceTypeId = apiResponse.Data.AdditionalServiceTypeId,
					NumberOfEntryId = EnumHelper.GetEnumDescription(typeof(NumberOfEntryType), apiResponse.Data.NumberOfEntryId.ToString()),
					CountryId = apiResponse.Data.CountryId,
					HasEntryBan = apiResponse.Data.HasEntryBan,
					EntryDate = apiResponse.Data.EntryDate,
					ExitDate = apiResponse.Data.ExitDate,
					CityName = apiResponse.Data.CityName,
					AccomodationDetail = apiResponse.Data.AccomodationDetail,
					HasRelativeAbroad = apiResponse.Data.HasRelativeAbroad,
					RelativeLocation = apiResponse.Data.RelativeLocation,
					PersonTravelWith = apiResponse.Data.PersonTravelWith,
					PersonTravelWithHasVisa = apiResponse.Data.PersonTravelWithHasVisa,
					ProvidedWithHasRelatedInsurance = apiResponse.Data.ProvidedWithHasRelatedInsurance,
					ApplicationTogether = apiResponse.Data.ApplicationTogether,
					ApplicationTogetherFiftyYearCount = apiResponse.Data.ApplicationTogetherFiftyYearCount,
					ApplicationTogetherFifteenYearCount = apiResponse.Data.ApplicationTogetherFifteenYearCount,
					HasPersonVisitedTurkeyBefore = apiResponse.Data.HasPersonVisitedTurkeyBefore,
					VehicleTypeId = apiResponse.Data.VehicleTypeId,
					PlateNo = apiResponse.Data.PlateNo,
					ModelYear = apiResponse.Data.ModelYear,
					BrandModelId = apiResponse.Data.BrandModelId,
					ChassisNumber = apiResponse.Data.ChassisNumber,
					BrandText = apiResponse.Data.BrandText,
					ModelText = apiResponse.Data.ModelText,
					ResidenceApplicationToBeMade = apiResponse.Data.ResidenceApplicationToBeMade
				},

				VisaHistories = apiResponse.Data.VisaHistories.Select(p => new ApplicationDocumentViewModel.ApplicationVisaHistory
				{
					CountryId = p.CountryId,
					CountryName = p.CountryName,
					FromDate = p.FromDate,
					UntilDate = p.UntilDate,
					IsUsed = p.IsUsed,
					NumberOfEntryId = p.NumberOfEntryId,
					NumberOfEntry = EnumHelper.GetEnumDescription(typeof(NumberOfEntryType), p.NumberOfEntryId.ToString()),
					VisaIsUsedYear = p.VisaIsUsedYear,
					VisaIsUsedYearString = EnumHelper.GetEnumDescription(typeof(VisaIsUsedYear), p.VisaIsUsedYear.ToString()),
					OldVisaDecision = p.OldVisaDecisionId == null ? "" : EnumHelper.GetEnumDescription(typeof(OldVisaDecisionType), p.OldVisaDecisionId.ToString()),
				}).ToList()
			};

			return PartialView("_ApplicationDocumentSummary", viewModel);
		}

		public async Task<IActionResult> DetailCargoStatusHistory(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int applicationId = encryptedApplicationId.ToDecryptInt();

			var apiCargoResponse = await RestHttpClient.Create().Get<ApplicationCargoTrackingResponse>(
				AppSettings.Cargo.BaseApiUrl + CargoEndpoint.Track + applicationId, QMSApiDefaultRequestHeaders);

			if (apiCargoResponse?.Data is null)
				return Json(new { Message = apiCargoResponse?.Message ?? $"{ResultMessage.ErrorOccurred.ToDescription()}", ResultType = ResultType.Warning });

			var viewModel = new ApplicationCargoStatusHistoryViewModel
			{
				CargoTrackingNumber = apiCargoResponse.Data.TrackingNumber,
				ApplicationCargoStatusHistories = apiCargoResponse.Data.Status?.Select(p => new ApplicationCargoStatusHistory
				{
					Status = p.Description,
					Date = DateTime.TryParseExact(p.Date, "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var exactDate) ? exactDate.ToString("yyyy-MM-dd") :
						DateTime.TryParse(p.Date, out var date) ? date.ToString("yyyy-MM-dd") : null,
					Time = DateTime.TryParseExact(p.Time, "HHmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out var timeWithSec) ?
						timeWithSec.ToString("HH:mm:ss") :
						DateTime.TryParseExact(p.Time, "HHmm", CultureInfo.InvariantCulture, DateTimeStyles.None, out var timeWithoutSec) ? timeWithoutSec.ToString("HH:mm") : null
				}).ToList()
			};

			return PartialView("_ApplicationCargoStatusHistory", viewModel);
		}

		public async Task<IActionResult> DetailStatusHistory(string encryptedApplicationId)
		{

			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationStatusHistoryApiResponse>>
				(ApiMethodName.Appointment.GetSanitizedApplicationStatusHistory + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var viewModel = new ApplicationStatusHistoryViewModel
			{
				StatusHistories = apiResponse.Data.StatusHistories.Select(p => new ApplicationStatusHistoryViewModel.ApplicationStatusHistory()
				{
					Order = p.Order,
					Status = p.Status,
					NameSurname = p.NameSurname,
					ApplicationCreatedAt = apiResponse.Data.ApplicationTime,
					StatusDate = p.StatusDate
				}).ToList(),
			};

			return PartialView("_ApplicationStatusHistory", viewModel);
		}

		public async Task<IActionResult> DetailApplicationHistory(string encryptedApplicationId)
		{

			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationHistoryApiResponse>>
                ($"{ApiMethodName.Appointment.GetSanitizedApplicationHistory + id}/{LanguageId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var viewModel = new ApplicationHistoryViewModel
			{
				ApplicationHistories = apiResponse.Data.ApplicationHistories.Select(p => new ApplicationHistoryViewModel.ApplicationHistory
				{
					ApplicationHistoryId = p.ApplicationHistoryId,
					PropertyName = p.PropertyName,
					PreviousValue = p.PreviousValue,
					CurrentValue = p.CurrentValue,
					CreatedBy = p.CreatedBy,
					CreatedByNameSurname = p.CreatedByNameSurname,
					CreatedAt = p.CreatedAt
				}).ToList()
			};

			var visaCategories = GetCachedVisaCategories();

			foreach (var item in viewModel.ApplicationHistories)
			{
				item.PropertyNameDetail = GetApplicationHistoryPropertyDetail(item.PropertyName);
				item.PreviousValueDetail = GetApplicationHistoryValueDetail(item.PropertyName, item.PreviousValue, visaCategories);
				item.CurrentValueDetail = GetApplicationHistoryValueDetail(item.PropertyName, item.CurrentValue, visaCategories);
			}

			return PartialView("_ApplicationHistory", viewModel);
		}

		public async Task<IActionResult> EasyUse(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationEasyUseApiResponse>>
				(ApiMethodName.Appointment.GetApplicationEasyUse + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			if (UserSession.BranchId.HasValue && UserSession.BranchId.Value != apiResponse.Data.BranchId)
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			var viewModel = new ApplicationEasyUseViewModel
			{
				EncryptedId = apiResponse.Data.Id.ToEncrypt(),
				BranchId = apiResponse.Data.BranchId,
				PassportNumber = apiResponse.Data.PassportNumber,
				IsActive = apiResponse.Data.IsActive,
				IsDeleted = apiResponse.Data.IsDeleted,
				ApplicationTime = apiResponse.Data.ApplicationTime.DateTime,
				StatusId = apiResponse.Data.StatusId,
				ApplicationInsurances = apiResponse.Data.ApplicationInsurances?.Select(p2 => new ApplicationEasyUseViewModel.ApplicationInsurance
				{
					Id = p2.Id,
					Number = p2.Number,
					IsActive = p2.IsActive,
					IsDeleted = p2.IsDeleted
				}).ToList()
			};

			return PartialView("_EasyUse", viewModel);
		}

		private string GetApplicationHistoryPropertyDetail(string propertyName)
		{
			var result = propertyName;

			switch (propertyName)
			{
				case "ApplicantTypeId":
					result = SiteResources.ApplicantType;
					break;
				case "ApplicationTypeId":
					result = SiteResources.ApplicationType;
					break;
				case "PassportNumber":
					result = SiteResources.PassportNumber;
					break;
				case "PassportExpireDate":
					result = SiteResources.PassportExpireDate;
					break;
				case "ApplicationPassportStatusId":
					result = SiteResources.ApplicationPassportStatus;
					break;
				case "TitleId":
					result = SiteResources.Title;
					break;
				case "Name":
					result = SiteResources.Name;
					break;
				case "Surname":
					result = SiteResources.Surname;
					break;
				case "BirthDate":
					result = SiteResources.BirthDate;
					break;
				case "GenderId":
					result = SiteResources.Gender;
					break;
				case "MaritalStatusId":
					result = SiteResources.MaritalStatus;
					break;
				case "NationalityId":
					result = SiteResources.Nationality;
					break;
				case "MaidenName":
					result = SiteResources.MaidenName;
					break;
				case "FatherName":
					result = SiteResources.FatherName;
					break;
				case "MotherName":
					result = SiteResources.MotherName;
					break;
				case "Email":
					result = SiteResources.Email;
					break;
				case "PhoneNumber1":
					result = SiteResources.PhoneNumber;
					break;
				case "PhoneNumber2":
					result = SiteResources.PhoneNumber;
					break;
				case "Address":
					result = SiteResources.Address;
					break;
				case "ApplicationStatusId":
					result = SiteResources.ApplicationStatus;
					break;
				case "Note":
					result = SiteResources.Notes;
					break;
				case "StatusId":
					result = SiteResources.Status;
					break;
				//ApplicationDocument
				case "TotalYearInCountry":
					result = SiteResources.TotalYearInCountry;
					break;
				case "ReimbursementTypeId":
					result = SiteResources.ReimbursementType;
					break;
				case "ReimbursementSponsorDetail":
					result = SiteResources.ReimbursementSponsorDetail;
					break;
				case "Job":
					result = SiteResources.Job;
					break;
				case "OccupationId":
					result = SiteResources.Occupation;
					break;
				case "CompanyName":
					result = SiteResources.CompanyName;
					break;
				case "TotalYearInCompany":
					result = SiteResources.TotalYearInCompany;
					break;
				case "MonthlySalary":
					result = SiteResources.MonthlySalary;
					break;
				case "MonthlySalaryCurrencyId":
					result = $"{SiteResources.Currency} ({SiteResources.MonthlySalary})";
					break;
				case "HasBankAccount":
					result = SiteResources.HasBankAccount;
					break;
				case "BankBalance":
					result = SiteResources.BankBalance;
					break;
				case "BankBalanceCurrencyId":
					result = $"{SiteResources.Currency} ({SiteResources.BankBalance})";
					break;
				case "HasDeed":
					result = SiteResources.HasDeed;
					break;
				case "VisaCategoryId":
					result = SiteResources.VisaCategory;
					break;
				case "HasEntryBan":
					result = SiteResources.HasEntryBan;
					break;
				case "EntryDate":
					result = SiteResources.EntryDate;
					break;
				case "ExitDate":
					result = SiteResources.ExitDate;
					break;
				case "CityName":
					result = SiteResources.CityToVisit;
					break;
				case "AccomodationDetail":
					result = SiteResources.AccomodationDetail;
					break;
				case "HasRelativeAbroad":
					result = SiteResources.HasRelativeAbroad;
					break;
				case "RelativeLocation":
					result = SiteResources.RelativeLocation;
					break;
				case "PersonTravelWith":
					result = SiteResources.PersonTravelWith;
					break;
				case "PersonTravelWithHasVisa":
					result = SiteResources.PersonTravelWithHasVisa;
					break;
				case "ProvidedWithHasRelatedInsurance":
					result = SiteResources.ProvidedWithHasRelatedInsurance;
					break;
				case "ApplicationTogether":
					result = SiteResources.ApplicationTogether;
					break;
				case "HasPersonVisitedTurkeyBefore":
					result = SiteResources.HasPersonVisitedTurkeyBefore;
					break;
				default:
					break;
			}

			return result;
		}

		private string GetApplicationHistoryValueDetail(string propertyName, string value, List<BranchApplicationCountryVisaCategoriesApiResponse.VisaCategory> visaCategories)
		{
			var result = value;
			switch (propertyName)
			{
				case "ApplicantTypeId":
					result = EnumHelper.GetEnumDescription(typeof(ApplicantType), value);
					break;
				case "ApplicationTypeId":
					result = EnumHelper.GetEnumDescription(typeof(ApplicationType), value);
					break;
				case "ApplicationPassportStatusId":
					result = EnumHelper.GetEnumDescription(typeof(ApplicationPassportStatus), value);
					break;
				case "TitleId":
					result = EnumHelper.GetEnumDescription(typeof(Title), value);
					break;
				case "GenderId":
					result = EnumHelper.GetEnumDescription(typeof(Gender), value);
					break;
				case "MaritalStatusId":
					result = EnumHelper.GetEnumDescription(typeof(MaritalStatus), value);
					break;
				case "NationalityId":
					//TODO: get country list and use the related value
					break;
				case "ApplicationStatusId":
					//TODO: use the value taken by ApplicationStatus table
					break;
				case "StatusId":
					result = EnumHelper.GetEnumDescription(typeof(ApplicationStatus), value);
					break;
				//ApplicationDocument
				case "ReimbursementTypeId":
					result = EnumHelper.GetEnumDescription(typeof(ReimbursementType), value);
					break;
				case "OccupationId":
					result = EnumHelper.GetEnumDescription(typeof(DataOccupationType), value);
					break;
				case "MonthlySalaryCurrencyId":
					result = EnumHelper.GetEnumDescription(typeof(CurrencyType), value);
					break;
				case "HasBankAccount":
					result = EnumHelper.GetEnumDescription(typeof(YesNo), value);
					break;
				case "BankBalanceCurrencyId":
					result = EnumHelper.GetEnumDescription(typeof(CurrencyType), value);
					break;
				case "HasDeed":
					result = EnumHelper.GetEnumDescription(typeof(YesNo), value);
					break;
				case "VisaCategoryId":
					result = GetVisaCategoryNameFromId(value.ToInt(), visaCategories);
                    break;
				case "HasEntryBan":
					result = EnumHelper.GetEnumDescription(typeof(YesNo), value);
					break;
				case "HasRelativeAbroad":
					result = EnumHelper.GetEnumDescription(typeof(YesNo), value);
					break;
				case "PersonTravelWithHasVisa":
					result = EnumHelper.GetEnumDescription(typeof(YesNo), value);
					break;
				case "ProvidedWithHasRelatedInsurance":
					result = EnumHelper.GetEnumDescription(typeof(YesNo), value);
					break;
				default:
					break;
			}
			return result;
		}

		public async Task<IActionResult> IsApplicationHasDifferentJob(string passportNumber, int countryId, int nationalityId, string job, int oppucationId, string name, string surname, string birthDate, string fatherName, string motherName)
		{

			if (!MemoryCache.TryGetValue(CacheKeys.BranchApplicationStatusCache, out BranchApplicationStatusesApiResponse cacheItemBranchApplicationStatus) ||
							!MemoryCache.TryGetValue(CacheKeys.BranchCache, out BranchesApiResponse cacheItemBranch))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurredInApplicationStatusCheck });

			var branchApplicationStatuses = cacheItemBranchApplicationStatus.BranchApplicationStatuses.Where(p => p.BranchId == UserSession.BranchId).ToList();

			if (branchApplicationStatuses == null || !branchApplicationStatuses.Any())
				return Json(new { Result = false, ErrorMessage = $"{SiteResources.ErrorOccurredInApplicationStatusCheck} ({SiteResources.BranchApplicationStatusItemNotFound})" });

			var apiRequest = new PaginatedApplicationsApiRequest
			{
				CountryId = countryId,
				PassportNumber = passportNumber,
				NationalityId = nationalityId,
				Job = job,
				OccupationId = oppucationId,
				Name = name,
				Surname = surname,
				BirthDate = DateTime.Parse(birthDate, new CultureInfo(SiteResources.CultureTr)),
				FatherName = fatherName,
				MotherName = motherName,
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.AddUpdateApplicationsControlForJob, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurred });

			if (apiResponse.Data == null || !apiResponse.Data.Items.Any())
				return Json(new { Result = true });

			var previousApplication = apiResponse.Data.Items.First().Applications.LastOrDefault(p => p.StatusId != (int)ApplicationStatus.Cancelled &&
																		p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
																		p.ApplicationTypeId != ApplicationType.NonApplication.ToInt() &&
																		p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
																		p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
																		p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
																		p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() && (!string.IsNullOrEmpty(p.Document.Job) && !string.IsNullOrEmpty(job) && !p.Document.Job.Contains(job)) &&
																		branchApplicationStatuses.Any(p2 => p2.ApplicationStatusId == p.ApplicationStatusId && !p2.IsNewApplicationWithSamePassportNumberBlocked));

			if (previousApplication != null)
			{
				return Json(new { Result = false, ErrorMessage = $"{SiteResources.PreviousJobOfApplicant}: {previousApplication.Document.Job} {SiteResources.ApplicationDate}:  {previousApplication.ApplicationTime}" });
			}

			return Json(new { Result = true });

		}

		public async Task<IActionResult> IsApplicationHasPreviousNote(string passportNumber, int countryId, int nationalityId)
		{
			if (!MemoryCache.TryGetValue(CacheKeys.BranchApplicationStatusCache,
					out BranchApplicationStatusesApiResponse cacheItemBranchApplicationStatus) ||
				!MemoryCache.TryGetValue(CacheKeys.BranchCache, out BranchesApiResponse cacheItemBranch))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurredInApplicationStatusCheck });

			var branchApplicationStatuses = cacheItemBranchApplicationStatus.BranchApplicationStatuses
				.Where(p => p.BranchId == UserSession.BranchId).ToList();

			if (!branchApplicationStatuses.Any())
				return Json(new
				{
					Result = false,
					ErrorMessage =
						$"{SiteResources.ErrorOccurredInApplicationStatusCheck} ({SiteResources.BranchApplicationStatusItemNotFound})"
				});

			var apiRequest = new PaginatedApplicationsApiRequest
			{
				CountryId = countryId,
				PassportNumber = passportNumber,
				NationalityId = nationalityId,
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.AddUpdateApplicationsControlForPassportNumber,
					AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurred });

			if (apiResponse.Data == null || !apiResponse.Data.Items.Any())
				return Json(new { Result = true });

			var previousApplications = apiResponse.Data.Items.First().Applications.Where(p =>
				p.StatusId != (int)ApplicationStatus.Cancelled &&
				p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
				p.ApplicationTypeId != ApplicationType.NonApplication.ToInt() &&
				p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
				p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
				p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
				p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
				branchApplicationStatuses.Any(p2 =>
					p2.ApplicationStatusId == p.ApplicationStatusId &&
					!p2.IsNewApplicationWithSamePassportNumberBlocked)).ToList();

			if (previousApplications.Any() && UserSession.BranchId != null)
			{
				var allowedBranches = cacheItemBranch.Branches.Where(r => r.Country.Id == 80 || r.Country.Id == 181).Select(r => r.Id).ToList();
				var allowedTurkmenBranches = cacheItemBranch.Branches.Where(r => r.Country.Id == 181).Select(r => r.Id).ToList();
                var checkTurkmenApplicationReferenceDate = new DateTime(2025, 3, 18);
                string cancelledInsuranceMessage = null;

				if (!previousApplications.Any(p => p.ApplicationNotes.Any()) && !allowedBranches.Contains(UserSession.BranchId.Value))
					return Json(new { Result = true });

				var builder = new StringBuilder();
				var index = 0;
				var buttonStr = await this.RenderViewWithoutModelAsync("Template/_ApplicationDetailButtonTemplate");
                var rejectedButton = await this.RenderViewWithoutModelAsync("Template/_ApplicationDetailRejectedFeeButtonTemplate");

                if (previousApplications.Any(p => p.ApplicationNotes.Any()))
				{
					AppendLineToContent(builder, SiteResources.ApplicationNotes.ToUpper() + ": ", index);
					index++;

					foreach (var application in previousApplications)
					{
						for (var i = 0; i < application.ApplicationNotes.Count; i++)
						{
							AppendLineToContent(builder, application.ApplicationTime.ToString("dd/MM/yyyy") + ": " + application.ApplicationNotes[i], index);
							index++;
						}

						AppendLineToContent(builder, string.Empty, index);
						index++;
					}
				}

				if (allowedBranches.Contains(UserSession.BranchId.Value))
                {
                    var lastApplication = previousApplications.OrderByDescending(s => s.ApplicationTime).FirstOrDefault();

					foreach (var application in previousApplications)
					{
                        #region RejectedApplicationCheck

                        if (AppSettings.EnableRejectedApplicationInsuranceCase && allowedTurkmenBranches.Contains(UserSession.BranchId.Value) && application.Id == lastApplication.Id && application.ApplicationTime.Date >= checkTurkmenApplicationReferenceDate &&
                            (application.PolicyEndDate.HasValue && DateTime.Now.Date <= application.PolicyEndDate.Value.Date) && 
                             (application.StatusHistories.Any(s => s.ApplicationStatusId == (int)ApplicationStatusType.Rejection ||
                                                                   s.ApplicationStatusId == (int)ApplicationStatusType.IstizanRejection) || application.IsCancelledInsurance))
                        {
							if(application.VisaCategoryId == (int)VisaCategoryTypeEnum.VisitSpecialResidancePermit || application.VisaCategoryId == (int)VisaCategoryTypeEnum.TouristicResidencePermit)
                            {
                                if (application.PolicyPeriod is 178 or 179 or 180) // 6 ay
                                {
                                    AppendLineToContent(builder, $"{@SiteResources.ApplicationTime.ToTitleCase() + ": " + application.ApplicationTime.ToString("dd/MM/yyyy") + " " + buttonStr.Replace("<replaceEncryptedId>", application.Id.ToEncrypt()) + " " + rejectedButton.Replace("replaceButtonLabel", SiteResources.FreeCreateSixMonthInsuranceButtonLabel.ToTitleCase()).Replace("<policyType>", ((int)RejectedApplicationFeePolicyType.SixMonth).ToString()) + "" + "<br>"}", index);
                                    cancelledInsuranceMessage = SiteResources.SixMonthCancelledInsuranceMessage;
                                }
								else if (application.PolicyPeriod is 363 or 364 or 365) // 1 yıl
                                {
                                    AppendLineToContent(builder, $"{@SiteResources.ApplicationTime.ToTitleCase() + ": " + application.ApplicationTime.ToString("dd/MM/yyyy") + " " + buttonStr.Replace("<replaceEncryptedId>", application.Id.ToEncrypt()) + " " + rejectedButton.Replace("replaceButtonLabel", SiteResources.FreeCreateOneYearInsuranceButtonLabel.ToTitleCase()).Replace("<policyType>", ((int)RejectedApplicationFeePolicyType.OneYear).ToString()) + "" + "<br>"}", index);
                                    cancelledInsuranceMessage = SiteResources.OneYearCancelledInsuranceMessage;
                                }
                                else
                                {
                                    AppendLineToContent(builder, $"{@SiteResources.ApplicationTime.ToTitleCase() + ": " + application.ApplicationTime.ToString("dd/MM/yyyy") + " " + buttonStr.Replace("<replaceEncryptedId>", application.Id.ToEncrypt()) + "<br>"}", index);
                                }
                            }
                            else if (application.PolicyPeriod is 89 or 90 or 91)
                            {
                                AppendLineToContent(builder, $"{@SiteResources.ApplicationTime.ToTitleCase() + ": " + application.ApplicationTime.ToString("dd/MM/yyyy") + " " + buttonStr.Replace("<replaceEncryptedId>", application.Id.ToEncrypt()) + " " + rejectedButton.Replace("replaceButtonLabel", SiteResources.FreeCreateThreeMonthInsuranceButtonLabel.ToTitleCase()).Replace("<policyType>", ((int)RejectedApplicationFeePolicyType.ThreeMonth).ToString()) + "" + "<br>"}", index);
                                cancelledInsuranceMessage = SiteResources.ThreeMonthCancelledInsuranceMessage;
                            }
                            else
                            {
                                AppendLineToContent(builder, $"{@SiteResources.ApplicationTime.ToTitleCase() + ": " + application.ApplicationTime.ToString("dd/MM/yyyy") + " " + buttonStr.Replace("<replaceEncryptedId>", application.Id.ToEncrypt()) + "<br>"}", index);
                            }
                        }
                        else
                        {
                            AppendLineToContent(builder, $"{@SiteResources.ApplicationTime.ToTitleCase() + ": " + application.ApplicationTime.ToString("dd/MM/yyyy") + " " + buttonStr.Replace("<replaceEncryptedId>", application.Id.ToEncrypt()) + "<br>"}", index);
                        }

                        #endregion

                        index++;
					}
				}

                return Json(new
				{
					Result = false,
					ApplicationNote = builder.ToString(),
					CancelledInsuranceMessage = cancelledInsuranceMessage
                });
			}

			return Json(new { Result = true });
		}

		public void AppendLineToContent(StringBuilder builder, string content, int index)
		{
			builder.Append(content + "<br>");
			builder.Insert(index, Environment.NewLine);
		}

		public async Task<IActionResult> IsAllowedDeniedPassportNumber(string passportNumber, int countryId, int nationalityId, bool isAllowDeniedPassport, bool isExistingApplication, string encryptedApplicationId = null)
		{
			if (!MemoryCache.TryGetValue(CacheKeys.BranchApplicationStatusCache, out BranchApplicationStatusesApiResponse cacheItemBranchApplicationStatus) ||
							!MemoryCache.TryGetValue(CacheKeys.BranchCache, out BranchesApiResponse cacheItemBranch))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurredInApplicationStatusCheck });

			var branchApplicationStatuses = cacheItemBranchApplicationStatus.BranchApplicationStatuses.Where(p => p.BranchId == UserSession.BranchId).ToList();

			if (branchApplicationStatuses == null || !branchApplicationStatuses.Any())
				return Json(new { Result = false, ErrorMessage = $"{SiteResources.ErrorOccurredInApplicationStatusCheck} ({SiteResources.BranchApplicationStatusItemNotFound})" });

			var apiRequest = new PaginatedApplicationsApiRequest
			{
				CountryId = countryId,
				PassportNumber = passportNumber,
				NationalityId = nationalityId,
				IncludeApplicationStatusHistories = true
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.AddUpdateApplicationsControlForPassportNumber, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurred });

			if (apiResponse.Data == null || !apiResponse.Data.Items.Any())
				return Json(new { Result = true });

			int applicationId = 0;

			if (!string.IsNullOrEmpty(encryptedApplicationId))
				applicationId = encryptedApplicationId.ToDecryptInt();

			var branch = cacheItemBranch.Branches.FirstOrDefault(p => p.Id == UserSession.BranchId);

			if (branch == null)
				return Json(new { Result = false, ErrorMessage = $"{SiteResources.ErrorOccurredInApplicationStatusCheck} ({SiteResources.BranchNotFound})" });

			if (branch.CheckRejectedStatus && !isAllowDeniedPassport)
			{
				var checkRejectedStatusStartDate = DateTime.Today.AddDays(branch.CheckRejectedStatusPeriod * -1);
				var checkRejectedStatusEndDate = DateTime.Today;

				var rejectionApplicationStatusIds = branch.Country.Id == 80 ?
					new int[] { (int)ApplicationStatusType.Rejection } :
					new int[] { (int)ApplicationStatusType.Rejection, (int)ApplicationStatusType.IstizanRejection, (int)ApplicationStatusType.RejectionWithCountryEntryBanned };

				if (apiResponse.Data.Items.First().Applications.Any(p => (applicationId != 0 ? p.Id != applicationId : true) &&
																		 p.PassportNumber == passportNumber &&
																		 p.StatusId != (int)ApplicationStatus.Cancelled &&
																		 p.StatusHistories != null &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplication.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
																		 p.StatusHistories.Any(p2 => rejectionApplicationStatusIds.Contains(p2.ApplicationStatusId) &&
																			 p2.StatusDate.Date >= checkRejectedStatusStartDate &&
																			 p2.StatusDate.Date <= checkRejectedStatusEndDate)))
				{
					return Json(new { Result = false, ErrorMessage = $"{SiteResources.CannotApplyWithThisPassportNumber} ({SiteResources.ExistingRejectedApplicationStatus} - {SiteResources.CheckRejectedStatusPeriod}: {branch.CheckRejectedStatusPeriod} {SiteResources.Day})" });
				}
			}

			if (branch.CheckUnrealDocumentStatus && !isAllowDeniedPassport)
			{
				var checkUnrealDocumentStatusStartDate = DateTime.Today.AddDays(branch.CheckUnrealDocumentStatusPeriod * -1);
				var checkUnrealDocumentStatusEndDate = DateTime.Today;
				var unrealDocumentApplicationStatusId = (int)ApplicationStatusType.UnrealDocument;

				if (apiResponse.Data.Items.First().Applications.Any(p => (applicationId != 0 ? p.Id != applicationId : true) &&
																		 p.PassportNumber == passportNumber &&
																		 p.StatusId != (int)ApplicationStatus.Cancelled &&
																		 p.StatusHistories != null &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplication.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
																		 p.StatusHistories.Any(p2 => p2.ApplicationStatusId == unrealDocumentApplicationStatusId &&
																									 p2.StatusDate.Date >= checkUnrealDocumentStatusStartDate &&
																									 p2.StatusDate.Date <= checkUnrealDocumentStatusEndDate)))
				{
					return Json(new { Result = false, ErrorMessage = $"{SiteResources.CannotApplyWithThisPassportNumber} ({SiteResources.ExistingUnrealDocumentApplicationStatus} - {SiteResources.CheckUnrealDocumentStatusPeriod}: {branch.CheckUnrealDocumentStatusPeriod} {SiteResources.Day})" });
				}
			}

			if (branch.Country.Id == 80 && branch.CheckRejectionWithCountryEntryBannedStatus && !isAllowDeniedPassport)
			{
				var checkRejectionWithCountryEntryBannedStatusStartDate = DateTime.Today.AddDays(branch.CheckRejectionWithCountryEntryBannedStatusPeriod * -1);
				var checkRejectionWithCountryEntryBannedStatusEndDate = DateTime.Today;
				var rejectionWithCountryEntryBannedStatusId = (int)ApplicationStatusType.RejectionWithCountryEntryBanned;

				if (apiResponse.Data.Items.First().Applications.Any(p => (applicationId != 0 ? p.Id != applicationId : true) &&
																		 p.PassportNumber == passportNumber &&
																		 p.StatusId != (int)ApplicationStatus.Cancelled &&
																		 p.StatusHistories != null &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplication.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
																		 p.StatusHistories.Any(p2 => p2.ApplicationStatusId == rejectionWithCountryEntryBannedStatusId &&
																									 p2.StatusDate.Date >= checkRejectionWithCountryEntryBannedStatusStartDate &&
																									 p2.StatusDate.Date <= checkRejectionWithCountryEntryBannedStatusEndDate)))
				{
					return Json(new { Result = false, ErrorMessage = $"{SiteResources.CannotApplyWithThisPassportNumber} ({SiteResources.ExistingRejectionWithCountryEntryBannedStatus} - {SiteResources.CheckRejectionWithCountryEntryBannedStatusPeriod}: {branch.CheckRejectionWithCountryEntryBannedStatusPeriod} {SiteResources.Day})" });
				}
			}

			if (isAllowDeniedPassport)
			{
				var checkRejectedStatusStartDate = DateTime.Today.AddDays(branch.CheckRejectedStatusPeriod * -1);
				var checkUnrealDocumentStatusStartDate = DateTime.Today.AddDays(branch.CheckUnrealDocumentStatusPeriod * -1);
				var checkRejectionWithCountryEntryBannedStatusStartDate = DateTime.Today.AddDays(branch.CheckRejectionWithCountryEntryBannedStatusPeriod * -1);
				var checkRejectedStatusEndDate = DateTime.Today;
				var rejectionApplicationStatusIds = branch.Country.Id == 80 ?
					new int[] { (int)ApplicationStatusType.Rejection, (int)ApplicationStatusType.UnrealDocument, (int)ApplicationStatusType.RejectionWithCountryEntryBanned } :
					new int[] { (int)ApplicationStatusType.Rejection, (int)ApplicationStatusType.IstizanRejection, (int)ApplicationStatusType.UnrealDocument, (int)ApplicationStatusType.RejectionWithCountryEntryBanned };

				if (!apiResponse.Data.Items.First().Applications.Any(p => (applicationId != 0 ? p.Id != applicationId : true) &&
																		  p.PassportNumber == passportNumber &&
																		  p.StatusId != (int)ApplicationStatus.Cancelled &&
																		  p.StatusHistories != null &&
																		  p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
																		  p.ApplicationTypeId != ApplicationType.NonApplication.ToInt() &&
																		  p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
																		  p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
																		  p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
																		  p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
																		  p.StatusHistories.Any(p2 => rejectionApplicationStatusIds.Contains(p2.ApplicationStatusId) &&
																			  (p2.StatusDate.Date >= checkRejectedStatusStartDate ||
																			   p2.StatusDate.Date >= checkUnrealDocumentStatusStartDate ||
																			   p2.StatusDate.Date >= checkRejectionWithCountryEntryBannedStatusStartDate) &&
																			  p2.StatusDate.Date <= checkRejectedStatusEndDate)))
				{
					return Json(new { Result = false, ErrorMessage = $"{SiteResources.ThisPassportIsNotBlocked}" });
				}
			}

			if (apiResponse.Data.Items.First().Applications.Any(p => (applicationId != 0 ? p.Id != applicationId : true) &&
																	 p.PassportNumber == passportNumber &&
																	 p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
																	 p.ApplicationTypeId !=
								ApplicationType.NonApplication.ToInt() &&
																	 p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
																	 p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
																	 p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
																	 p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
																	 p.StatusId != (int)ApplicationStatus.Cancelled &&
																	 branchApplicationStatuses.Any(p2 => p2.ApplicationStatusId == p.ApplicationStatusId && p2.IsNewApplicationWithSamePassportNumberBlocked && !isAllowDeniedPassport)))
			{
				return Json(new { Result = false, ErrorMessage = $"{SiteResources.CannotApplyWithThisPassportNumber} ({SiteResources.Exception_NewApplicationWithSamePassportNumberIsNotAllowedForThisApplicationStatus})" });
			}

			if (apiResponse.Data.Items.First().Applications.Any(p => (applicationId != 0 ? p.Id != applicationId : true) &&
											 p.PassportNumber == passportNumber &&
											 p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
											 p.ApplicationTypeId != ApplicationType.NonApplication.ToInt() &&
											 p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
											 p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
											 p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
											 p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
											 //p.StatusId != (int)ApplicationStatus.Active &&
											 p.StatusId != (int)ApplicationStatus.Cancelled &&
											 /*!p.IsDeleted &&*/
											 branchApplicationStatuses.Any(p2 => p2.ApplicationStatusId == p.ApplicationStatusId && p2.IsNewApplicationWithSamePassportNumberBlocked)) && !isExistingApplication)
			{
				return Json(new { Result = false, ErrorMessage = $"{SiteResources.CannotApplyWithThisPassportNumber} ({SiteResources.Exception_NewApplicationWithSamePassportNumberIsNotAllowedForThisApplicationStatus})" });
			}

			return Json(new { Result = true });
		}

		public async Task<IActionResult> IsApplicationAvailableByPassportNumber(string passportNumber, int countryId, int nationalityId, int isNewEntryNonApplication, string encryptedApplicationId = null)
		{
			if (!MemoryCache.TryGetValue(CacheKeys.BranchApplicationStatusCache, out BranchApplicationStatusesApiResponse cacheItemBranchApplicationStatus) ||
				!MemoryCache.TryGetValue(CacheKeys.BranchCache, out BranchesApiResponse cacheItemBranch))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurredInApplicationStatusCheck });

			var branchApplicationStatuses = cacheItemBranchApplicationStatus.BranchApplicationStatuses.Where(p => p.BranchId == UserSession.BranchId).ToList();

			if (branchApplicationStatuses == null || !branchApplicationStatuses.Any())
				return Json(new { Result = false, ErrorMessage = $"{SiteResources.ErrorOccurredInApplicationStatusCheck} ({SiteResources.BranchApplicationStatusItemNotFound})" });

			var apiRequest = new PaginatedApplicationsApiRequest
			{
				CountryId = countryId,
				PassportNumber = passportNumber,
				NationalityId = nationalityId,
				IncludeApplicationStatusHistories = true
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.AddUpdateApplicationsControlForPassportNumber, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurred });

			if (apiResponse.Data == null || !apiResponse.Data.Items.Any())
				return Json(new { Result = true });

			int applicationId = 0;

			if (!string.IsNullOrEmpty(encryptedApplicationId))
				applicationId = encryptedApplicationId.ToDecryptInt();

			var branch = cacheItemBranch.Branches.FirstOrDefault(p => p.Id == UserSession.BranchId);

			if (branch == null)
				return Json(new { Result = false, ErrorMessage = $"{SiteResources.ErrorOccurredInApplicationStatusCheck} ({SiteResources.BranchNotFound})" });

			var nonApplicationTypes = new int[] { (int)ApplicationType.NonApplicationInsurance, (int)ApplicationType.NonApplicationPcr, (int)ApplicationType.NonApplicationPhotocopy, (int)ApplicationType.NonApplicationPrintOut, (int)ApplicationType.NonApplicationPhotograph, (int)ApplicationType.NonApplication };

			if (branch.CheckRejectedStatus)
			{
				var checkRejectedStatusStartDate = DateTime.Today.AddDays(branch.CheckRejectedStatusPeriod * -1);
				var checkRejectedStatusEndDate = DateTime.Today;
				var rejectionApplicationStatusIds = branch.Country.Id == 80 ?
					new int[] { (int)ApplicationStatusType.Rejection } :
					new int[] { (int)ApplicationStatusType.Rejection, (int)ApplicationStatusType.IstizanRejection, (int)ApplicationStatusType.RejectionWithCountryEntryBanned };

				if (apiResponse.Data.Items.First().Applications.Any(p => (applicationId != 0 ? p.Id != applicationId : true) &&
																		 p.PassportNumber == passportNumber &&
																		 p.StatusId != (int)ApplicationStatus.Cancelled &&
																		 p.StatusHistories != null &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplication.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
																		 p.StatusHistories.Any(p2 => rejectionApplicationStatusIds.Contains(p2.ApplicationStatusId) &&
																									 p2.StatusDate.Date >= checkRejectedStatusStartDate &&
																									 p2.StatusDate.Date <= checkRejectedStatusEndDate)))
				{
					return Json(new { Result = false, ErrorMessage = $"{SiteResources.CannotApplyWithThisPassportNumber} ({SiteResources.ExistingRejectedApplicationStatus} - {SiteResources.CheckRejectedStatusPeriod}: {branch.CheckRejectedStatusPeriod} {SiteResources.Day})" });
				}
			}

			if (branch.CheckUnrealDocumentStatus)
			{
				var checkUnrealDocumentStatusStartDate = DateTime.Today.AddDays(branch.CheckUnrealDocumentStatusPeriod * -1);
				var checkUnrealDocumentStatusEndDate = DateTime.Today;
				var unrealDocumentApplicationStatusId = (int)ApplicationStatusType.UnrealDocument;

				if (apiResponse.Data.Items.First().Applications.Any(p => (applicationId != 0 ? p.Id != applicationId : true) &&
																		 p.PassportNumber == passportNumber &&
																		 p.StatusId != (int)ApplicationStatus.Cancelled &&
																		 p.StatusHistories != null &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplication.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
																		 p.StatusHistories.Any(p2 => p2.ApplicationStatusId == unrealDocumentApplicationStatusId &&
																									 p2.StatusDate.Date >= checkUnrealDocumentStatusStartDate &&
																									 p2.StatusDate.Date <= checkUnrealDocumentStatusEndDate)))
				{
					return Json(new { Result = false, ErrorMessage = $"{SiteResources.CannotApplyWithThisPassportNumber} ({SiteResources.ExistingUnrealDocumentApplicationStatus} - {SiteResources.CheckUnrealDocumentStatusPeriod}: {branch.CheckUnrealDocumentStatusPeriod} {SiteResources.Day})" });
				}
			}

			if (branch.Country.Id == 80 && branch.CheckRejectionWithCountryEntryBannedStatus)
			{
				var checkRejectionWithCountryEntryBannedStatusStartDate = DateTime.Today.AddDays(branch.CheckRejectionWithCountryEntryBannedStatusPeriod * -1);
				var checkRejectionWithCountryEntryBannedStatusEndDate = DateTime.Today;
				var rejectionWithCountryEntryBannedStatusId = (int)ApplicationStatusType.RejectionWithCountryEntryBanned;

				if (apiResponse.Data.Items.First().Applications.Any(p => (applicationId != 0 ? p.Id != applicationId : true) &&
																		 p.PassportNumber == passportNumber &&
																		 p.StatusId != (int)ApplicationStatus.Cancelled &&
																		 p.StatusHistories != null &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplication.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
																		 p.StatusHistories.Any(p2 => p2.ApplicationStatusId == rejectionWithCountryEntryBannedStatusId &&
																									 p2.StatusDate.Date >= checkRejectionWithCountryEntryBannedStatusStartDate &&
																									 p2.StatusDate.Date <= checkRejectionWithCountryEntryBannedStatusEndDate)))
				{
					return Json(new { Result = false, ErrorMessage = $"{SiteResources.CannotApplyWithThisPassportNumber} ({SiteResources.ExistingRejectionWithCountryEntryBannedStatus} - {SiteResources.CheckRejectionWithCountryEntryBannedStatusPeriod}: {branch.CheckRejectionWithCountryEntryBannedStatusPeriod} {SiteResources.Day})" });
				}
			}

			if (apiResponse.Data.Items.First().Applications.Any(p => (applicationId != 0 ? p.Id != applicationId : true) &&
																	 p.PassportNumber == passportNumber && !nonApplicationTypes.Contains(isNewEntryNonApplication) &&
																	p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
																	 p.ApplicationTypeId !=
								ApplicationType.NonApplication.ToInt() &&
																	 p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
																	 p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
																	 p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
																	 p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
																	 p.StatusId != (int)ApplicationStatus.Cancelled &&
																	 branchApplicationStatuses.Any(p2 => p2.ApplicationStatusId == p.ApplicationStatusId && p2.IsNewApplicationWithSamePassportNumberBlocked)))
			{
				return Json(new { Result = false, ErrorMessage = $"{SiteResources.CannotApplyWithThisPassportNumber} ({SiteResources.Exception_NewApplicationWithSamePassportNumberIsNotAllowedForThisApplicationStatus})" });
			}

			return Json(new { Result = true });
		}

		public async Task<IActionResult> RelationShipCheck(int relationShipId, int relationalApplicationId, string passportNumber)
		{
			var apiRequest = new RelationShipCheckApiRequest
			{
				RelationShipId = relationShipId,
				RelationalApplicationId = relationalApplicationId,
				PassportNumber = passportNumber
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<RelationShipCheckApiResponse>>
				(apiRequest, ApiMethodName.Appointment.RelationShipCheck, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (apiResponse.Data.Result)
				return Json(new { Result = false, ErrorMessage = SiteResources.MoreThanOneHusbandOrWifeCannotBeEntered });

			return Json(new { Result = true });
		}

		public async Task<IActionResult> IsApplicationAvailableByPersonalInformation(string name, string surname, string passportNumber, string birthDate, string maidenName, string fatherName, string motherName, int nationalityId, int countryId, bool isAllowDeniedPassport, string encryptedApplicationId = null)
		{
			if (!MemoryCache.TryGetValue(CacheKeys.BranchApplicationStatusCache, out BranchApplicationStatusesApiResponse cacheItemBranchApplicationStatus) ||
				!MemoryCache.TryGetValue(CacheKeys.BranchCache, out BranchesApiResponse cacheItemBranch))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurredInApplicationStatusCheck });

			var apiRequest = new PaginatedApplicationsApiRequest
			{
				CountryId = countryId,
				Name = name?.Trim() ?? string.Empty,
				Surname = surname?.Trim() ?? string.Empty,
				BirthDate = DateTime.Parse(birthDate, new CultureInfo(SiteResources.CultureTr)),
				MaidenName = maidenName,
				FatherName = fatherName,
				NationalityId = nationalityId,
				MotherName = motherName,
				IncludeApplicationStatusHistories = true
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.AddUpdateApplicationsControlForPassportNumber, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurred });

			if (apiResponse.Data == null || !apiResponse.Data.Items.Any())
				return Json(new { Result = true });

			int applicationId = 0;

			if (!string.IsNullOrEmpty(encryptedApplicationId))
				applicationId = encryptedApplicationId.ToDecryptInt();

			var branch = cacheItemBranch.Branches.FirstOrDefault(p => p.Id == UserSession.BranchId);

			if (branch == null)
				return Json(new { Result = false, ErrorMessage = $"{SiteResources.ErrorOccurredInApplicationStatusCheck} ({SiteResources.BranchNotFound})" });

			if (branch.CheckRejectedStatus)
			{
				var checkRejectedStatusStartDate = DateTime.Today.AddDays(branch.CheckRejectedStatusPeriod * -1);
				var checkRejectedStatusEndDate = DateTime.Today;
				var rejectionApplicationStatusIds = branch.Country.Id == 80 ?
					new int[] { (int)ApplicationStatusType.Rejection } :
					new int[] { (int)ApplicationStatusType.Rejection, (int)ApplicationStatusType.IstizanRejection, (int)ApplicationStatusType.RejectionWithCountryEntryBanned };

				if (apiResponse.Data.Items.First().Applications.Any(p => (applicationId != 0 ? p.Id != applicationId : true) && !isAllowDeniedPassport &&
																		 p.StatusId != (int)ApplicationStatus.Cancelled &&
																		 p.StatusHistories != null &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplication.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
																		 p.StatusHistories.Any(p2 => rejectionApplicationStatusIds.Contains(p2.ApplicationStatusId) &&
																									 p2.StatusDate.Date >= checkRejectedStatusStartDate &&
																									 p2.StatusDate.Date <= checkRejectedStatusEndDate)))
				{
					return Json(new { Result = false, ErrorMessage = $"{SiteResources.PersonIsNotAllowedToApply} ({SiteResources.PreviouslyRejected})" });
				}
			}

			if (branch.CheckUnrealDocumentStatus)
			{
				var checkUnrealDocumentStatusStartDate = DateTime.Today.AddDays(branch.CheckUnrealDocumentStatusPeriod * -1);
				var checkUnrealDocumentStatusEndDate = DateTime.Today;
				var unrealDocumentApplicationStatusId = (int)ApplicationStatusType.UnrealDocument;

				if (apiResponse.Data.Items.First().Applications.Any(p => (applicationId != 0 ? p.Id != applicationId : true) && !isAllowDeniedPassport &&
																		 p.StatusId != (int)ApplicationStatus.Cancelled &&
																		 p.StatusHistories != null &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplication.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
																		 p.StatusHistories.Any(p2 => p2.ApplicationStatusId == unrealDocumentApplicationStatusId &&
																			 p2.StatusDate.Date >= checkUnrealDocumentStatusStartDate &&
																			 p2.StatusDate.Date <= checkUnrealDocumentStatusEndDate)))
				{
					return Json(new { Result = false, ErrorMessage = $"{SiteResources.PersonIsNotAllowedToApply} ({SiteResources.PreviouslyHasUnrealDocument})" });
				}

			}

			if (branch.Country.Id == 80 && branch.CheckRejectionWithCountryEntryBannedStatus)
			{
				var checkRejectionWithCountryEntryBannedStatusStartDate = DateTime.Today.AddDays(branch.CheckRejectionWithCountryEntryBannedStatusPeriod * -1);
				var checkRejectionWithCountryEntryBannedStatusEndDate = DateTime.Today;
				var checkRejectionWithCountryEntryBannedStatusId = (int)ApplicationStatusType.RejectionWithCountryEntryBanned;

				if (apiResponse.Data.Items.First().Applications.Any(p => (applicationId != 0 ? p.Id != applicationId : true) && !isAllowDeniedPassport &&
																		 p.StatusId != (int)ApplicationStatus.Cancelled &&
																		 p.StatusHistories != null &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplication.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
																		 p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
																		 p.StatusHistories.Any(p2 => p2.ApplicationStatusId == checkRejectionWithCountryEntryBannedStatusId &&
																			 p2.StatusDate.Date >= checkRejectionWithCountryEntryBannedStatusStartDate &&
																			 p2.StatusDate.Date <= checkRejectionWithCountryEntryBannedStatusEndDate)))
				{
					return Json(new { Result = false, ErrorMessage = $"{SiteResources.PersonIsNotAllowedToApply} ({SiteResources.PreviouslyHasRejectionWithCountryEntryBannedStatus})" });
				}

			}

			apiRequest.PassportNumber = passportNumber.Trim();

			var apiResponseControlForMissionRejection = await PortalHttpClientHelper
			   .PostAsJsonAsync<ApiResponse<MissionRejectionApiResponse>>
			   (apiRequest, ApiMethodName.Appointment.ApplicationControlForMissionRejection, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
			   .ConfigureAwait(false);

			if (!await apiResponseControlForMissionRejection.Validate(out ResultModel result1).ConfigureAwait(false))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurred });

			if (apiResponseControlForMissionRejection.Data.Result)
				return Json(new { Result = false, ErrorMessage = SiteResources.MissionRejectionWarning });

			return Json(new { Result = true });
		}

		public async Task<IActionResult> PartialApplicationHistoryByPassportNumber(string passportNumber, int? countryId, string encryptedApplicationId = null, bool IngonreCancelledApplications = false)
		{
			var isAuthorizedForEarlyApplicationAuthorization = false;

			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object actions))
			{
				var roleActions = (RoleActionApiResponse)actions;

				isAuthorizedForEarlyApplicationAuthorization = roleActions.RoleActionSites.Where(r => UserSession.RoleIds.Contains(r.Role.Id))
					.Any(p => p.RoleActions.Any(q => (q.Action.ActionTranslations.Any(r => r.Name == SiteResources.IndiaEarlyApplicationAuthorization) || q.Action.Method == "IsAuthorizedForNepal") && q.Action.IsActive));
			}

			var apiRequest = new PaginatedApplicationsApiRequest
			{
				CountryId = countryId,
				PassportNumber = passportNumber,
				isRefundable = false,
				IngonreCancelledApplications = IngonreCancelledApplications,
				IgnoreEarlyApplications = isAuthorizedForEarlyApplicationAuthorization
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.GetSanitizedPaginatedApplications, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var viewModel = new List<ApplicationViewModel>();

			var visaCategories = GetCachedVisaCategories();

			if (apiResponse.Data != null && apiResponse.Data.Items.Any())
			{
				viewModel = apiResponse.Data.Items.First().Applications
					.Select(p => new ApplicationViewModel
					{
						EncryptedId = p.Id.ToEncrypt(),
						BranchApplicationCountryId = p.BranchApplicationCountryId,
						BranchName = p.BranchName,
						CountryName = p.CountryName,

						ApplicantTypeId = p.ApplicantTypeId,
						ApplicationTypeId = p.ApplicationTypeId,
						PassportNumber = p.PassportNumber,
						PassportExpireDate = p.PassportExpireDate,
						ApplicationPassportStatusId = p.ApplicationPassportStatusId,

						TitleId = p.TitleId,
						Name = p.Name,
						Surname = p.Surname,
						BirthDate = p.BirthDate,
						GenderId = p.GenderId,
						MaritalStatusId = p.MaritalStatusId,
						NationalityId = p.NationalityId,
						Nationality = p.Nationality,
						MaidenName = p.MaidenName,
						FatherName = p.FatherName,
						MotherName = p.MotherName,
						Email = p.Email,
						PhoneNumber1 = p.PhoneNumber1,
						PhoneNumber2 = p.PhoneNumber2,
						Address = p.Address,

						ApplicationTime = p.ApplicationTime,
						ApplicationStatusId = p.ApplicationStatusId,
						ApplicationStatus = p.ApplicationStatus,

						StatusId = p.StatusId,

						HasApplicationNotes = p.HasApplicationNotes,

						Document = new ApplicationViewModel.ApplicationDocument
						{
							VisaCategoryId = p.Document.VisaCategoryId,
							VisaCategory = GetVisaCategoryNameFromId(p.Document.VisaCategoryId, visaCategories)
						}
					}).ToList();
			}

			return PartialView("_ApplicationHistoryByPassportNumber", viewModel);
		}

		public async Task<IActionResult> PartialGetPreApplicationHistory(string passportNumber, int nationalityId)
		{
			var apiRequest = new PaginatedPreApplicationApiRequest
			{
				NationalityId = nationalityId,
				PassportNumber = passportNumber,
				BranchId = UserSession.BranchId,
				IsFromApplicationPage = true
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<PaginatedPreApplicationApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.GetPaginatedPreApplications, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			if (apiResponse.Data == null || !apiResponse.Data.Items.First().PreApplications.Any())
			{
				if (!nationalityId.IsNumericAndGreaterThenZero())
					return Content($"<h3>{EnumResources.NoAppointmentFoundWithGivenPassport}</h3>");
				else
					return Content($"<h3>{EnumResources.NoAppointmentFoundWithGivenPassportAndNationality}</h3>");
			}

			var viewModel = apiResponse.Data.Items.First().PreApplications
					.Select(p => new PreApplicationApplicantViewModel
					{
						PreApplicationNumber = p.PreApplicationId.ToApplicationNumber(),
						EncryptedPreApplicationId = p.PreApplicationId.ToEncrypt(),
						EncryptedPreApplicationApplicantId = p.PreApplicationApplicantId.ToEncrypt(),
						Name = p.Name,
						Surname = p.Surname,
						PassportNumber = p.PassportNumber,
						PhoneNumber = p.PhoneNumber,
						BranchName = p.BranchName,
						PreApplicationTime = p.Slot.DateTime == new DateTime(2021, 01, 01) ? SiteResources.WalkIn : p.Slot.DateTime.ToString("dd/MM/yyyy HH:mm"),
						VasTypeId = p.VasTypeId,
						ApplicationTypeId = p.ApplicationTypeId,
						ApplicantTypeId = p.ApplicantTypeId,
						Nationality = p.Nationality.IsNullOrWhitespace() ? "N/A" : p.Nationality
					}).ToList();

			return PartialView("_PreApplicationHistoryByPassportNumber", viewModel);
		}

		public async Task<IActionResult> ConnectPreApplicationApplicant(string applicantId)
		{
			var preApplicantId = applicantId.ToDecryptInt();

			var preApplicationResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<PreApplicationApplicantApiResponse>>
					($"{ApiMethodName.QueueMatic.GetPreApplicationApplicant + preApplicantId}/{UserSession.BranchId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (preApplicationResponse == null || preApplicationResponse.Data == null)
				return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

			return Json(new ResultModel
			{
				Data = preApplicationResponse.Data,
				Message = string.Format(EnumResources.ApplicationPreApplicationMessage, preApplicationResponse.Data.PreApplicationNumber, preApplicationResponse.Data.Name, preApplicationResponse.Data.Surname),
				ResultType = ResultType.Success
			});
		}

		[HttpGet]
		public async Task<IActionResult> IsLastApplication(int ApplicationId)
		{
			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<LastApplicationApiResponse>>
				(ApiMethodName.Appointment.IsLastApplication + ApplicationId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (apiResponse == null)
				return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

			return Json(new ResultModel { Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		[HttpPost]
		[AllowAnonymous]
		public ActionResult CheckVisaExemption(DateTime birthDate, int nationalityId)
		{
			var age = DateTime.Today.Year - birthDate.Year;

			if (DateTime.Today < birthDate.AddYears(age))
				age--;


			CountriesApiResponse nationalities = new CountriesApiResponse();
			if (MemoryCache.TryGetValue(CacheKeys.CountryCache, out object value))
			{
				nationalities = (CountriesApiResponse)value;
			}


			var countryIso3 = string.Empty;

			if (nationalities.Countries.Any())
				countryIso3 = nationalities.Countries.FirstOrDefault(c => c.Id == nationalityId).ISO3;


			string message = countryIso3 switch
			{
				"IRQ" when age < 15 => SiteResources.VisaExemptionIraqUnder15,
				"IRQ" when age >= 50 => SiteResources.VisaExemptionIraqOver50,
				"DZA" when age < 15 => SiteResources.VisaExemptionAlgerianUnder15,
				"DZA" when age >= 65 => SiteResources.VisaExemptionAlgerianOver65,
				"LBY" when age < 16 => SiteResources.VisaExemptionLibyanUnder16,
				"LBY" when age >= 45 => SiteResources.VisaExemptionLibyanOver45,
				_ => string.Empty
			};

			return Json(new { Message = message });
		}

		[HttpGet]
		public async Task<IActionResult> IsNotCreatedInsuranceExist(int applicationId)
		{
			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ValidateApiResponse>>
				(ApiMethodName.Appointment.IsNotCreatedInsuranceExist + applicationId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (apiResponse == null)
				return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

			return Json(new ResultModel { Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		[HttpGet]
		public async Task<IActionResult> PartialApplicationStatusHistory(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
				return NoContent();

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationApiResponse>>
				(ApiMethodName.Appointment.GetApplication + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Content(result.Message);

			var viewModel = new ApplicationStatusHistoryViewModel()
			{
				StatusHistories = apiResponse.Data.StatusHistories.Select(p => new ApplicationStatusHistoryViewModel.ApplicationStatusHistory()
				{
					Order = p.Order,
					Status = p.Status,
					NameSurname = p.NameSurname,
					ApplicationCreatedAt = apiResponse.Data.ApplicationTime,
					StatusDate = p.StatusDate
				}).ToList()
			};

			return PartialView("_ApplicationStatusHistory", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> AddCustomerCard(string encryptedId)
		{
			if (string.IsNullOrEmpty(encryptedId))
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var applicationId = encryptedId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<AddApiResponse>>
				(ApiMethodName.Management.AddCustomerCardFromApplication + applicationId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		[HttpPut]
		public async Task<IActionResult> UpdateInterviewInformation(string encryptedApplicationId, bool? isInterviewRequired, bool? isInterviewDone)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var applicationId = encryptedApplicationId.ToDecryptInt();

			var apiRequest = new ApplicationUpdateInterviewInformationApiRequest
			{
				EncryptedApplicationId = applicationId,
			};

			if (isInterviewRequired == null)
				apiRequest.IsInterviewRequired = 2;
			else if (isInterviewRequired == true)
				apiRequest.IsInterviewRequired = 1;
			else if (isInterviewRequired == false)
				apiRequest.IsInterviewRequired = 0;

			if (isInterviewDone == null)
				apiRequest.IsInterviewDone = 2;
			else if (isInterviewDone == true)
				apiRequest.IsInterviewDone = 1;
			else if (isInterviewDone == false)
				apiRequest.IsInterviewDone = 0;

			var apiResponse = await PortalHttpClientHelper
				.PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
				(apiRequest, ApiMethodName.Appointment.UpdateInterviewInformation, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		[HttpGet]
		public async Task<IActionResult> VisaRejectionRefundForNewApplication(string name, string surname, string birthDate, int genderId, string fatherName, string motherName)
		{
			var apiRequest = new VisaRejectionRefundForNewApplicationApiRequest
			{
				Name = name,
				Surname = surname,
				BirthDate = DateTime.Parse(birthDate, new CultureInfo(SiteResources.CultureTr)),
				GenderId = genderId,
				FatherName = fatherName,
				MotherName = motherName
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<VisaRejectionRefundForNewApplicationApiResponse>>
				(apiRequest, ApiMethodName.Appointment.VisaRejectionRefundForNewApplication, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (apiResponse == null)
				return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });

			return Json(new ResultModel { Data = apiResponse.Data, Message = $"{apiResponse.Data.Id} {nameof(SiteResources.VisaRejectionRefundApplicationNumberWarning).ToSiteResourcesValue(LanguageId)}", ResultType = ResultType.Success });
		}

		#region Report

		[HttpPost]
		public async Task<FileContentResult> GetApplicationReport(FilterApplicationViewModel filterViewModel)
		{
			var isAuthorizedForEarlyApplicationAuthorization = false;

			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object actions))
			{
				var roleActions = (RoleActionApiResponse)actions;

				isAuthorizedForEarlyApplicationAuthorization = roleActions.RoleActionSites.Where(r => UserSession.RoleIds.Contains(r.Role.Id))
					.Any(p => p.RoleActions.Any(q => (q.Action.ActionTranslations.Any(r => r.Name == SiteResources.IndiaEarlyApplicationAuthorization) || q.Action.Method == "IsAuthorizedForNepal") && q.Action.IsActive));
			}

			var reportName = SiteResources.FamilyApplicationReport.ToTitleCase();
			var apiRequest = new GetApplicationReportApiRequest
			{
				BranchIds = UserSession.BranchId != null ? UserSession.BranchId : -1,
				ApplicationNumber = filterViewModel.FilterApplicationNumber,
				CountryId = filterViewModel.FilterCountryId,
				AgencyId = filterViewModel.FilterAgencyId,
				ApplicantTypeId = filterViewModel.FilterApplicantTypeId,
				Name = filterViewModel.FilterName,
				Surname = filterViewModel.FilterSurname,
				PassportNumber = filterViewModel.FilterPassportNumber,
				NationalityId = filterViewModel.FilterNationalityId,
				Email = filterViewModel.FilterEmail,
				PhoneNumber = filterViewModel.FilterPhoneNumber,
				VisaCategoryId = filterViewModel.FilterVisaCategoryId,
				StartDate = filterViewModel.FilterStartDate,
				EndDate = filterViewModel.FilterEndDate,
				IgnoreEarlyApplications = isAuthorizedForEarlyApplicationAuthorization
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<ApplicationReportApiResponse>>
				(apiRequest, ApiMethodName.Appointment.GetApplicationReport, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			using (var workbook = new XLWorkbook())
			{
				var worksheet = workbook.Worksheets.Add(reportName);
				var currentRow = 1;

				worksheet.Cell(currentRow, 1).Value = SiteResources.AppointmentDate.ToTitleCase();
				worksheet.Cell(currentRow, 2).Value = SiteResources.PassportNumber.ToTitleCase();
				worksheet.Cell(currentRow, 3).Value = SiteResources.VisaNo.ToTitleCase();
				worksheet.Cell(currentRow, 4).Value = SiteResources.NameSurname.ToTitleCase();
				worksheet.Cell(currentRow, 5).Value = SiteResources.Nationality.ToTitleCase();
				worksheet.Cell(currentRow, 6).Value = SiteResources.BirthDate.ToTitleCase();
				worksheet.Cell(currentRow, 7).Value = SiteResources.PhoneNumber.ToTitleCase();
				worksheet.Cell(currentRow, 8).Value = SiteResources.Email.ToTitleCase();
				worksheet.Cell(currentRow, 9).Value = SiteResources.VisaFee.ToTitleCase();
				worksheet.Cell(currentRow, 10).Value = SiteResources.CreatedBy.ToTitleCase();

				var rangeTableHeader = worksheet.Range(worksheet.Cell(currentRow, 1), worksheet.Cell(currentRow, 10));
				rangeTableHeader.Style.Font.SetBold();
				currentRow++;

				for (int i = 0; i < apiResponse.Data.Appointments.Count(); i++)
				{
					var data = apiResponse.Data.Appointments.ElementAt(i);
					worksheet.Cell(currentRow, 1).Value = data.ApplicationDate.Date;
					worksheet.Cell(currentRow, 2).Value = "'" + data.PassportNumber;
					worksheet.Cell(currentRow, 2).DataType = XLDataType.Text;
					worksheet.Cell(currentRow, 3).Value = data.VisaNo;
					worksheet.Cell(currentRow, 4).Value = $"{data.Name} {data.Surname}";
					worksheet.Cell(currentRow, 5).Value = data.Nationality;
					worksheet.Cell(currentRow, 6).Value = data.BirthDate;
					worksheet.Cell(currentRow, 7).Value = "'" + data.PhoneNumber;
					worksheet.Cell(currentRow, 7).DataType = XLDataType.Text;
					worksheet.Cell(currentRow, 8).Value = data.Email;
					worksheet.Cell(currentRow, 9).Value = data.VisaPrice.ToString() + " " + (CurrencyType)data.Currency;
					worksheet.Cell(currentRow, 10).Value = data.CreatedByNameAndSurname;

					currentRow++;
				}

				var rangeTable = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, 10));
				rangeTable.Style.Border.SetTopBorder(XLBorderStyleValues.Thin);
				rangeTable.Style.Border.SetBottomBorder(XLBorderStyleValues.Thin);
				rangeTable.Style.Border.SetLeftBorder(XLBorderStyleValues.Thin);
				rangeTable.Style.Border.SetRightBorder(XLBorderStyleValues.Thin);
				rangeTable.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);
				rangeTable.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

				var rangeAlignment = worksheet.Range(worksheet.Cell(1, 1), worksheet.Cell(currentRow - 1, 10));
				rangeAlignment.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);

				for (int i = 1; i <= 10; i++)
				{
					worksheet.Column(i).AdjustToContents();
				}

				using (var stream = new MemoryStream())
				{
					workbook.SaveAs(stream);
					var content = stream.ToArray();
					return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{reportName} - {DateTime.UtcNow.ToString("ddMMyyyy")}.xlsx");
				}
			}
		}

		#endregion

		#region UpdateStatus

		[ActionAttribute(IsMenuItem = true)]
		public async Task<IActionResult> UpdateStatus()
		{
			if (UserSession.BranchId == null)
			{
				return RedirectToAction("SelectModuleBranch", "User", new { Area = "" });
			}

			BranchApiResponse branch = new BranchApiResponse();
			if (MemoryCache.TryGetValue(CacheKeys.BranchCache, out object value))
			{
				var cacheItem = (BranchesApiResponse)value;
				branch = cacheItem.Branches.FirstOrDefault(p => p.Id == UserSession.BranchId.GetValueOrDefault());
			}

			var viewModel = new FilterUpdateApplicationCurrentStatusViewModel()
			{
				EncryptedBranchId = UserSession.BranchId.Value.ToEncrypt(),
				IsCargoIntegrationActive = branch.IsCargoIntegrationActive,
				CallingCode = branch.Country.CallingCode,
				BranchCountryId = branch.CountryId
			};

			return View(viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> UpdateStatus([DataSourceRequest] DataSourceRequest request,
			[Bind(Prefix = "models")] IEnumerable<UpdateApplicationCurrentStatusViewModel> applications)
		{
			if (applications != null && ModelState.IsValid)
			{
				var apiRequest = new UpdateApplicationsCurrentStatusApiRequest()
				{
					UserId = UserSession.UserId,
					BranchId = UserSession.BranchId ?? 0,
					SendNotification = AppSettings.Notification,
					CargoWarningControl = false,
					ApplicationCurrentStatus = applications
												.Where(p => p.StatusId != p.CurrentStatus.Id)
												.Select(p => new UpdateApplicationCurrentStatusApiRequest()
												{
													ApplicationId = p.EncryptedId.ToDecryptInt(),
													ApplicationStatusId = p.CurrentStatus.Id
												}).ToList(),
					IdListForRejectionRefundDoneControl = applications.Where(a => a.StatusId != a.CurrentStatus.Id && a.CurrentStatus.Id == (int)ApplicationStatusType.RejectionRefundDone).Select(s => s.EncryptedId.ToDecryptInt()).ToList(),
					IdListForRejectionApprovalControl = applications.Where(a => a.StatusId != a.CurrentStatus.Id && a.HasApplicationApprovalExist && a.CurrentStatus.Id == (int)ApplicationStatusType.RejectionRefundDone).Select(s => s.EncryptedId.ToDecryptInt()).ToList()
				};

				var apiResponse = await PortalHttpClientHelper
										.PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
										(apiRequest, ApiMethodName.Appointment.UpdateApplicationCurrentStatus, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
										.ConfigureAwait(false);

				if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
					return Json(result);

				if (!string.IsNullOrEmpty(apiResponse.Data.CheckRejectionRefundDoneControlMessage))
				{
					applications.ToList()[0].CheckRejectionRefundDoneControlMessage = apiResponse.Data.CheckRejectionRefundDoneControlMessage;
					return Json(applications.ToDataSourceResult(request, ModelState));
				}

				if (!string.IsNullOrEmpty(apiResponse.Data.CheckRejectionApprovalControlMessage))
				{
					applications.ToList()[0].CheckRejectionApprovalControlMessage = apiResponse.Data.CheckRejectionApprovalControlMessage;
					return Json(applications.ToDataSourceResult(request, ModelState));
				}

				if (!apiResponse.Data.CheckPreviousRefundIsDoneMessage.Equals(""))
				{
					applications.ToList()[0].CheckPreviousRefundIsDoneMessage = apiResponse.Data.CheckPreviousRefundIsDoneMessage;
				}

                if (!apiResponse.Data.CheckTurkmenRejectionRefundDoneControlMessage.Equals(""))
                {
                    applications.ToList()[0].CheckRejectionApprovalControlMessage = apiResponse.Data.CheckTurkmenRejectionRefundDoneControlMessage;
                }

                if (apiResponse.Data.NotUpdatedInsuranceRefundIds.Any())
				{
                    foreach (var app in applications)
                    {
						if(apiResponse.Data.NotUpdatedInsuranceRefundIds.Any(s => s.Key == app.EncryptedId.ToDecryptInt()))
						{
                            string insuranceExist = "";

                            var groupedData = apiResponse.Data.NotUpdatedInsuranceRefundIds.GroupBy(s => s.Value.Type);

                            foreach (var data in groupedData)
                            {
                                foreach (var dataSet in data)
                                {
                                    insuranceExist += $"{dataSet.Key} ";
                                }

                                if (LanguageId == 1)
                                {
                                    insuranceExist += $" numaralı başvuruda {GetInsurancePolicyVisaMessage(data.Key)} {data.First().Value.Date.ToString("dd.MM.yyyy")} sonrasında Vize Ret İadesi yapılmamaktadır. \r\n";
                                    app.CheckInsuranceRefundBlockExistMessage = insuranceExist;
                                }
                                else
                                {
                                    insuranceExist += $" Visa fee refunds will not be made for applications after {data.First().Value.Date.ToString("dd.MM.yyyy").ConvertToFormattedDate()} for {GetInsurancePolicyVisaMessage(data.Key)} \r\n";
                                    app.CheckInsuranceRefundBlockExistMessage = insuranceExist;
                                }
                            }
                        }
                    }
                    return Json(applications.ToDataSourceResult(request, ModelState));
                }

				if (apiResponse.Data.NotUpdatedCargoIds.Any())
				{
					foreach (var app in applications)
					{
						string CargoExist = "";
						if (LanguageId == 1)
						{
							foreach (var id in apiResponse.Data.NotUpdatedCargoIds)
							{
								if (id == app.EncryptedId.ToDecryptInt())
								{
									CargoExist = app.ApplicationId.ToString() + " numaralı başvuruda kargo ücreti bulunmamaktadır,bu nedenle Kargoya Teslim Edildi statüsünü seçemezsiniz.";
									app.CargoExist = CargoExist;
								}
							}
						}
						else
						{
							foreach (var id in apiResponse.Data.NotUpdatedCargoIds)
							{
								if (id == app.EncryptedId.ToDecryptInt())
								{
									CargoExist = "There is no cargo fee in the application number " + app.ApplicationId.ToString() + ",so you cannot select the status Outscan to Courrier.";
									app.CargoExist = CargoExist;
								}
							}
						}
					}
					return Json(applications.ToDataSourceResult(request, ModelState));
				}

				if (apiResponse.Data.NonTurkmenNationIhbStatusIds.Any())
				{
					foreach (var application in applications)
					{
						var turkmenNationControlMessage = "";

						foreach (var id in apiResponse.Data.NonTurkmenNationIhbStatusIds.Where(id => id == application.EncryptedId.ToDecryptInt()))
						{
							turkmenNationControlMessage = $"{SiteResources.IHBUploadedNotAvailableForThisApplication} ({id})";
							application.CheckNonTurkmenNationMessage = turkmenNationControlMessage;
						}
					}

                    return Json(applications.ToDataSourceResult(request, ModelState));
                }

                //minio 
            }

			return Json(applications.ToDataSourceResult(request, ModelState));
		}

		[HttpPost]
		public async Task<IActionResult> UpdateStatusWithBarcodeReader(Dictionary<string, string> request)
		{
			if (request != null && ModelState.IsValid)
			{
				var apiRequest = new UpdateApplicationsCurrentStatusApiRequest()
				{
					UserId = UserSession.UserId,
					BranchId = UserSession.BranchId ?? 0,
					SendNotification = AppSettings.Notification,
					CargoWarningControl = false,
					ApplicationCurrentStatus = request
												.Select(p => new UpdateApplicationCurrentStatusApiRequest()
												{
													ApplicationId = p.Key.Split('-').Last().ToInt(),
													ApplicationStatusId = p.Value.ToInt()
												}).ToList(),
					IdListForRejectionRefundDoneControl = request.Where(r => r.Value.ToInt() == (int)ApplicationStatusType.RejectionRefundDone).Select(s => s.Key.Split('-').Last().ToInt()).ToList(),
					IdListForRejectionApprovalControl = request.Where(r => r.Value.ToInt() == (int)ApplicationStatusType.RejectionRefundDone).Select(s => s.Key.Split('-').Last().ToInt()).ToList()
				};

				var apiResponse = await PortalHttpClientHelper
										.PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
										(apiRequest, ApiMethodName.Appointment.UpdateApplicationCurrentStatus, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
										.ConfigureAwait(false);

				await apiResponse.Validate(out ResultModel result).ConfigureAwait(false);

				if (!string.IsNullOrEmpty(apiResponse.Data.CheckRejectionApprovalControlMessage))
				{
					return Json(new ResultModel { Message = apiResponse.Data.CheckRejectionApprovalControlMessage, ResultType = ResultType.Warning });
				}

				if (!string.IsNullOrEmpty(apiResponse.Data.CheckRejectionRefundDoneControlMessage))
				{
					return Json(new ResultModel { Message = apiResponse.Data.CheckRejectionRefundDoneControlMessage, ResultType = ResultType.Warning });
				}

				if (apiResponse.Data.NotUpdatedCargoIds.Any())
				{
					string CargoExist = "There is no cargo fee in the application number ";
					foreach (var id in apiResponse.Data.NotUpdatedCargoIds)
					{
						CargoExist += $"{id} ";
					}
					CargoExist += ",so you cannot select the status Outscan to Courrier.";
					return Json(new ResultModel { Message = CargoExist, ResultType = ResultType.Warning });
				}

                else if (apiResponse.Data.NotUpdatedInsuranceRefundIds.Any())
                {
                    var insuranceExist = new StringBuilder();

                    var groupedData = apiResponse.Data.NotUpdatedInsuranceRefundIds.GroupBy(s => s.Value.Type);

                    foreach (var data in groupedData)
                    {
                        foreach (var dataSet in data)
                        {
                            insuranceExist.Append($"{dataSet.Key} ");
                        }

                        if (LanguageId == 1)
                        {
                            insuranceExist.Append($" numaralı başvuruda {GetInsurancePolicyVisaMessage(data.Key)} {data.First().Value.Date.ToString("dd.MM.yyyy")} sonrasında Vize Ret İadesi yapılmamaktadır.");
                            insuranceExist.Append("<br />");
                        }
                        else
                        {
                            insuranceExist.Append($" Visa fee refunds will not be made for applications after {data.First().Value.Date.ToString("dd.MM.yyyy").ConvertToFormattedDate()} for {GetInsurancePolicyVisaMessage(data.Key)}");
                            insuranceExist.Append("<br />");
                        }
                    }

                    if (insuranceExist.ToString() == string.Empty)
                        return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
                    else
                        return Json(new ResultModel { Message = insuranceExist.ToString(), ResultType = ResultType.Warning });
                }

                if (apiResponse.Data.NonTurkmenNationIhbStatusIds.Any())
				{
					var ids = apiResponse.Data.NonTurkmenNationIhbStatusIds;

					var turkmenNationControlMessage = $"{SiteResources.IHBUploadedNotAvailableForThisApplication} - ";

					turkmenNationControlMessage += ids[0];

					ids.Remove(ids[0]);

					turkmenNationControlMessage = ids.Aggregate(turkmenNationControlMessage, (current, id) => current + $", {id}");

					return Json(new ResultModel { Message = turkmenNationControlMessage, ResultType = ResultType.Warning });
				}

                if (!string.IsNullOrEmpty(apiResponse.Data.CheckTurkmenRejectionRefundDoneControlMessage))
                {
                    return Json(new ResultModel { Message = apiResponse.Data.CheckTurkmenRejectionRefundDoneControlMessage, ResultType = ResultType.Warning });
                }

                return Json(result);
			}
			return Json(null);
		}

		public IActionResult GetApplicationStatusOrders(int applicationStatusId)
		{
			if (MemoryCache.TryGetValue(CacheKeys.ApplicationStatusOrderCache, out object value))
			{
				var cacheItem = (ApplicationStatusOrdersApiResponse)value;

				return Json(cacheItem.ApplicationStatusOrders.FirstOrDefault(p => p.ApplicationStatusId == applicationStatusId)
									.ApplcationStatusOrders
									.Where(p => p.AvailableBranchIds.Any(x => x == UserSession.BranchId.Value))
									.Select(p => new ApplicationCurrentStatusViewModel()
									{
										Id = p.IdName.Id,
										Name = p.IdName.NameTranslation.Any(q => q.LanguageId == LanguageId) ?
											p.IdName.NameTranslation.First(q => q.LanguageId == LanguageId).Name :
											p.IdName.NameTranslation.FirstOrDefault().Name
									}));
			}

			return Json(null);
		}

		[HttpGet]
		public async Task<IActionResult> GetApplicationStatusOrdersByApplicationId(int applicationId)
		{
			if (MemoryCache.TryGetValue(CacheKeys.ApplicationStatusOrderCache, out object value))
			{
				var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationApiResponse>>
				(ApiMethodName.Appointment.GetApplication + applicationId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

				if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
					return Json(null);

				if (apiResponse.Data.BranchId != UserSession.BranchId)
					return Json(SiteResources.ApplicationNotAppliedFromBranch);

				var cacheItem = (ApplicationStatusOrdersApiResponse)value;

				var applicationStatus = cacheItem.ApplicationStatusOrders.FirstOrDefault(p => p.ApplicationStatusId == apiResponse.Data.ApplicationStatusId);

				return Json(new BarcodeUpdateApplicationCurrentStatusViewModel()
				{
					ApplicationId = apiResponse.Data.Id,
					NameSurname = $"{apiResponse.Data.Name} {apiResponse.Data.Surname}",
					CurrentStatusName = applicationStatus.NameTranslation.Any(q => q.LanguageId == LanguageId) ?
											applicationStatus.NameTranslation.First(q => q.LanguageId == LanguageId).Name :
											applicationStatus.NameTranslation.FirstOrDefault().Name,
					StatusOrder = applicationStatus.ApplcationStatusOrders
									.Where(p => p.AvailableBranchIds.Any(x => x == UserSession.BranchId.Value))
									.Select(p => new ApplicationCurrentStatusViewModel()
									{
										Id = p.IdName.Id,
										Name = p.IdName.NameTranslation.Any(q => q.LanguageId == LanguageId) ?
											p.IdName.NameTranslation.First(q => q.LanguageId == LanguageId).Name :
											p.IdName.NameTranslation.FirstOrDefault().Name
									}),
					IsCancelled = apiResponse.Data.StatusId == (int)ApplicationStatus.Cancelled
				});
			}
			return Json(null);
		}

		public async Task<IActionResult> GetPaginatedApplicationsUpdateStatus([DataSourceRequest] DataSourceRequest request, FilterUpdateApplicationCurrentStatusViewModel filterViewModel)
		{
			var paginationFilter = request.GetPaginationFilter(filterViewModel);

			var isAuthorizedForEarlyApplicationAuthorization = false;

			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object actions))
			{
				var roleActions = (RoleActionApiResponse)actions;

				isAuthorizedForEarlyApplicationAuthorization = roleActions.RoleActionSites.Where(r => UserSession.RoleIds.Contains(r.Role.Id))
					.Any(p => p.RoleActions.Any(q => (q.Action.ActionTranslations.Any(r => r.Name == SiteResources.IndiaEarlyApplicationAuthorization) || q.Action.Method == "IsAuthorizedForNepal") && q.Action.IsActive));
			}

			var apiRequest = new PaginatedApplicationsApiRequest
			{
				BranchIds = new List<int>() { UserSession.BranchId.Value },
				ApplicationStatusId = filterViewModel.FilterApplicationStatusId,
				StartDate = filterViewModel.FilterStartDate,
				EndDate = filterViewModel.FilterEndDate,
				ApplicationId = filterViewModel.FilterApplicationId,
				PassportNumber = !string.IsNullOrEmpty(filterViewModel.FilterPassportNumber) ? filterViewModel.FilterPassportNumber : string.Empty,
				PhoneNumber1 = !string.IsNullOrEmpty(filterViewModel.FilterPhoneNumber) ? filterViewModel.FilterPhoneNumber : string.Empty,
				Email = !string.IsNullOrEmpty(filterViewModel.FilterEmail) ? filterViewModel.FilterEmail : string.Empty,
				ApplicationTypeIds = new List<int>()
				{
					ApplicationType.Normal.ToInt(),
					ApplicationType.Free.ToInt(),
					ApplicationType.Turquois.ToInt(),
					ApplicationType.TurquoisPremium.ToInt(),
					ApplicationType.TurquoisGratis.ToInt(),
					ApplicationType.TurquoisWithGratisNote.ToInt(),
				},
				IngonreCancelledApplications = true,
				Pagination = new PaginationApiRequest
				{
					Page = paginationFilter.Page,
					PageSize = paginationFilter.PageSize,
					OrderBy = paginationFilter.OrderBy,
					SortDirection = paginationFilter.SortDirection
				},
				IgnoreEarlyApplications = isAuthorizedForEarlyApplicationAuthorization,
				IncludeApplicationStatusHistories = true
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationsApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.GetSanitizedPaginatedApplicationsForScanSycle, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var paginatedData = new List<UpdateApplicationCurrentStatusViewModel>();

			if (apiResponse.Data != null && apiResponse.Data.Items.Any())
			{
				paginatedData = apiResponse.Data.Items.First().Applications
					.Select(p => new UpdateApplicationCurrentStatusViewModel
					{
						EncryptedId = p.Id.ToEncrypt(),
						ApplicationId = p.Id.ToApplicationNumber(),
						NameSurname = $"{p.Name} {p.Surname}",
						Passport = p.PassportNumber,
						Email = p.Email,
						PhoneNumber1 = p.PhoneNumber1,
						StatusId = p.ApplicationStatusId,
						ApplicationTime = p.ApplicationTime,
						Contact = $"{p.Email} / {p.PhoneNumber1}",
						HasApplicationApprovalExist = p.HasApplicationApprovalExist,
                        HasPolicyRefundBlock = p.HasPolicyRefundBlock.GetValueOrDefault(),
						InsuranceRefundBlockExistMessage = GetInsuranceRefundMessageForScanSycle(p.PolicyRefundBlockType.GetValueOrDefault(), p.PolicyRefundBlockDate),
                        CurrentStatus = new ApplicationCurrentStatusViewModel()
						{
							Id = p.ApplicationStatusId
						},
						ApplicationFilesUpload = p.RejectionReturnStatementFileUpload && p.RejectionDataPageFileUpload && p.RejectionPassportFileUpload ? "white" : (!p.RejectionReturnStatementFileUpload && !p.RejectionDataPageFileUpload && !p.RejectionPassportFileUpload ? "red" : "yellow"),
						CanUpdateToRejectionRefundStatus = 
                            p.Document?.VisaCategoryId == (int)VisaCategoryTypeEnum.TouristicResidencePermit 
                            || p.Document?.VisaCategoryId == (int)VisaCategoryTypeEnum.VisitSpecialResidancePermit
							? SiteResources.ScanCycleRefundInformationContent : string.Empty 
                    }).ToList();
			}

			if (MemoryCache.TryGetValue(CacheKeys.ApplicationStatusOrderCache, out object value))
			{
				var cacheItem = (ApplicationStatusOrdersApiResponse)value;

				await paginatedData.ForEachAsync(async item =>
				{
					var currentStatus = cacheItem.ApplicationStatusOrders
													.FirstOrDefault(p => p.ApplicationStatusId == item.CurrentStatus.Id);

					if (currentStatus != null)
					{
						item.CurrentStatus.Id = currentStatus.ApplicationStatusId;
						item.CurrentStatus.Name = currentStatus.NameTranslation.Any(q => q.LanguageId == LanguageId) ?
													currentStatus.NameTranslation.First(q => q.LanguageId == LanguageId).Name :
													currentStatus.NameTranslation.FirstOrDefault().Name;
					}
				});
			}

			return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
		}

		[HttpPost]
		public async Task<IActionResult> PartialUpdateAllStatus(int statusId, DateTime startDate, DateTime endDate, int applicationCount, string encryptedBranchId)
		{
			if (MemoryCache.TryGetValue(CacheKeys.ApplicationStatusOrderCache, out object value))
			{
				var cacheItem = (ApplicationStatusOrdersApiResponse)value;

				return PartialView("_UpdateAllStatus", new UpdateAllStatusViewModel()
				{
					EncryptedBranchId = encryptedBranchId,
					TotalApplicationCount = applicationCount,
					StartDate = startDate,
					EndDate = endDate,
					CurrentStatusId = statusId,
					CurrentStatusName = cacheItem.ApplicationStatusOrders.FirstOrDefault(p => p.ApplicationStatusId == statusId).NameTranslation.Any(q => q.LanguageId == LanguageId) ?
											cacheItem.ApplicationStatusOrders.FirstOrDefault(p => p.ApplicationStatusId == statusId).NameTranslation.First(q => q.LanguageId == LanguageId).Name :
											cacheItem.ApplicationStatusOrders.FirstOrDefault(p => p.ApplicationStatusId == statusId).NameTranslation.FirstOrDefault().Name,
					Status = cacheItem.ApplicationStatusOrders.FirstOrDefault(p => p.ApplicationStatusId == statusId)
									.ApplcationStatusOrders
									.Where(p => p.AvailableBranchIds.Any(x => x == UserSession.BranchId.Value))
									.Select(p => new SelectListItem()
									{
										Value = p.IdName.Id.ToString(),
										Text = p.IdName.NameTranslation.Any(q => q.LanguageId == LanguageId) ?
											p.IdName.NameTranslation.First(q => q.LanguageId == LanguageId).Name :
											p.IdName.NameTranslation.FirstOrDefault().Name
									}).ToList()
				});
			}
			else
			{
				return Content(EnumResources.MissingOrInvalidData);
			}
		}

		[HttpPost]
		public async Task<IActionResult> UpdateAllStatus(UpdateAllStatusViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var apiRequest = new UpdateAllApplicationsCurrentStatusApiRequest
			{
				BranchId = viewModel.EncryptedBranchId.ToDecryptInt(),
				StartDate = viewModel.StartDate,
				EndDate = viewModel.EndDate,
				CurrentApplicationStatusId = viewModel.CurrentStatusId,
				ApplicationStatusId = viewModel.StatusId,
				UserId = UserSession.UserId,
				SendNotification = AppSettings.Notification
			};

			var apiResponse = await PortalHttpClientHelper
				.PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
				(apiRequest, ApiMethodName.Appointment.UpdateAllApplicationCurrentStatus, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			if (!string.IsNullOrEmpty(apiResponse.Data.CheckRejectionApprovalControlMessage))
			{
				return Json(new ResultModel { Message = apiResponse.Data.CheckRejectionApprovalControlMessage, ResultType = ResultType.Warning });
			}

			if (apiResponse.Data.NotUpdatedCargoIds.Any())
			{
				string ids = "";
				if (LanguageId == 1)
				{
					foreach (var id in apiResponse.Data.NotUpdatedCargoIds)
					{
						ids += $"{id} ";
					}
					ids += " numaralı başvuruda kargo ücreti bulunmamaktadır.";
				}
				else
				{
					ids = "There is no cargo fee in the application number ";
					foreach (var id in apiResponse.Data.NotUpdatedCargoIds)
					{
						ids += $"{id} ";
					}
					ids += ".";
				}
				return Json(new ResultModel { Message = ids, ResultType = ResultType.Warning });
			}
            else if (apiResponse.Data.NotUpdatedInsuranceRefundIds?.Any() == true)
            {
                var insuranceExist = new StringBuilder();

                var groupedData = apiResponse.Data.NotUpdatedInsuranceRefundIds.GroupBy(s => s.Value.Type);

                foreach (var data in groupedData)
                {
                    foreach (var dataSet in data)
                    {
                        insuranceExist.Append($"{dataSet.Key} ");
                    }

                    if (LanguageId == 1)
                    {
                        insuranceExist.Append($" numaralı başvuruda {GetInsurancePolicyVisaMessage(data.Key)} {data.First().Value.Date.ToString("dd.MM.yyyy")} sonrasında Vize Ret İadesi yapılmamaktadır.");
                        insuranceExist.Append("<br />");
                    }
                    else
                    {
                        insuranceExist.Append($" Visa fee refunds will not be made for applications after {data.First().Value.Date.ToString("dd.MM.yyyy").ConvertToFormattedDate()} for {GetInsurancePolicyVisaMessage(data.Key)}");
                        insuranceExist.Append("<br />");
                    }
                }

				if(insuranceExist.ToString() == string.Empty)
                    return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
                else
                    return Json(new ResultModel { Message = insuranceExist.ToString(), ResultType = ResultType.Warning });
            }
            else if (apiResponse.Data.NonTurkmenNationIhbStatusIds.Any())
			{
				var ids = apiResponse.Data.NonTurkmenNationIhbStatusIds;

				var turkmenNationControlMessage = $"{SiteResources.IHBUploadedNotAvailableForThisApplication} - ";

				turkmenNationControlMessage += ids[0];

				ids.Remove(ids[0]);

				turkmenNationControlMessage = ids.Aggregate(turkmenNationControlMessage, (current, id) => current + $", {id}");

				return Json(new ResultModel { Message = turkmenNationControlMessage, ResultType = ResultType.Warning });
			}
            else if (apiResponse.Data.NotUpdatedTurkmenVisaRejectionRefundDoneIds.Any() || !string.IsNullOrEmpty(apiResponse.Data.CheckTurkmenRejectionRefundDoneControlMessage))
            {
                var message = apiResponse.Data.CheckTurkmenRejectionRefundDoneControlMessage;
                return Json(new ResultModel { Message = message, ResultType = ResultType.Warning });
            }
            else
				return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		[HttpPost]
		public IActionResult PartialUpdateSelectedStatus(UpdateSelectedStatusViewModel request)
		{
			if (MemoryCache.TryGetValue(CacheKeys.ApplicationStatusOrderCache, out object value))
			{
				var cacheItem = (ApplicationStatusOrdersApiResponse)value;

				request.Status = cacheItem.ApplicationStatusOrders.FirstOrDefault(p => p.ApplicationStatusId == request.CurrentStatusId)
									.ApplcationStatusOrders
									.Where(p => p.AvailableBranchIds.Any(x => x == UserSession.BranchId.Value))
									.Select(p => new SelectListItem()
									{
										Value = p.IdName.Id.ToString(),
										Text = p.IdName.NameTranslation.Any(q => q.LanguageId == LanguageId) ?
											p.IdName.NameTranslation.First(q => q.LanguageId == LanguageId).Name :
											p.IdName.NameTranslation.FirstOrDefault().Name
									}).ToList();

				request.TotalApplicationCount = request.EncryptedIdList.Count();
				request.CurrentStatusName = cacheItem.ApplicationStatusOrders.FirstOrDefault(p => p.ApplicationStatusId == request.CurrentStatusId).NameTranslation.Any(q => q.LanguageId == LanguageId) ?
											cacheItem.ApplicationStatusOrders.FirstOrDefault(p => p.ApplicationStatusId == request.CurrentStatusId).NameTranslation.First(q => q.LanguageId == LanguageId).Name :
											cacheItem.ApplicationStatusOrders.FirstOrDefault(p => p.ApplicationStatusId == request.CurrentStatusId).NameTranslation.FirstOrDefault().Name;

				return PartialView("_UpdateSelectedStatus", request);
			}

			return Content(EnumResources.MissingOrInvalidData);
		}

		[HttpPost]
		public async Task<IActionResult> UpdateSelectedStatus(UpdateSelectedStatusViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			if (UserSession.BranchId == null)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var apiRequest = new UpdateSelectedApplicationsCurrentStatusApiRequest
			{
				BranchId = UserSession.BranchId.Value,
				CurrentApplicationStatusId = viewModel.CurrentStatusId,
				IdList = viewModel.EncryptedIdList.Select(q => q.ToDecryptInt()).ToList(),
				ApplicationStatusId = viewModel.StatusId,
				UserId = UserSession.UserId,
				SendNotification = AppSettings.Notification,
				StartDate = viewModel.StartDate,
				EndDate = viewModel.EndDate,
			};

			var apiResponse = await PortalHttpClientHelper
				.PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
				(apiRequest, ApiMethodName.Appointment.UpdateSelectedApplicationsCurrentStatus, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			if (!string.IsNullOrEmpty(apiResponse.Data.CheckRejectionApprovalControlMessage))
			{
				return Json(new ResultModel { Message = apiResponse.Data.CheckRejectionApprovalControlMessage, ResultType = ResultType.Warning });
			}

			if (apiResponse.Data.NotUpdatedCargoIds.Any())
			{
				string ids = "";
				if (LanguageId == 1)
				{
					foreach (var id in apiResponse.Data.NotUpdatedCargoIds)
					{
						ids += $"{id} ";
					}
					ids += " numaralı başvuruda kargo ücreti bulunmamaktadır.";
				}
				else
				{
					ids = "There is no cargo fee in the application number ";
					foreach (var id in apiResponse.Data.NotUpdatedCargoIds)
					{
						ids += $"{id} ";
					}
					ids += ".";
				}
				return Json(new ResultModel { Message = ids, ResultType = ResultType.Warning });
			}
            else if (apiResponse.Data.NotUpdatedInsuranceRefundIds.Any())
            {
                var insuranceExist = new StringBuilder();

                var groupedData = apiResponse.Data.NotUpdatedInsuranceRefundIds.GroupBy(s => s.Value.Type);

                foreach (var data in groupedData)
                {
                    foreach (var dataSet in data)
                    {
                        insuranceExist.Append($"{dataSet.Key} ");
                    }

                    if (LanguageId == 1)
                    {
                        insuranceExist.Append($" numaralı başvuruda {GetInsurancePolicyVisaMessage(data.Key)} {data.First().Value.Date.ToString("dd.MM.yyyy")} sonrasında Vize Ret İadesi yapılmamaktadır.");
                        insuranceExist.Append("<br />");
                    }
                    else
                    {
                        insuranceExist.Append($" Visa fee refunds will not be made for applications after {data.First().Value.Date.ToString("dd.MM.yyyy").ConvertToFormattedDate()} for {GetInsurancePolicyVisaMessage(data.Key)}");
                        insuranceExist.Append("<br />");
                    }
                }

                if (insuranceExist.ToString() == string.Empty)
                    return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
                else
                    return Json(new ResultModel { Message = insuranceExist.ToString(), ResultType = ResultType.Warning });
            }
            else if (apiResponse.Data.NotUpdatedRejectionRefundDoneIds.Any())
			{
				var message = new StringBuilder();
				message.AppendLine(SiteResources.RejectionRefundDoneCanNotBeSelected);
				foreach (var id in apiResponse.Data.NotUpdatedRejectionRefundDoneIds)
				{
					message.AppendLine(id.ToString());
				}
				return Json(new ResultModel { Message = message.ToString(), ResultType = ResultType.Warning });
			}
			else if (apiResponse.Data.NonTurkmenNationIhbStatusIds.Any())
			{
				var ids = apiResponse.Data.NonTurkmenNationIhbStatusIds;

				var turkmenNationControlMessage = $"{SiteResources.IHBUploadedNotAvailableForThisApplication} - ";

				turkmenNationControlMessage += ids[0];

				ids.Remove(ids[0]);

				turkmenNationControlMessage = ids.Aggregate(turkmenNationControlMessage, (current, id) => current + $", {id}");

				return Json(new ResultModel { Message = turkmenNationControlMessage, ResultType = ResultType.Warning });
			}
            else if (apiResponse.Data.NotUpdatedTurkmenVisaRejectionRefundDoneIds.Any())
            {
                var message = apiResponse.Data.CheckTurkmenRejectionRefundDoneControlMessage;
                return Json(new ResultModel { Message = message.ToString(), ResultType = ResultType.Warning });
            }
            else
				return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region SAP

		[HttpGet]
		public async Task<IActionResult> CreateOrder(string encryptedApplicationId)
		{
			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<AddApiResponse>>
				(ApiMethodName.Accounting.CreateOrder + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}
		public async Task<IActionResult> SendVisaRejection(string encryptedApplicationId, string gatewayServicePriceText, string gatewayServiceFeeCurrency)
		{
			int applicationId = encryptedApplicationId.ToDecryptInt();

			var apiResponseGetApplication = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationApiResponse>>
				(ApiMethodName.Appointment.GetApplication + applicationId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);
			if (apiResponseGetApplication.Data.IsSapApplicationOrderExists)
			{
				if (apiResponseGetApplication.Data.Insurance != null && apiResponseGetApplication.Data.Insurance.Number != null)
				{
					Decimal? gatewayServicePrice = Convert.ToDecimal(gatewayServicePriceText != null ? gatewayServicePriceText.Replace('.', ',') : "0");

					var apiResponse = await PortalHttpClientHelper
						.GetAsync<ApiResponse<ApplicationVisaRejectionResponse>>
						($"{ApiMethodName.Appointment.GetSendVisaRejectionApplication + applicationId}/{gatewayServicePrice}/{gatewayServiceFeeCurrency}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
						.ConfigureAwait(false);

					if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
						return Json(result);
					else
					{
						var apiRequest = new SendVisaRejectionSapApiModelRequest
						{
							ApplicationId = applicationId,
							Bukrs = apiResponse.Data.BranchCode,
							BasvSubeKod = apiResponse.Data.SapBranchId,
							CajoNumber = apiResponse.Data.CashNumber,
							Land1 = apiResponse.Data.BranchCountryCode,
							Pasno = apiResponse.Data.PassportNumber,
							Polno = apiResponse.Data.PolicyNumber,
							Poltr = apiResponse.Data.PolicyDate.ToString("dd.MM.yyyy"),
							Adsoy = apiResponse.Data.Name + " " + apiResponse.Data.Surname,
							Tutar = apiResponse.Data.RejectionAmount,
							Parab = apiResponse.Data.Currency
						};

						var apiResponseOfSendVisaRejection = await PortalHttpClientHelper
							.PostAsJsonAsync<ApiResponse<SendVisaRejectionServiceResponse>>
							(apiRequest, ApiMethodName.Accounting.SendVisaRejectionSap, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
							.ConfigureAwait(false);


						if (!await apiResponseOfSendVisaRejection.Validate(out ResultModel resultOfSendVisaRejection).ConfigureAwait(false))
							return Json(resultOfSendVisaRejection);

						if (!apiResponseOfSendVisaRejection.IsSuccess)
							return Json(new ResultModel { Message = apiResponseOfSendVisaRejection.Message, ResultType = ResultType.Warning });

						return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
					}
				}
				else
				{
					return Json(new ResultModel { Message = SiteResources.ErrorOpenedBeforeInsurance, ResultType = ResultType.Danger });
				}

			}

			else
			{
				return Json(new ResultModel { Message = SiteResources.ErrorOpenedBeforeSap, ResultType = ResultType.Danger });
			}
		}

		#endregion

		#region Insurance

		public async Task<IActionResult> GetNllChechByPassportNumberAndNationalty(int nationalityId, string passportNumber)
		{

			var apiRequest = new GetNllCheckByPassportNumberNationaltyApiRequest
			{
				NationalityId = nationalityId,
				PassportNumber = passportNumber,
				BranchId = UserSession.BranchId
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(apiRequest, ApiMethodName.Appointment.GetNllChechByPassportNumberAndNationalty, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurred });

			if (apiResponse.Data != null && apiResponse.Data.Id != -1)
			{

				return Json(new { Result = true, Message = $"{SiteResources.PreviousApplicationUnrealNll})" });
			}
			else
				return Json(new { Result = false });
		}

		public async Task<IActionResult> GetRejectionRefundDoneChechByPassportNumberAndNationalty(int nationalityId, string passportNumber)
		{
			var apiRequest = new GetNllCheckByPassportNumberNationaltyApiRequest
			{
				NationalityId = nationalityId,
				PassportNumber = passportNumber,
				BranchId = UserSession.BranchId
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<ApplicationChechRefundDoneResponse>>
				(apiRequest, ApiMethodName.Appointment.GetRejectionRefundDoneChechByPassportNumberAndNationalty, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(new { Result = false, ErrorMessage = SiteResources.ErrorOccurred });

			if (apiResponse.Data != null && apiResponse.Data.ApplicationNumber != "")
			{

				return Json(new { Result = true, Message = apiResponse.Data.ApplicationNumber + " " + SiteResources.RejectionRefundDonePreviousDescription + " " + apiResponse.Data.RefundDoneDate + " " + SiteResources.RejectionRefundDoneLastDescription });
			}
			else
				return Json(new { Result = false });
		}
		#endregion

		#region ApplicationFile

		public IActionResult PartialAddApplicationFile(string encryptedApplicationId, bool isDamageRejection, bool isExtraFile)
		{
			var viewModel = new AddApplicationFileViewModel
			{
				EncryptedApplicationId = encryptedApplicationId,
				FileSessionId = Guid.NewGuid().ToString(),
				IsDamageRejection = isDamageRejection,
				IsExtraFile = isExtraFile,
			};

			return PartialView("_AddApplicationFile", viewModel);
		}

		public IActionResult PartialTurkmenPageAddApplicationFile(string encryptedApplicationId, bool isDamageRejection, bool isExtraFile, bool isLocalAuthorized, string name, string surname)
		{
			var isFilenamePageAuthorized = false;
			if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object value))
			{
				var roleActions = (RoleActionApiResponse)value;

				isFilenamePageAuthorized = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
					.Any(p => p.RoleActions.Any(q => q.Action.Method == "TurkmenistanFilenameRequirement" && q.Action.IsActive));
			}

			var viewModel = new TurkmenPageAddApplicationFileViewModel
			{
				EncryptedApplicationId = encryptedApplicationId,
				FileSessionId = Guid.NewGuid().ToString(),
				IsDamageRejection = isDamageRejection,
				IsExtraFile = isExtraFile,
				IsLocalAuthorized = isLocalAuthorized,
				Name = name,
				Surname = surname,
				IsFilenamePageAuthorized = isFilenamePageAuthorized
			};

			return PartialView("_TurkmenPageAddApplicationFile", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> SessionUploadFile([FromForm] IFormFile file, string fileSessionId)
		{
			if (file != null && !string.IsNullOrEmpty(fileSessionId))
			{
				using (var ms = new MemoryStream())
				{
					file.CopyTo(ms);
					await _fileStorage.SaveFileAsync(FileStorage.FileStorageBucketName, $"{FileStorage.SessionUploadFileFolderName}/{fileSessionId}", ms, "");
				}

				var fileModel = FileHelper.GetFileInfo(file);
				Extensions.SessionExtensions.Set(HttpContext.Session, $"SessionUploadFile{fileSessionId}", fileModel);
			}

			return Content("");
		}

		public IActionResult SessionRemoveFile([FromForm] IFormFile file, string fileSessionId)
		{
			if (file != null && !string.IsNullOrEmpty(fileSessionId))
			{
				var fileModel = Extensions.SessionExtensions.Get<FileModel>(HttpContext.Session, $"SessionUploadFile{fileSessionId}");
				Extensions.SessionExtensions.Set(HttpContext.Session, $"SessionUploadFile{fileSessionId}", fileModel);
			}

			return Content("");
		}

		[HttpPost]
		public async Task<IActionResult> AddApplicationFile(AddApplicationFileViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var fileModel = Extensions.SessionExtensions.Get<FileModel>(HttpContext.Session, $"SessionUploadFile{viewModel.FileSessionId}");

			if (fileModel == null)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			byte[] bytes;
			using (var ms = new MemoryStream())
			{
				var stream = await _fileStorage.GetFileStreamAsync(FileStorage.FileStorageBucketName, $"{FileStorage.SessionUploadFileFolderName}/{viewModel.FileSessionId}");
				await stream.CopyToAsync(ms);
				bytes = ms.ToArray();
			}

			var addDocumentsApiRequest = new AddDocumentsApiRequest { Documents = new List<AddDocumentApiRequest>() };

			var document = new AddDocumentApiRequest
			{
				DocumentTypeId = (int)DocumentType.Application,
				ReferenceId = viewModel.EncryptedApplicationId.ToDecryptInt(),
				UploadPath = $"{DocumentType.Application}/{viewModel.EncryptedApplicationId}",
				FileName = viewModel.FileName,
				FileExtension = fileModel.FileExtension,
				FileContent = bytes,
				CreatedBy = UserSession.UserId
			};

			addDocumentsApiRequest.Documents.Add(document);

			var addDocumentsApiResponse = await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<AddMultipleApiResponse>>
					(addDocumentsApiRequest, ApiMethodName.DocumentManagement.AddDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

			if (!await addDocumentsApiResponse.Validate(out ResultModel addDocumentsResult).ConfigureAwait(false))
				return Json(addDocumentsResult);

			var addApplicationFileApiRequest = new AddApplicationFileApiRequest
			{
				ApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
				FileTypeId = viewModel.FileTypeId,
				DocumentId = addDocumentsApiResponse.Data.Ids.First(),
				Note = viewModel.Note
			};

			var addApplicationFileApiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
					(addApplicationFileApiRequest, ApiMethodName.Appointment.AddApplicationFile, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await addApplicationFileApiResponse.Validate(out ResultModel addApplicationFileResult).ConfigureAwait(false))
				return Json(addApplicationFileResult);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		[HttpPost]
		public async Task<IActionResult> TurkmenPageAddApplicationFile(TurkmenPageAddApplicationFileViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			if (viewModel.FileTypeId == ApplicationFileType.IHB.ToInt())
			{
				var createIhbFileApiRequest = new CreateIhbFileApiRequest
				{
					MainApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
					IsSuitable = viewModel.IsSuitable ? viewModel.IsSuitable : false,
					IhbNo = viewModel.IhbNo
				};

				var createIhbFileApiResponse = await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<CreateIhbFileApiResponse>>
						(createIhbFileApiRequest, ApiMethodName.DocumentManagement.CreateIhbFile, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (createIhbFileApiResponse.Data == null)
					return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

				var addDocumentsApiRequest = new AddDocumentsApiRequest { Documents = new List<AddDocumentApiRequest>() };

				var document = new AddDocumentApiRequest
				{
					DocumentTypeId = (int)DocumentType.Application,
					ReferenceId = viewModel.EncryptedApplicationId.ToDecryptInt(),
					UploadPath = $"{DocumentType.Application}/{viewModel.EncryptedApplicationId}",
					FileName = viewModel.FileName == null ? string.Empty : viewModel.FileName,
					FileExtension = ".pdf",
					FileContent = createIhbFileApiResponse.Data.FileBytes,
					CreatedBy = UserSession.UserId
				};

				addDocumentsApiRequest.Documents.Add(document);

				var addDocumentsApiResponse = await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<AddMultipleApiResponse>>
						(addDocumentsApiRequest, ApiMethodName.DocumentManagement.AddDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (!await addDocumentsApiResponse.Validate(out ResultModel addDocumentsResult).ConfigureAwait(false))
					return Json(addDocumentsResult);

				var addApplicationFileApiRequest = new AddApplicationFileApiRequest
				{
					ApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
					FileTypeId = viewModel.FileTypeId,
					DocumentId = addDocumentsApiResponse.Data.Ids.First(),
					Note = viewModel.Note,
					IhbDocumentNumber = viewModel.IhbDocumentNumber,
					IsSuitable = viewModel.IsSuitable ? viewModel.IsSuitable : false,
					IhbNo = viewModel.IhbNo
				};

				var addApplicationFileApiResponse = await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<AddApiResponse>>
						(addApplicationFileApiRequest, ApiMethodName.Appointment.AddApplicationFile, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (!await addApplicationFileApiResponse.Validate(out ResultModel addApplicationFileResult).ConfigureAwait(false))
					return Json(addApplicationFileResult);

				var updateApplicationStatusApiRequest = new UpdateSelectedApplicationStatusForApplicationFileApiRequest
				{
					ApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
					ApplicationStatusId = ApplicationStatusType.IHBUploaded.ToInt(),
					UserId = UserSession.UserId
				};

				var updateApplicationStatusApiResponse = await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
						(updateApplicationStatusApiRequest, ApiMethodName.Appointment.UpdateSelectedApplicationStatusForApplicationFile, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders);

				if (!await updateApplicationStatusApiResponse.Validate(out ResultModel updateApplicationStatusResult).ConfigureAwait(false))
					return Json(updateApplicationStatusResult);
			}
			else
			{
				var fileModel = Extensions.SessionExtensions.Get<FileModel>(HttpContext.Session, $"SessionUploadFile{viewModel.FileSessionId}");

				if (fileModel == null)
					return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

				byte[] bytes;
				using (var ms = new MemoryStream())
				{
					var stream = await _fileStorage.GetFileStreamAsync(FileStorage.FileStorageBucketName, $"{FileStorage.SessionUploadFileFolderName}/{viewModel.FileSessionId}");
					await stream.CopyToAsync(ms);
					bytes = ms.ToArray();
				}

				var addDocumentsApiRequest = new AddDocumentsApiRequest { Documents = new List<AddDocumentApiRequest>() };

				var document = new AddDocumentApiRequest
				{
					DocumentTypeId = (int)DocumentType.Application,
					ReferenceId = viewModel.EncryptedApplicationId.ToDecryptInt(),
					UploadPath = $"{DocumentType.Application}/{viewModel.EncryptedApplicationId}",
					FileName = viewModel.FileName == null ? string.Empty : viewModel.FileName,
					FileExtension = fileModel.FileExtension,
					FileContent = bytes,
					CreatedBy = UserSession.UserId
				};

				addDocumentsApiRequest.Documents.Add(document);

				var addDocumentsApiResponse = await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<AddMultipleApiResponse>>
					(addDocumentsApiRequest, ApiMethodName.DocumentManagement.AddDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (!await addDocumentsApiResponse.Validate(out ResultModel addDocumentsResult).ConfigureAwait(false))
					return Json(addDocumentsResult);

				var addApplicationFileApiRequest = new AddApplicationFileApiRequest
				{
					ApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
					FileTypeId = viewModel.FileTypeId,
					DocumentId = addDocumentsApiResponse.Data.Ids.First(),
					Note = viewModel.Note
				};

				var addApplicationFileApiResponse = await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<AddApiResponse>>
						(addApplicationFileApiRequest, ApiMethodName.Appointment.AddApplicationFile, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (!await addApplicationFileApiResponse.Validate(out ResultModel addApplicationFileResult).ConfigureAwait(false))
					return Json(addApplicationFileResult);
			}

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		public async Task<IActionResult> GetPaginatedApplicationFiles([DataSourceRequest] DataSourceRequest request, FilterApplicationFileViewModel filterViewModel)
		{
			var paginationFilter = request.GetPaginationFilter(filterViewModel);

			var apiRequest = new PaginatedApplicationFilesApiRequest
			{
				ApplicationId = filterViewModel.EncryptedApplicationId.ToDecryptInt(),
				HasOnlyApplicationFiles = filterViewModel.HasOnlyApplicationFiles,
				HasLocalRole = filterViewModel.HasLocalRole,
				HasIhbLocalRole = filterViewModel.HasIhbLocalRole,
				IsRejectionList = filterViewModel.IsRejectionList,
				Pagination = new PaginationApiRequest
				{
					Page = paginationFilter.Page,
					PageSize = paginationFilter.PageSize,
					OrderBy = paginationFilter.OrderBy,
					SortDirection = paginationFilter.SortDirection
				}
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationFilesApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.GetPaginatedApplicationFiles, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var paginatedData = new List<ApplicationFileViewModel>();

			if (apiResponse.Data != null && apiResponse.Data.Items.Any())
			{
				paginatedData = apiResponse.Data.Items.First().ApplicationFiles
					.Select(p => new ApplicationFileViewModel
					{
						EncryptedId = p.Id.ToEncrypt(),
						EncryptedApplicationId = p.ApplicationId.ToEncrypt(),
						FileTypeId = p.FileTypeId,
						EncryptedDocumentId = p.DocumentId.ToEncrypt(),
						Note = p.Note,
						CreatedAt = p.CreatedAt.HasValue ? p.CreatedAt.Value.ToString("dd/MM/yyyy HH:mm:ss") : string.Empty,
						CreatedBy = p.CreatedBy
					}).ToList();
			}

			if (paginatedData.Select(p => p.EncryptedDocumentId).Any())
			{
				var documentsApiRequest = new DocumentsApiRequest
				{
					Ids = paginatedData.Select(p => p.EncryptedDocumentId.ToDecryptInt()).ToList(),
					IsFileContentIncluded = false
				};

				var getDocumentsApiResponse = await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<DocumentsApiResponse>>
					(documentsApiRequest, ApiMethodName.DocumentManagement.GetDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (!await getDocumentsApiResponse.Validate(out ResultModel getDocumentsResult).ConfigureAwait(false))
					return Json(getDocumentsResult);

				foreach (var item in paginatedData)
				{
					var document = getDocumentsApiResponse.Data.Documents.FirstOrDefault(p => p.Id == item.EncryptedDocumentId.ToDecryptInt());

					if (document != null)
					{
						item.OriginalFileName = document.OriginalFileName;
						item.UniqueFileName = document.UniqueFileName;
						item.FileExtension = document.FileExtension;
					}
				}
			}

			return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
		}

		[HttpDelete]
		public async Task<IActionResult> DeleteApplicationFile(string encryptedApplicationFileId)
		{
			int id = encryptedApplicationFileId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.DeleteAsync<ApiResponse<DeleteApiResponse>>
				(ApiMethodName.Appointment.DeleteApplicationFile + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		public async Task<IActionResult> DownloadFile(string encryptedDocumentId)
		{
			var documentsApiRequest = new DocumentsApiRequest
			{
				Ids = new List<int> { encryptedDocumentId.ToDecryptInt() },
				IsFileContentIncluded = true
			};

			var getDocumentsApiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<DocumentsApiResponse>>
				(documentsApiRequest, ApiMethodName.DocumentManagement.GetDocuments, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await getDocumentsApiResponse.Validate(out ResultModel getDocumentsResult).ConfigureAwait(false))
				return Json(null);

			var document = getDocumentsApiResponse.Data.Documents.FirstOrDefault();

			if (document == null || document.FileContent == null)
				return Json(null);

			return File(document.FileContent, "application/octet-stream", document.UniqueFileNameWithExtension);
		}

		#endregion

		#region ApplicationNote

		public IActionResult PartialAddApplicationNote(string encryptedApplicationId)
		{
			var viewModel = new AddApplicationNoteViewModel
			{
				EncryptedApplicationId = encryptedApplicationId
			};

			return PartialView("_AddApplicationNote", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> AddApplicationNote(AddApplicationNoteViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var addApplicationNoteApiRequest = new AddApplicationNoteApiRequest
			{
				ApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
				Note = viewModel.Note,
				CreatedBy = UserSession.UserId
			};

			var addApplicationNoteApiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(addApplicationNoteApiRequest, ApiMethodName.Appointment.AddApplicationNote, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await addApplicationNoteApiResponse.Validate(out ResultModel addApplicationNoteResult).ConfigureAwait(false))
				return Json(addApplicationNoteResult);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		public async Task<IActionResult> GetPaginatedApplicationNotes([DataSourceRequest] DataSourceRequest request, FilterApplicationNoteViewModel filterViewModel)
		{
			var paginationFilter = request.GetPaginationFilter(filterViewModel);

			var apiRequest = new PaginatedApplicationNotesApiRequest
			{
				ApplicationId = filterViewModel.EncryptedApplicationId.ToDecryptInt(),
				Pagination = new PaginationApiRequest
				{
					Page = paginationFilter.Page,
					PageSize = paginationFilter.PageSize,
					OrderBy = paginationFilter.OrderBy,
					SortDirection = paginationFilter.SortDirection
				}
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationNotesApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.GetPaginatedApplicationNotes, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var paginatedData = new List<ApplicationNoteViewModel>();

			if (apiResponse.Data != null && apiResponse.Data.Items.Any())
			{
				paginatedData = apiResponse.Data.Items.First().ApplicationNotes
					.Select(p => new ApplicationNoteViewModel
					{
						EncryptedId = p.Id.ToEncrypt(),
						EncryptedApplicationId = p.ApplicationId.ToEncrypt(),
						Note = p.Note,
						CreatedAt = p.CreatedAt,
						CreatedBy = p.CreatedBy,
						CreatedByNameSurname = p.CreatedByNameSurname
					}).ToList();
			}

			return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
		}

		[HttpDelete]
		public async Task<IActionResult> DeleteApplicationNote(string encryptedApplicationNoteId)
		{
			int id = encryptedApplicationNoteId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.DeleteAsync<ApiResponse<DeleteApiResponse>>
				(ApiMethodName.Appointment.DeleteApplicationNote + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region ApplicationOfficialNote

		public IActionResult PartialAddApplicationOfficialNote(string encryptedApplicationId)
		{
			var viewModel = new AddApplicationOfficialNoteViewModel
			{
				EncryptedApplicationId = encryptedApplicationId
			};

			return PartialView("_AddApplicationOfficialNote", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> AddApplicationOfficialNote(AddApplicationOfficialNoteViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var addApplicationOfficialNoteApiRequest = new AddApplicationOfficialNoteApiRequest
			{
				ApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
				OfficialNote = viewModel.OfficialNote,
				CreatedBy = UserSession.UserId
			};

			var addApplicationOfficialNoteApiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(addApplicationOfficialNoteApiRequest, ApiMethodName.Appointment.AddApplicationOfficialNote, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await addApplicationOfficialNoteApiResponse.Validate(out ResultModel addApplicationOfficialNoteResult).ConfigureAwait(false))
				return Json(addApplicationOfficialNoteResult);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		public async Task<IActionResult> GetPaginatedApplicationOfficialNotes([DataSourceRequest] DataSourceRequest request, FilterApplicationOfficialNoteViewModel filterViewModel)
		{
			var paginationFilter = request.GetPaginationFilter(filterViewModel);

			var apiRequest = new PaginatedApplicationOfficialNotesApiRequest
			{
				ApplicationId = filterViewModel.EncryptedApplicationId.ToDecryptInt(),
				Pagination = new PaginationApiRequest
				{
					Page = paginationFilter.Page,
					PageSize = paginationFilter.PageSize,
					OrderBy = paginationFilter.OrderBy,
					SortDirection = paginationFilter.SortDirection
				}
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationOfficialNotesApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.GetPaginatedApplicationOfficialNotes, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var paginatedData = new List<ApplicationOfficialNoteViewModel>();

			if (apiResponse.Data != null && apiResponse.Data.Items.Any())
			{
				paginatedData = apiResponse.Data.Items.First().ApplicationOfficialNotes
					.Select(p => new ApplicationOfficialNoteViewModel
					{
						EncryptedId = p.Id.ToEncrypt(),
						EncryptedApplicationId = p.ApplicationId.ToEncrypt(),
						OfficialNote = p.OfficialNote,
						CreatedAt = p.CreatedAt,
						CreatedBy = p.CreatedBy,
						CreatedByNameSurname = p.CreatedByNameSurname
					}).ToList();
			}

			return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
		}

		[HttpDelete]
		public async Task<IActionResult> DeleteApplicationOfficialNote(string encryptedApplicationOfficialNoteId)
		{
			int id = encryptedApplicationOfficialNoteId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.DeleteAsync<ApiResponse<DeleteApiResponse>>
				(ApiMethodName.Appointment.DeleteApplicationOfficialNote + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region ApplicationVisaDecision

		public async Task<IActionResult> PartialAddApplicationVisaDecision(string encryptedApplicationId, bool isRelationalApplicant)
		{
			if (encryptedApplicationId.IsNullOrWhitespace())
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<GetApplicationVisaDecisionInformationApiResponse>>
					($"{ApiMethodName.Appointment.GetApplicationVisaDecisionInformation + id}/{LanguageId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel ApplicationVisaDecisionInformationResult).ConfigureAwait(false))
				return Json(ApplicationVisaDecisionInformationResult);

			var viewModel = new AddApplicationVisaDecisionViewModel
			{
				EncryptedApplicationId = encryptedApplicationId,
				IsRelationalApplicant = isRelationalApplicant,
				NumberOfEntry = apiResponse.Data.NumberOfEntry,
				VisaDurationId = apiResponse.Data.VisaDurationId ?? 0
			};

			return PartialView("_AddApplicationVisaDecision", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> AddApplicationVisaDecision(AddApplicationVisaDecisionViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			if (viewModel.FileCategory == null)
				viewModel.FileCategory = String.Empty;

			if (viewModel.NumberOfEntry == null)
				viewModel.NumberOfEntry = String.Empty;

			var addApplicationVisaDecisionApiRequest = new AddApplicationVisaDecisionApiRequest
			{
				ApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
				VisaDurationId = viewModel.VisaDurationId,
				VisaDurationOldType = viewModel.VisaDurationOldType,
				NumberOfEntry = viewModel.NumberOfEntry,
				FileCategory = viewModel.FileCategory,
				AddToFamilyMembers = viewModel.AddToFamilyMembers,
				CreatedBy = UserSession.UserId,
				VisaDurationOther = viewModel.VisaDurationOther ?? 0
			};

			var addApplicationVisaDecisionApiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(addApplicationVisaDecisionApiRequest, ApiMethodName.Appointment.AddApplicationVisaDecision, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await addApplicationVisaDecisionApiResponse.Validate(out ResultModel addApplicationVisaDecisionResult).ConfigureAwait(false))
				return Json(addApplicationVisaDecisionResult);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		public async Task<IActionResult> GetPaginatedApplicationVisaDecisions([DataSourceRequest] DataSourceRequest request, FilterApplicationVisaDecisionViewModel filterViewModel)
		{
			var paginationFilter = request.GetPaginationFilter(filterViewModel);

			var apiRequest = new PaginatedApplicationVisaDecisionsApiRequest
			{
				ApplicationId = filterViewModel.EncryptedApplicationId.ToDecryptInt(),
				Pagination = new PaginationApiRequest
				{
					Page = paginationFilter.Page,
					PageSize = paginationFilter.PageSize,
					OrderBy = paginationFilter.OrderBy,
					SortDirection = paginationFilter.SortDirection
				}
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationVisaDecisionsApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.GetPaginatedApplicationVisaDecisions, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var paginatedData = new List<ApplicationVisaDecisionViewModel>();

			if (apiResponse.Data != null)
			{
				paginatedData = apiResponse.Data.Items.First().ApplicationVisaDecisions
					.Select(p => new ApplicationVisaDecisionViewModel
					{
						EncryptedId = p.Id.ToEncrypt(),
						EncryptedApplicationId = p.ApplicationId.ToEncrypt(),
						VisaDurationId = p.VisaDurationId.GetValueOrDefault(),
						VisaDurationOldType = p.VisaDurationOldType,
						NumberOfEntry = p.NumberOfEntry,
						FileCategory = p.FileCategory,
						CreatedAt = p.CreatedAt.DateTime.ToString("dd/MM/yyyy HH:mm"),
						CreatedBy = p.CreatedBy,
						VisaDurationOther = p.VisaDurationOther,
						PolicyPeriod = p.PolicyPeriod
					}).ToList();
			}

			return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
		}

		[HttpDelete]
		public async Task<IActionResult> DeleteApplicationVisaDecision(string encryptedApplicationVisaDecisionId)
		{
			int id = encryptedApplicationVisaDecisionId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.DeleteAsync<ApiResponse<DeleteApiResponse>>
				(ApiMethodName.Appointment.DeleteApplicationVisaDecision + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

        #endregion

        #region ApplicationRelationalServices
        
        public async Task<IActionResult> GetRelatedInsuranceApplications(string encryptedId)
        {
	        var apiResponse = await PortalHttpClientHelper
		        .GetAsync<ApiResponse<ApplicationRelatedServicesApiResponse>>
			        (ApiMethodName.Appointment.GetRelatedServicesApplications + encryptedId.ToDecryptInt(), AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
		        .ConfigureAwait(false);

	        if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
		        return Json(null);
	        
	        CountriesApiResponse nationalities = new CountriesApiResponse();
	        if (MemoryCache.TryGetValue(CacheKeys.CountryCache, out object value2))
	        {
		        nationalities = (CountriesApiResponse)value2;
	        }
			
	        var nationalitiesValue =nationalities.Countries.Select(x => new AddUpdateApplicationViewModel.NationalityViewModel{ Id = x.Id, Name = x.Name.ToTitleCase() });

	        ViewData["defaultNationalities"] = nationalitiesValue;
	        
	        var relatedServices = new List<AddUpdateApplicationViewModel.ReleatedInsuranceApplication>();

	        if (apiResponse.Data != null)
	        {
		        relatedServices = apiResponse.Data.RelatedServices.Select(p => new AddUpdateApplicationViewModel.ReleatedInsuranceApplication
		        {  
                    Id = p.Id,
			        ReleatedInsuranceNationalityId = p.NationalityId,
			        Name = p.Name,
			        Surname = p.Surname,
			        BirthDate = p.BirthDate,
			        PhoneNumber1 = p.PhoneNumber,
			        Email =p.Email,
			        PassportNumber = p.PassportNo,
			        ReleatedInsuranceNationality = new AddUpdateApplicationViewModel.NationalityViewModel()
			        {
				        Id = p.NationalityId,
				        Name = nationalitiesValue.First(c => c.Id == p.NationalityId).Name
			        }
		        }).ToList();
	        }

	        return Json(new DataSourceResult { Data = relatedServices , Total = relatedServices.Count });
        }
        public async Task<IActionResult> GetRelatedServicesApplications(string encryptedId)
        {
            var apiResponse = await PortalHttpClientHelper
                .GetAsync<ApiResponse<ApplicationRelatedServicesApiResponse>>
                (ApiMethodName.Appointment.GetRelatedServicesApplications + encryptedId.ToDecryptInt(), AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
                .ConfigureAwait(false);

            if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                return Json(null);
            
            var apiResponse2 = await PortalHttpClientHelper
	            .GetAsync<ApiResponse<ApplicationSummaryApiResponse>>
	            (ApiMethodName.Appointment.GetSanitizedApplicationSummary + encryptedId.ToDecryptInt(), AppSettings.PortalGatewayApiUrl,
		            PortalGatewayApiDefaultRequestHeaders)
	            .ConfigureAwait(false);


            var hasAuthorizedRoleForUpdatePolicy = false;
            var hasAuthorizedRoleForSendingSms = false;

            if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out object value2))
            {
	            var roleActions = (RoleActionApiResponse)value2;

	            hasAuthorizedRoleForUpdatePolicy = roleActions.RoleActionSites
		            .Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
		            .Any(p => p.RoleActions.Any(q => q.Action.Method == "CancelInsurance" && q.Action.IsActive));

                hasAuthorizedRoleForSendingSms = roleActions.RoleActionSites.Where(p => UserSession.RoleIds.Any(x => x == p.Role.Id))
                    .Any(p => p.RoleActions.Any(q => q.Action.Method == "RelatedInsuranceSendingSmsRoleCheck" && q.Action.IsActive));
            }

			Boolean releatedInsuranceForPolicyCheck = false;

            if (apiResponse2.Data.Insurance.PolicyCount == apiResponse.Data.RelatedServices.Count() &&
                apiResponse.Data.RelatedServices.Count() > 0)
                releatedInsuranceForPolicyCheck = true;

            Boolean releatedInsuranceForSmsHistoryCheck = false;

			if (releatedInsuranceForPolicyCheck)
			{
				var smsHistoryApiResponse = await PortalHttpClientHelper
					.GetAsync<ApiResponse<ApplicationSmsHistoryApiResponse>>
					(ApiMethodName.Appointment.GetApplicationSmsHistory + encryptedId.ToDecryptInt(), AppSettings.PortalGatewayApiUrl,
						PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (smsHistoryApiResponse.Data.ApplicationSmsHistories.Where(q=>q.IsRelatedInsurance && !q.IsDeleted).Count() > 1)
				{
                    if (hasAuthorizedRoleForSendingSms)
                    {
                        releatedInsuranceForSmsHistoryCheck = false;
                    }
                    else
                    {
                        releatedInsuranceForSmsHistoryCheck = true;
                    }
                }
			}


			var relatedServices = new List<ApplicationRelatedServicesViewModel>();

            if (apiResponse.Data != null)
            {
                relatedServices = [.. apiResponse.Data.RelatedServices.Select(p => new ApplicationRelatedServicesViewModel
                {
                    Id = "N" + p.Id.ToApplicationNumber(),
                    EncryptedInsuranceId = p.Id.ToEncrypt(),
                    ApplicationType = p.ApplicationType,
                    PassportNo = p.PassportNo,
                    Name = p.Name,
                    Surname = p.Surname,
                    BirthDate = p.BirthDateText,
                    PolicyNo = p.PolicyNo,
                    StartDate = p.StartDate,
                    EndDate = p.EndDate,
                    UpdateReleatedPolicy = apiResponse2.Data.Insurance.UpdateReleatedPolicy,
                    RoleCheckUpdatePolicy = hasAuthorizedRoleForUpdatePolicy,
					ReleatedInsuranceForPolicyCheck = releatedInsuranceForPolicyCheck,
					ReleatedInsuranceForSmsHistoryCheck = releatedInsuranceForSmsHistoryCheck,
                    RelatedInsuranceSmsUrl = string.IsNullOrEmpty(p.SmsLink)
                        ? string.Empty : p.SmsLink,
                })];
            }

            return Json(new DataSourceResult { Data = relatedServices });
        }
        
        public async Task<IActionResult> PrintReleatedInsurance(string encryptedApplicationId,string encryptedReleatedInsuraneId,string branchCountryIso3)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}
			
			var viewModel = new ApplicationViewModel
			{
				EncryptedId = encryptedApplicationId,
				ReleatedEncryptedId = encryptedReleatedInsuraneId,
				BranchCountryIso3 = branchCountryIso3
			};

			return PartialView("_ReleatedInsurancePolicy", viewModel);
		}
        #endregion

        #region ApplicationSurvey

        public async Task<IActionResult> DetailSurveyDubai(string encryptedId, string BranchCountryIso3)
		{

			if (string.IsNullOrEmpty(encryptedId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedId.ToDecryptInt();

			var apiResponseSurvey = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationIsSurveyExistApiResponse>>
				(ApiMethodName.Appointment.IsExistSurvey + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var viewModel = new AddApplicationSurveyDubaiViewModel { };

			if (apiResponseSurvey.Data.isExistSurvey)
			{
				var apiResponse = await PortalHttpClientHelper
					.GetAsync<ApiResponse<ApplicationUpdateSurveyDubaiApiResponse>>
					($"{ApiMethodName.Appointment.UpdateApplicationSurveyDubai + id}/{LanguageId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				viewModel = new AddApplicationSurveyDubaiViewModel
				{
					EncryptedApplicationId = id,
					BranchCountryIso3 = BranchCountryIso3,
					Nationality = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.Nationality).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.Nationality).FirstOrDefault(),
					ApplicantsRelationship = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ApplicantsRelationship).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ApplicantsRelationship).FirstOrDefault(),
					VisaCategory = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.VisaCategory).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.VisaCategory).FirstOrDefault(),
					TotalYearInCountry = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TotalYearInCountry).FirstOrDefault(),
					ReimbursementSponsorDetail = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ReimbursementSponsorDetail).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ReimbursementSponsorDetail).FirstOrDefault(),
					Job = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.Job).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.Job).FirstOrDefault(),
					MonthlyIncome = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.MonthlyIncome).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.MonthlyIncome).FirstOrDefault(),
					BankBalance = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.BankBalance).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.BankBalance).FirstOrDefault(),
					IdentityInformationChangesYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.IdentityInformationChangesYes).FirstOrDefault(),
					IdentityInformationChangesNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.IdentityInformationChangesNo).FirstOrDefault(),
					IdentityInformationChangesReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.IdentityInformationChangesReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.IdentityInformationChangesReason).FirstOrDefault(),
					RelativesInTurkeyYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RelativesInTurkeyYes).FirstOrDefault(),
					RelativesInTurkeyNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RelativesInTurkeyNo).FirstOrDefault(),
					RelativesInTurkeyReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RelativesInTurkeyReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RelativesInTurkeyReason).FirstOrDefault(),
					PropertyInTurkeyYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInTurkeyYes).FirstOrDefault(),
					PropertyInTurkeyNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInTurkeyNo).FirstOrDefault(),
					PropertyInTurkeyReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInTurkeyReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInTurkeyReason).FirstOrDefault(),
					PropertyInUaeYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInUaeYes).FirstOrDefault(),
					PropertyInUaeNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInUaeNo).FirstOrDefault(),
					PropertyInUaeReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInUaeReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInUaeReason).FirstOrDefault(),
					AppliedTurkishVisaBeforeYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AppliedTurkishVisaBeforeYes).FirstOrDefault(),
					AppliedTurkishVisaBeforeNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AppliedTurkishVisaBeforeNo).FirstOrDefault(),
					AppliedTurkishVisaBeforeReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AppliedTurkishVisaBeforeReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AppliedTurkishVisaBeforeReason).FirstOrDefault(),
					RejectedTurkishVisaApplicationYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RejectedTurkishVisaApplicationYes).FirstOrDefault(),
					RejectedTurkishVisaApplicationNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RejectedTurkishVisaApplicationNo).FirstOrDefault(),
					RejectedTurkishVisaApplicationReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RejectedTurkishVisaApplicationReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RejectedTurkishVisaApplicationReason).FirstOrDefault(),
					ObtainedTurkishVisaYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ObtainedTurkishVisaYes).FirstOrDefault(),
					ObtainedTurkishVisaNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ObtainedTurkishVisaNo).FirstOrDefault(),
					ObtainedTurkishVisaReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ObtainedTurkishVisaReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ObtainedTurkishVisaReason).FirstOrDefault(),
					TravelHistoryToTurkeyYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelHistoryToTurkeyYes).FirstOrDefault(),
					TravelHistoryToTurkeyNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelHistoryToTurkeyNo).FirstOrDefault(),
					TravelHistoryToTurkeyReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelHistoryToTurkeyReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelHistoryToTurkeyReason).FirstOrDefault(),
					AnyTravelBanToTurkeyYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AnyTravelBanToTurkeyYes).FirstOrDefault(),
					AnyTravelBanToTurkeyNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AnyTravelBanToTurkeyNo).FirstOrDefault(),
					AnyTravelBanToTurkeyReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AnyTravelBanToTurkeyReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AnyTravelBanToTurkeyReason).FirstOrDefault(),
					ShengenUsaUkIrelandVisaYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ShengenUsaUkIrelandVisaYes).FirstOrDefault(),
					ShengenUsaUkIrelandVisaNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ShengenUsaUkIrelandVisaNo).FirstOrDefault(),
					ShengenUsaUkIrelandVisaReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ShengenUsaUkIrelandVisaReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ShengenUsaUkIrelandVisaReason).FirstOrDefault(),
					TravelToOtherCountriesYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelToOtherCountriesYes).FirstOrDefault(),
					TravelToOtherCountriesNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelToOtherCountriesNo).FirstOrDefault(),
					TravelToOtherCountriesReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelToOtherCountriesReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelToOtherCountriesReason).FirstOrDefault(),
					VisaOfficerObservation = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.VisaOfficerObservation).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.VisaOfficerObservation).FirstOrDefault(),
					ProcessedBy = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ProcessedBy).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ProcessedBy).FirstOrDefault()
				};
			}
			else
			{
				var apiResponse = await PortalHttpClientHelper
					.GetAsync<ApiResponse<ApplicationSurveyDubaiApiResponse>>
					(ApiMethodName.Appointment.GetSanitizedApplicationDubaiSurvey + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				viewModel = new AddApplicationSurveyDubaiViewModel
				{
					EncryptedApplicationId = id,
					BranchCountryIso3 = BranchCountryIso3,
					Nationality = apiResponse.Data.ApplicationSurveyDubaies.Select(p => p.Nationality).FirstOrDefault(),
					ApplicantsRelationship = string.Empty,
					VisaCategory = apiResponse.Data.ApplicationSurveyDubaies.Select(p => p.VisaCategory).FirstOrDefault(),
					TotalYearInCountry = apiResponse.Data.ApplicationSurveyDubaies.Select(p => p.TotalYearInCountry).FirstOrDefault(),
					ReimbursementSponsorDetail = apiResponse.Data.ApplicationSurveyDubaies.Select(p => p.ReimbursementSponsorDetail).FirstOrDefault(),
					Job = apiResponse.Data.ApplicationSurveyDubaies.Select(p => p.Job).FirstOrDefault(),
					MonthlyIncome = apiResponse.Data.ApplicationSurveyDubaies.Select(p => p.MonthlyIncome).FirstOrDefault(),
					BankBalance = apiResponse.Data.ApplicationSurveyDubaies.Select(p => p.BankBalance).FirstOrDefault(),
					IdentityInformationChangesYes = false,
					IdentityInformationChangesNo = false,
					IdentityInformationChangesReason = string.Empty,
					RelativesInTurkeyYes = false,
					RelativesInTurkeyNo = false,
					RelativesInTurkeyReason = string.Empty,
					PropertyInTurkeyYes = false,
					PropertyInTurkeyNo = false,
					PropertyInTurkeyReason = string.Empty,
					PropertyInUaeYes = false,
					PropertyInUaeNo = false,
					PropertyInUaeReason = string.Empty,
					AppliedTurkishVisaBeforeYes = false,
					AppliedTurkishVisaBeforeNo = false,
					AppliedTurkishVisaBeforeReason = string.Empty,
					RejectedTurkishVisaApplicationYes = false,
					RejectedTurkishVisaApplicationNo = false,
					RejectedTurkishVisaApplicationReason = string.Empty,
					ObtainedTurkishVisaYes = false,
					ObtainedTurkishVisaNo = false,
					ObtainedTurkishVisaReason = string.Empty,
					TravelHistoryToTurkeyYes = false,
					TravelHistoryToTurkeyNo = false,
					TravelHistoryToTurkeyReason = string.Empty,
					AnyTravelBanToTurkeyYes = false,
					AnyTravelBanToTurkeyNo = false,
					AnyTravelBanToTurkeyReason = string.Empty,
					ShengenUsaUkIrelandVisaYes = false,
					ShengenUsaUkIrelandVisaNo = false,
					ShengenUsaUkIrelandVisaReason = string.Empty,
					TravelToOtherCountriesYes = false,
					TravelToOtherCountriesNo = false,
					TravelToOtherCountriesReason = string.Empty,
					VisaOfficerObservation = string.Empty,
					ProcessedBy = apiResponse.Data.ApplicationSurveyDubaies.Select(p => p.ProcessedBy).FirstOrDefault()
				};
			}

			if (BranchCountryIso3 == "RUS")
				return PartialView("_AddUpdateApplicationSurveyRus", viewModel);
			else
				return PartialView("_AddUpdateApplicationSurveyDubai", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> SaveApplicationSurveyDubai(AddApplicationSurveyDubaiViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var addApplicationSurveyApiRequest = new SaveApplicationSurveyDubaiApiRequest
			{
				ApplicationId = viewModel.EncryptedApplicationId,
				Nationality = viewModel.Nationality,
				ApplicantsRelationship = viewModel.ApplicantsRelationship,
				VisaCategory = viewModel.VisaCategory,
				TotalYearInCountry = viewModel.TotalYearInCountry,
				ReimbursementSponsorDetail = viewModel.ReimbursementSponsorDetail,
				Job = viewModel.Job,
				MonthlyIncome = viewModel.MonthlyIncome,
				BankBalance = viewModel.BankBalance,
				IdentityInformationChangesYes = viewModel.IdentityInformationChangesYes,
				IdentityInformationChangesNo = viewModel.IdentityInformationChangesNo,
				IdentityInformationChangesReason = viewModel.IdentityInformationChangesReason,
				RelativesInTurkeyYes = viewModel.RelativesInTurkeyYes,
				RelativesInTurkeyNo = viewModel.RelativesInTurkeyNo,
				RelativesInTurkeyReason = viewModel.RelativesInTurkeyReason,
				PropertyInTurkeyYes = viewModel.PropertyInTurkeyYes,
				PropertyInTurkeyNo = viewModel.PropertyInTurkeyNo,
				PropertyInTurkeyReason = viewModel.PropertyInTurkeyReason,
				PropertyInUaeYes = viewModel.PropertyInUaeYes,
				PropertyInUaeNo = viewModel.PropertyInUaeNo,
				PropertyInUaeReason = viewModel.PropertyInUaeReason,
				AppliedTurkishVisaBeforeYes = viewModel.AppliedTurkishVisaBeforeYes,
				AppliedTurkishVisaBeforeNo = viewModel.AppliedTurkishVisaBeforeNo,
				AppliedTurkishVisaBeforeReason = viewModel.AppliedTurkishVisaBeforeReason,
				RejectedTurkishVisaApplicationYes = viewModel.RejectedTurkishVisaApplicationYes,
				RejectedTurkishVisaApplicationNo = viewModel.RejectedTurkishVisaApplicationNo,
				RejectedTurkishVisaApplicationReason = viewModel.RejectedTurkishVisaApplicationReason,
				ObtainedTurkishVisaYes = viewModel.ObtainedTurkishVisaYes,
				ObtainedTurkishVisaNo = viewModel.ObtainedTurkishVisaNo,
				ObtainedTurkishVisaReason = viewModel.ObtainedTurkishVisaReason,
				TravelHistoryToTurkeyYes = viewModel.TravelHistoryToTurkeyYes,
				TravelHistoryToTurkeyNo = viewModel.TravelHistoryToTurkeyNo,
				TravelHistoryToTurkeyReason = viewModel.TravelHistoryToTurkeyReason,
				AnyTravelBanToTurkeyYes = viewModel.AnyTravelBanToTurkeyYes,
				AnyTravelBanToTurkeyNo = viewModel.AnyTravelBanToTurkeyNo,
				AnyTravelBanToTurkeyReason = viewModel.AnyTravelBanToTurkeyReason,
				ShengenUsaUkIrelandVisaYes = viewModel.ShengenUsaUkIrelandVisaYes,
				ShengenUsaUkIrelandVisaNo = viewModel.ShengenUsaUkIrelandVisaNo,
				ShengenUsaUkIrelandVisaReason = viewModel.ShengenUsaUkIrelandVisaReason,
				TravelToOtherCountriesYes = viewModel.TravelToOtherCountriesYes,
				TravelToOtherCountriesNo = viewModel.TravelToOtherCountriesNo,
				TravelToOtherCountriesReason = viewModel.TravelToOtherCountriesReason,
				VisaOfficerObservation = viewModel.VisaOfficerObservation,
				ProcessedBy = viewModel.ProcessedBy,
			};

			var addApplicationSurveyApiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(addApplicationSurveyApiRequest, ApiMethodName.Appointment.SaveApplicationSurveyDubai, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await addApplicationSurveyApiResponse.Validate(out ResultModel addApplicationSurveyResult).ConfigureAwait(false))
				return Json(addApplicationSurveyResult);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		public async Task<IActionResult> GetPaginatedApplicationSurvey([DataSourceRequest] DataSourceRequest request, FilterApplicationSurveyViewModel filterViewModel)
		{
			var paginationFilter = request.GetPaginationFilter(filterViewModel);

			var apiRequest = new PaginatedApplicationSurveyApiRequest
			{
				ApplicationId = filterViewModel.EncryptedApplicationId.ToDecryptInt(),
				Pagination = new PaginationApiRequest
				{
					Page = paginationFilter.Page,
					PageSize = paginationFilter.PageSize,
					OrderBy = paginationFilter.OrderBy,
					SortDirection = paginationFilter.SortDirection
				}
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<ApplicationSurveyApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.GetPaginatedApplicationSurvey, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var paginatedData = new List<ApplicationSurveyViewModel>();

			if (apiResponse.Data != null && apiResponse.Data.Items.Any())
			{
				paginatedData = apiResponse.Data.Items.First().ApplicationSurvey
					.Select(p => new ApplicationSurveyViewModel
					{
						EncryptedId = p.Id.ToEncrypt(),
						EncryptedApplicationId = p.ApplicationId.ToEncrypt(),
					}).ToList();
			}

			return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
		}

		[HttpDelete]
		public async Task<IActionResult> DeleteApplicationSurveyDubaiAsync(string EncryptedApplicationId)
		{
			int id = EncryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.DeleteAsync<ApiResponse<DeleteApiResponse>>
				($"{ApiMethodName.Appointment.DeleteApplicationSurveyDubai + id}/{UserSession.UserId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		[HttpGet]
		public async Task<IActionResult> IsExistSurvey(string encryptedId)
		{

			int id = encryptedId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationIsSurveyExistApiResponse>>
				(ApiMethodName.Appointment.IsExistSurvey + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			return Json(apiResponse.Data.isExistSurvey);
		}

		[HttpGet]
		public async Task<IActionResult> ApplicationPrintSurvey(string encryptedId, string BranchCountryIso3)
		{
			int id = encryptedId.ToDecryptInt();

			var apiResponseSurvey = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationIsSurveyExistApiResponse>>
				(ApiMethodName.Appointment.IsExistSurvey + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);


			if (!apiResponseSurvey.Data.isExistSurvey)
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationUpdateSurveyDubaiApiResponse>>
				($"{ApiMethodName.Appointment.UpdateApplicationSurveyDubai + id}/{LanguageId}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var viewModel = new AddApplicationSurveyDubaiViewModel
			{
				EncryptedApplicationId = id,
				BranchCountryIso3 = BranchCountryIso3,
				Nationality = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.Nationality).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.Nationality).FirstOrDefault(),
				ApplicantsRelationship = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ApplicantsRelationship).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ApplicantsRelationship).FirstOrDefault(),
				VisaCategory = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.VisaCategory).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.VisaCategory).FirstOrDefault(),
				TotalYearInCountry = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TotalYearInCountry).FirstOrDefault(),
				ReimbursementSponsorDetail = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ReimbursementSponsorDetail).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ReimbursementSponsorDetail).FirstOrDefault(),
				Job = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.Job).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.Job).FirstOrDefault(),
				MonthlyIncome = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.MonthlyIncome).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.MonthlyIncome).FirstOrDefault(),
				BankBalance = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.BankBalance).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.BankBalance).FirstOrDefault(),
				IdentityInformationChangesYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.IdentityInformationChangesYes).FirstOrDefault(),
				IdentityInformationChangesNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.IdentityInformationChangesNo).FirstOrDefault(),
				IdentityInformationChangesReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.IdentityInformationChangesReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.IdentityInformationChangesReason).FirstOrDefault(),
				RelativesInTurkeyYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RelativesInTurkeyYes).FirstOrDefault(),
				RelativesInTurkeyNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RelativesInTurkeyNo).FirstOrDefault(),
				RelativesInTurkeyReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RelativesInTurkeyReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RelativesInTurkeyReason).FirstOrDefault(),
				PropertyInTurkeyYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInTurkeyYes).FirstOrDefault(),
				PropertyInTurkeyNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInTurkeyNo).FirstOrDefault(),
				PropertyInTurkeyReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInTurkeyReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInTurkeyReason).FirstOrDefault(),
				PropertyInUaeYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInUaeYes).FirstOrDefault(),
				PropertyInUaeNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInUaeNo).FirstOrDefault(),
				PropertyInUaeReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInUaeReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.PropertyInUaeReason).FirstOrDefault(),
				AppliedTurkishVisaBeforeYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AppliedTurkishVisaBeforeYes).FirstOrDefault(),
				AppliedTurkishVisaBeforeNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AppliedTurkishVisaBeforeNo).FirstOrDefault(),
				AppliedTurkishVisaBeforeReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AppliedTurkishVisaBeforeReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AppliedTurkishVisaBeforeReason).FirstOrDefault(),
				RejectedTurkishVisaApplicationYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RejectedTurkishVisaApplicationYes).FirstOrDefault(),
				RejectedTurkishVisaApplicationNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RejectedTurkishVisaApplicationNo).FirstOrDefault(),
				RejectedTurkishVisaApplicationReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RejectedTurkishVisaApplicationReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.RejectedTurkishVisaApplicationReason).FirstOrDefault(),
				ObtainedTurkishVisaYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ObtainedTurkishVisaYes).FirstOrDefault(),
				ObtainedTurkishVisaNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ObtainedTurkishVisaNo).FirstOrDefault(),
				ObtainedTurkishVisaReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ObtainedTurkishVisaReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ObtainedTurkishVisaReason).FirstOrDefault(),
				TravelHistoryToTurkeyYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelHistoryToTurkeyYes).FirstOrDefault(),
				TravelHistoryToTurkeyNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelHistoryToTurkeyNo).FirstOrDefault(),
				TravelHistoryToTurkeyReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelHistoryToTurkeyReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelHistoryToTurkeyReason).FirstOrDefault(),
				AnyTravelBanToTurkeyYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AnyTravelBanToTurkeyYes).FirstOrDefault(),
				AnyTravelBanToTurkeyNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AnyTravelBanToTurkeyNo).FirstOrDefault(),
				AnyTravelBanToTurkeyReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AnyTravelBanToTurkeyReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.AnyTravelBanToTurkeyReason).FirstOrDefault(),
				ShengenUsaUkIrelandVisaYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ShengenUsaUkIrelandVisaYes).FirstOrDefault(),
				ShengenUsaUkIrelandVisaNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ShengenUsaUkIrelandVisaNo).FirstOrDefault(),
				ShengenUsaUkIrelandVisaReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ShengenUsaUkIrelandVisaReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ShengenUsaUkIrelandVisaReason).FirstOrDefault(),
				TravelToOtherCountriesYes = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelToOtherCountriesYes).FirstOrDefault(),
				TravelToOtherCountriesNo = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelToOtherCountriesNo).FirstOrDefault(),
				TravelToOtherCountriesReason = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelToOtherCountriesReason).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.TravelToOtherCountriesReason).FirstOrDefault(),
				VisaOfficerObservation = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.VisaOfficerObservation).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.VisaOfficerObservation).FirstOrDefault(),
				ProcessedBy = apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ProcessedBy).FirstOrDefault() == null ? string.Empty : apiResponse.Data.ApplicationUpdateSurveyDubaies.Select(p => p.ProcessedBy).FirstOrDefault()
			};

			if (BranchCountryIso3 == "RUS")
				return View("ApplicationPrintSurveyRus", viewModel);
			else
				return View("ApplicationPrintSurvey", viewModel);
		}
		#endregion

		#region EmaaHasarServisExcel
		[ActionAttribute(IsMenuItem = true)]
		public ActionResult EmaaHasarExcelUpdate()
		{
			return View();
		}
		public async Task<IActionResult> GetPaginatedEmaaHasarServisExcel([DataSourceRequest] DataSourceRequest request)
		{

			var apiRequest = new PaginatedEmaaHasarServisApiRequest
			{
				Pagination = new PaginationApiRequest
				{
					Page = request.Page,
					PageSize = request.PageSize
				}
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<PaginationApiResponse<EmaaHasarServisExcelListApiResponse>>>
				(apiRequest, ApiMethodName.Appointment.GetSanitizedPaginatedEmaaHasarServisExcel, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(null);

			var paginatedData = new List<SendHasarServiceExcelViewModel>();

			if (apiResponse.Data != null && apiResponse.Data.Items.Any())
			{
				paginatedData = apiResponse.Data.Items.First().EmaaHasarServisExcelListe
					.Select(p => new SendHasarServiceExcelViewModel
					{
						EncryptedId = p.Id.ToEncrypt(),
						Ulke = p.Ulke,
						Sube = p.Sube,
						Tarih = p.Tarih,
						Police = p.Police,
						Pasaport = p.Pasaport,
						Ad = p.Ad,
						Soyad = p.Soyad,
						Tutar = p.Tutar,
						Durum = p.Durum,
						DosyaNo = p.DosyaNo,
						ServiceResponse = p.ServiceResponse,
						PoliceBaslangicTarihi = p.PoliceBaslangicTarihi
					}).ToList();
			}


			return Json(new DataSourceResult { Data = paginatedData, Total = apiResponse.Data.TotalItemCount });
		}

		[HttpPost]
		public async Task<IActionResult> EmaaHasarExcelUpdate([DataSourceRequest] DataSourceRequest request,
		 [Bind(Prefix = "models")] IEnumerable<SendHasarServiceExcelViewModel> hasarServisExcel)
		{
			if (hasarServisExcel != null && ModelState.IsValid)
			{

				var apiRequest1 = new PaginatedEmaaHasarServisApiRequest
				{
					Pagination = new PaginationApiRequest
					{
						Page = request.Page,
						PageSize = request.PageSize
					}
				};

				var apiResponse1 = await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<PaginationApiResponse<EmaaHasarServisExcelListApiResponse>>>
					(apiRequest1, ApiMethodName.Appointment.GetSanitizedPaginatedEmaaHasarServisExcel, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				var apiRequest = new SendToHasarServisExcelListApiRequest()
				{
					SendToHasarServisExcelList = apiResponse1.Data.Items.First().EmaaHasarServisExcelListe.Where(p => p.DosyaNo == "1").OrderBy(p => p.Id).Select(p => new SendToHasarServisExcelApiRequest()
					{
						Id = p.Id,
						Police = p.Police,
						Pasaport = p.Pasaport,
						Ad = p.Ad,
						Soyad = p.Soyad,
						Tutar = p.Tutar,
						Tarih = p.PoliceBaslangicTarihi != "" ? p.PoliceBaslangicTarihi : p.Tarih
					}).ToList()
				};

				var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<ClaimEntryPolicyApiResponse>>
				(apiRequest, ApiMethodName.Insurance.ClaimLossEntryExcel, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

				if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
					return Json(result);

			}
			return Json(hasarServisExcel.ToDataSourceResult(request, ModelState));
		}
		#endregion

		#region ApplicationVisaCategoryReferanceNumber

		public async Task<IActionResult> ApplicationVisaCategoryReferanceNumber(string encryptedApplicationId, string referenceNumberType, int visaCategoryId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<ApplicationVisaCategoryReferanceNumberApiResponse>>
				(ApiMethodName.Appointment.GetApplicationVisaCategoryReferenceNumber + id, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var viewModel = new ApplicationVisaCategoryReferanceNumberViewModel();

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return PartialView("_ApplicationVisaCategoryReferanceNumber", viewModel);

			viewModel.Email = apiResponse.Data.Email;
			viewModel.Id = apiResponse.Data.Id;
			viewModel.ApplicationNumber = apiResponse.Data.ApplicationNumber;
			viewModel.PhoneNumber1 = apiResponse.Data.PhoneNumber1;
			viewModel.ReferenceNumber = apiResponse.Data.ReferenceNumber;
			viewModel.IsAvailableForSendSms = apiResponse.Data.IsAvailableForSendSms;
			viewModel.BranchCountryIso3 = apiResponse.Data.BranchCountryIso3;
			viewModel.ReferenceNumberType = referenceNumberType;
			viewModel.VisaCategoryId = visaCategoryId;

			return PartialView("_ApplicationVisaCategoryReferanceNumber", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> SendReferenceNumberNotification(SendReferenceNumberNotificationViewModel model)
		{
			if (model == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			if (string.IsNullOrEmpty(model.Email) && string.IsNullOrEmpty(model.PhoneNumber))
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var referanceNumberNotificationRequest = new SendReferenceNumberNotificationApiRequest
			{
				SendNotification = AppSettings.Notification,
				ApplicationId = model.Id,
				Email = model.Email,
				PhoneNumber = model.PhoneNumber,
				ReferenceNumber = model.ReferenceNumber,
				IsSmsNotification = !string.IsNullOrEmpty(model.PhoneNumber),
				UserId = UserSession.UserId,
				ReferenceNumberType = model.ReferenceNumberType,
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
					(referanceNumberNotificationRequest, ApiMethodName.Appointment.SendReferenceNumberNotification, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		[HttpGet]
		public async Task<IActionResult> SendReleatedInsuranceSms(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification",
					new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}
            
			
			var request = new SendInviduallInsuranceSmsApiRequest
			{
				ApplicationId = encryptedApplicationId.ToDecryptInt(),
				UserId = UserSession.UserId,
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
					(request, ApiMethodName.Appointment.SendInvidualInsuranceSms, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}
		
		#endregion

		#region ApplicationConfirmationCode

		public async Task<IActionResult> ApplicationConfirmationCode(string encryptedApplicationId, int codeTypeId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			var request = new ApplicationConfirmationCodeApiRequest
			{
				ApplicationId = encryptedApplicationId.ToDecryptInt(),
				CodeTypeId = codeTypeId
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<ApplicationConfirmationCodeApiResponse>>
				(request, ApiMethodName.Appointment.GetApplicationConfirmationCode, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var viewModel = new ApplicationConfirmationCodeViewModel();

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return PartialView(codeTypeId == (int)ConfirmationCodeType.SmsCode ?
					"_ApplicationConfirmationCodeSms" :
					"_ApplicationConfirmationCodeMail", viewModel);

			viewModel.PhoneNumber1 = apiResponse.Data.PhoneNumber1;
			viewModel.Email = apiResponse.Data.Email;
			viewModel.SendCount = apiResponse.Data.SendCount;
			viewModel.ConfirmationTypeId = apiResponse.Data.ConfirmationTypeId;
			viewModel.ShowApprovalButton = apiResponse.Data.SendCount > 0;
			viewModel.ShowNotificationButton = apiResponse.Data.SendCount < 2;

			return PartialView(codeTypeId == (int)ConfirmationCodeType.SmsCode ?
				"_ApplicationConfirmationCodeSms" :
				"_ApplicationConfirmationCodeMail", viewModel);
		}

		[HttpPost]
		public async Task<IActionResult> SendConfirmationCodeNotification(SendConfirmationCodeNotificationViewModel model)
		{
			if (model == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var referanceNumberNotificationRequest = new SendConfirmationCodeNotificationApiRequest
			{
				PhoneNumber = model.PhoneNumber,
				Email = model.Email,
				ApplicationId = model.EncryptedId.ToDecryptInt(),
				SendNotification = AppSettings.Notification,
				UserId = UserSession.UserId,
				IsConfirmationTypeUpdated = false,
				CodeTypeId = model.CodeTypeId
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
					(referanceNumberNotificationRequest, ApiMethodName.Appointment.SendConfirmationCodeNotification, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Data = apiResponse.Data.ConfirmationCode, Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		[HttpPost]
		public async Task<IActionResult> UpdateConfirmationType(UpdateConfirmationTypeViewModel model)
		{
			if (model == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var updateConfirmationTypeRequest = new UpdateApplicationConfirmationCodeApiRequest
			{
				ConfirmationTypeId = model.ConfirmationTypeId,
				ApplicationId = model.EncryptedId.ToDecryptInt(),
				UserId = UserSession.UserId,
				IsConfirmationTypeUpdated = true,
				CodeTypeId = model.CodeTypeId
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<UpdateApplicationConfirmationCodeApiResponse>>
					(updateConfirmationTypeRequest, ApiMethodName.Appointment.UpdateConfirmationType, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region ApplicationRejectionApprovalStatus

		[HttpGet]
		public async Task<IActionResult> PartialRejectionApprovalStatus(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<GetApplicationRejectionApprovalStatusApiResponse>>
				(ApiMethodName.Appointment.GetApplicationRejectionApprovalStatus + $"{encryptedApplicationId.ToDecryptInt()}", AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var viewModel = new ApplicationRejectionApprovalStatusViewModel();

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			var lastHistoryData = apiResponse.Data.RejectionApprovalHistories.OrderByDescending(o => o.CreatedAt).FirstOrDefault();

			viewModel.EncryptedApplicationId = encryptedApplicationId;
			viewModel.PhoneNumber = apiResponse.Data.PhoneNumber;
			viewModel.Email = apiResponse.Data.Email;
			viewModel.NotificationRetryCount = apiResponse.Data.NotificationRetryCount;
			viewModel.LastApprovalStatusHistory.HistoryId = lastHistoryData?.HistoryId;
			viewModel.LastApprovalStatusHistory.CreatedAt = lastHistoryData?.CreatedAt;

			return PartialView("_ApplicationRejectionApprovalStatus", viewModel);
		}

		[HttpPatch]
		public async Task<IActionResult> CompleteRejectionApprovalHistory(int historyId)
		{
			if (!historyId.IsNumericAndGreaterThenZero())
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var request = new CompleteRejectionApprovalHistoryApiRequest
			{
				HistoryId = historyId
			};

			var apiResponse = await PortalHttpClientHelper
				.PutAsJsonAsync<ApiResponse<UpdateApiResponse>>
					(request, ApiMethodName.Appointment.CompleteRejectionApprovalHistory, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		[HttpPost]
		public async Task<IActionResult> VerifyRejectionApprovalCode(int historyId, string code)
		{
			if (!historyId.IsNumericAndGreaterThenZero())
				return Json(new ResultModel { Message = EnumResources.SendApprovalCodeFirst, ResultType = ResultType.Danger });

			if (code.IsNullOrWhitespace())
				return Json(new ResultModel { Message = EnumResources.EnterCodeFirst, ResultType = ResultType.Danger });

			var request = new VerifyRejectionApprovalCodeApiRequest
			{
				HistoryId = historyId,
				Code = code,
				UserId = UserSession.UserId
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<VerifyRejectionApprovalCodeApiResponse>>
					(request, ApiMethodName.Appointment.VerifyRejectionApprovalCode, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		[HttpPost]
		public async Task<IActionResult> SendRejectionApprovalNotification(ApplicationRejectionApprovalStatusViewModel model)
		{
			if (model == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var sendViaNotification = model.SendAsEmail || model.SendAsSms;

			if (sendViaNotification)
			{
				var apiRequest = new SendRejectionApprovalNotificationApiRequest
				{
					ApplicationId = model.EncryptedApplicationId.ToDecryptInt(),
					BranchId = UserSession.BranchId.Value,
					UserId = UserSession.UserId,
					SendAsEmail = model.SendAsEmail,
					SendAsSms = model.SendAsSms,
					SendToSupervisor = model.SendToSupervisor,
					Email = model.Email,
					PhoneNumber = model.PhoneNumber,
					SendNotification = sendViaNotification,
				};

				var apiResponse = await PortalHttpClientHelper
					.PostAsJsonAsync<ApiResponse<SendRejectionApprovalNotificationApiResponse>>
						(apiRequest, ApiMethodName.Appointment.SendRejectionApprovalNotification, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
					.ConfigureAwait(false);

				if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
					return Json(result);

				return Json(new ResultModel { Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
			}

			return Json(new ResultModel { Message = ResultMessage.ErrorOccurred.ToDescription(), ResultType = ResultType.Danger });
		}

		[HttpGet]
		public async Task<IActionResult> LoadSupervisorVerificationPage(string encryptedApplicationId)
		{
			return PartialView("_ApplicationSupervisorVerificationPage", new ApplicationSupervisorVerificationViewModel() { EncryptedApplicationId = encryptedApplicationId });
		}

		[HttpPost]
		public async Task<IActionResult> CheckSupervisorInformation(ApplicationSupervisorVerificationViewModel model)
		{
			if (model == null || !ModelState.IsValid || model.Username == null || model.SupervisorPassword == null || !model.Username.Contains("/"))
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var request = new CheckSupervisorInformationApiRequest
			{
				BranchId = UserSession.BranchId.Value,
				UserId = UserSession.UserId,
				ApplicationId = model.EncryptedApplicationId.ToDecryptInt(),
				Email = model.Username.Split("/")[1],
                Username = model.Username.Split("/")[1],
                Password = _sessionSettings.Authentication == SessionSettings.LdapAuthenticationOptionValue ?
					model.SupervisorPassword
					: model.SupervisorPassword.ToHash(),			
			};

			var url = _sessionSettings.Authentication == SessionSettings.LdapAuthenticationOptionValue ?
                ApiMethodName.Appointment.CheckSupervisorInformationWithLdap
                : ApiMethodName.Appointment.CheckSupervisorInformation;

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<CheckSupervisorInformationApiResponse>>
					(request, url, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Data = apiResponse.Data, Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		[HttpPost]
		public async Task<IActionResult> SendKsaIcrGenerateEIvoice(string EncryptedApplicationId)
		{

			var apiRequest = new SendKsaIcrGenerateEInvoiceApiRequest
			{
				ApplicationId = EncryptedApplicationId.ToDecryptInt(),
				CreatedBy = UserSession.UserId,
				CreatedAt = DateTime.Now,
				ExtraFeeIds = new List<int>()
			};

			var apiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<UpdateApiResponse>>
					(apiRequest, ApiMethodName.Appointment.SendKsaIcrGenerateEInvoice, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		#endregion

		#region Inquiry

		public async Task<IActionResult> DetailApplicationInquiry(string encryptedApplicationId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<GetApplicationInquiryApiResponse>>
				(ApiMethodName.Appointment.GetApplicationInquiry + id + "/" + LanguageId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var viewModel = new ApplicationInquiryViewModel();

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return PartialView("_ApplicationInquiry", viewModel);

			viewModel = new ApplicationInquiryViewModel()
			{
				EncryptedApplicationId = encryptedApplicationId,
				AvailableInquires = apiResponse.Data.AvailableInquiries.Where(s => !apiResponse.Data.Inquiries.Select(k => k.Id).Contains(s.Id)).Select(s => new AvailableInquiryViewModel()
				{
					Id = s.Id,
					Name = s.Name
				}).ToList(),
				Inquiries = apiResponse.Data.Inquiries.Select(s => new InquiryViewModel()
				{
					Id = s.Id,
					BranchId = s.BranchId,
					Name = s.Name,
					InquiryQuestions = s.InquiryQuestions.Select(q => new InquiryQuestionViewModel()
					{
						Id = q.Id,
						Name = q.Name,
						QuestionTypeId = q.QuestionTypeId,
						IsMultiSelectable = q.IsMultiSelectable,
						ColSpan = q.ColSpan,
						DescriptionColSpan = q.DescriptionColSpan,
						IsRequired = q.IsRequired,
						IsDescriptionIncluded = q.IsDescriptionIncluded,
						ElectiveAnswer = q.ElectiveAnswer == null ? new InquiryElectiveAnswerViewModel() : new InquiryElectiveAnswerViewModel()
						{
							Description = q.ElectiveAnswer?.Description,
							Ids = q.ElectiveAnswer?.Ids
						},
						ExplanationAnswers = q.ExplanationAnswers == null ? new List<InquiryExplanationAnswerViewModel>() : q.ExplanationAnswers.Select(s => new InquiryExplanationAnswerViewModel()
						{
							Explanation = s.Explanation,
							Id = s.Id
						}).ToList(),
                        Choices = q.Choices.Select(c => new InquiryQuestionChoiceViewModel()
						{
							Id = c.Id,
							ChoiceTypeId = c.ChoiceTypeId,
							ColSpan = c.ColSpan,
							IsChecked = q.ElectiveAnswer.Ids.Contains(c.Id),
							Name = c.Name,
                            Selections = q.SelectionAnswers is null ? new List<int>() : c.Selections.Where(k => q.SelectionAnswers.Select(r => r.Id).Contains(k.Id)).Select(s => s.Id).ToList(),
                        }).ToList()
					}).ToList()
				}).ToList()
			};

			return PartialView("_ApplicationInquiry", viewModel);
		}

		public async Task<IActionResult> AddApplicationInquiryPartial(string applicationId, int inquiryId)
		{
			if (string.IsNullOrEmpty(applicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = applicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<GetInquiryInformationApiResponse>>
				(ApiMethodName.Appointment.GetInquiryInformation + inquiryId + "/" + LanguageId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var viewModel = new AddUpdateApplicationInquiryViewModel();

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return PartialView("_AddUpdateApplicationInquiry", viewModel);

			viewModel = new AddUpdateApplicationInquiryViewModel()
			{
				EncryptedApplicationId = applicationId,
				InquiryId = inquiryId,
				Name = apiResponse.Data.Name,
				InquiryQuestions = apiResponse.Data.InquiryQuestions.Select(q => new InquiryQuestionViewModel()
				{
					Id = q.Id,
					Name = q.Name,
					QuestionTypeId = q.QuestionTypeId,
					IsMultiSelectable = q.IsMultiSelectable,
					IsRequired = q.IsRequired,
					ColSpan = q.ColSpan,
					DescriptionColSpan = q.DescriptionColSpan,
					IsDescriptionIncluded = q.IsDescriptionIncluded,
					ElectiveAnswer = q.ElectiveAnswer == null ? new InquiryElectiveAnswerViewModel() : new InquiryElectiveAnswerViewModel()
					{
						Description = q.ElectiveAnswer?.Description,
						Ids = q.ElectiveAnswer?.Ids
					},
					ExplanationAnswers = q.ExplanationAnswers == null ? new List<InquiryExplanationAnswerViewModel>() : q.ExplanationAnswers.Select(s => new InquiryExplanationAnswerViewModel()
					{
						Explanation = s.Explanation,
						Id = s.Id
					}).ToList(),
                    Choices = q.Choices.Select(c => new InquiryQuestionChoiceViewModel()
					{
						Id = c.Id,
						ChoiceTypeId = c.ChoiceTypeId,
						ColSpan = c.ColSpan,
						Name = c.Name,
                        Selections = q.SelectionAnswers is null ? new List<int>() : c.Selections.Where(k => q.SelectionAnswers.Select(r => r.Id).Contains(k.Id)).Select(s => s.Id).ToList()
                    }).ToList()
				}).ToList()
			};

			return PartialView("_AddUpdateApplicationInquiry", viewModel);
		}

		public async Task<IActionResult> UpdateApplicationInquiryPartial(string applicationId, int inquiryId)
		{
			if (string.IsNullOrEmpty(applicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = applicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.GetAsync<ApiResponse<GetApplicationInquiryApiResponse>>
					(ApiMethodName.Appointment.GetApplicationInquiry + id + "/" + LanguageId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			var viewModel = new AddUpdateApplicationInquiryViewModel();

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return PartialView("_AddUpdateApplicationInquiry", viewModel);

			if (apiResponse.Data.Inquiries.FirstOrDefault(s => s.Id == inquiryId) == null)
				return PartialView("_AddUpdateApplicationInquiry", viewModel);

			viewModel = new AddUpdateApplicationInquiryViewModel()
			{
				EncryptedApplicationId = applicationId,
				InquiryId = inquiryId,
				Observation = apiResponse.Data.Inquiries.FirstOrDefault(s => s.Id == inquiryId)?.Observation,
				Name = apiResponse.Data.Inquiries.FirstOrDefault(s => s.Id == inquiryId)?.Name,
				InquiryQuestions = apiResponse.Data.Inquiries.FirstOrDefault(s => s.Id == inquiryId).InquiryQuestions.Select(q => new InquiryQuestionViewModel()
				{
					Id = q.Id,
					Name = q.Name,
					QuestionTypeId = q.QuestionTypeId,
					IsMultiSelectable = q.IsMultiSelectable,
					IsRequired = q.IsRequired,
					ColSpan = q.ColSpan,
					DescriptionColSpan = q.DescriptionColSpan,
					IsDescriptionIncluded = q.IsDescriptionIncluded,
					ElectiveAnswer = q.ElectiveAnswer == null ? new InquiryElectiveAnswerViewModel() : new InquiryElectiveAnswerViewModel()
					{
						Description = q.ElectiveAnswer?.Description,
						Ids = q.ElectiveAnswer?.Ids
					},
					ExplanationAnswers = q.ExplanationAnswers == null ? new List<InquiryExplanationAnswerViewModel>() : q.ExplanationAnswers.Select(s => new InquiryExplanationAnswerViewModel()
					{
						Explanation = s.Explanation,
						Id = s.Id
					}).ToList(),
                    Choices = q.Choices.Select(c => new InquiryQuestionChoiceViewModel()
					{
						Id = c.Id,
						Name = c.Name,
						ColSpan = c.ColSpan,
						ChoiceTypeId = c.ChoiceTypeId,
						IsChecked = q.ElectiveAnswer.Ids.Contains(c.Id),
						Explanation = q.ExplanationAnswers.FirstOrDefault(f => f.Id == c.Id)?.Explanation,
                        Selections = q.SelectionAnswers is null ? new List<int>() : c.Selections.Where(k => q.SelectionAnswers.Select(r => r.Id).Contains(k.Id)).Select(s => s.Id).ToList()
                    }).ToList()
				}).ToList()
			};

			return PartialView("_AddUpdateApplicationInquiry", viewModel);
		}

		public async Task<IActionResult> DeleteApplicationInquiry(string encryptedApplicationId, int inquiryId)
		{
			if (string.IsNullOrEmpty(encryptedApplicationId))
			{
				TempData.Put("Notification", new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });
				return RedirectToAction("List", "Application", new { Area = "Appointment" });
			}

			int id = encryptedApplicationId.ToDecryptInt();

			var apiResponse = await PortalHttpClientHelper
				.DeleteAsync<ApiResponse<DeleteApiResponse>>
					(ApiMethodName.Appointment.DeleteApplicationInquiry + id + "/" + inquiryId, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
				return Json(result);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

		public async Task<IActionResult> SaveApplicationInquiry(AddUpdateApplicationInquiryViewModel viewModel)
		{
			if (viewModel == null || !ModelState.IsValid)
				return Json(new ResultModel { Message = EnumResources.MissingOrInvalidData, ResultType = ResultType.Danger });

			var addApplicationInquiryApiRequest = new SaveApplicationInquiryApiRequest
			{
				ApplicationId = viewModel.EncryptedApplicationId.ToDecryptInt(),
				InquiryId = viewModel.InquiryId,
				BranchId = viewModel.BranchId,
				Observation = viewModel.Observation,
				Answers = viewModel.InquiryQuestions.Select(s => new AddUpdateInquiryAnswerDto
				{
					QuestionId = s.Id,
					ChoiceIds = s.Choices.Where(c => c.IsChecked).Select(k => k.Id).ToList(),
					ExplanationAnswers = s.Choices.Where(s => s.Explanation is not null).Select(k => new AddUpdateInquiryExplanationAnswerDto()
					{
						QuestionId = s.Id,
						ChoiceId = k.Id,
						Explanation = k.Explanation
					}).ToList(),
					SelectionAnswers = s.Choices.Where(s => s.Selections is not null && s.Selections.Any()).Select(s => new AddUpdateInquirySelectionAnswerDto
                    {
						ChoiceId = s.Id,
						SelectionIds = s.Selections.Select(s => s).ToList()
                    }).ToList(),
					Description = s.ElectiveAnswer?.Description
				}).ToList()
			};

			var addApplicationSurveyApiResponse = await PortalHttpClientHelper
				.PostAsJsonAsync<ApiResponse<AddApiResponse>>
				(addApplicationInquiryApiRequest, ApiMethodName.Appointment.SaveApplicationInquiry, AppSettings.PortalGatewayApiUrl, PortalGatewayApiDefaultRequestHeaders)
				.ConfigureAwait(false);

			if (!await addApplicationSurveyApiResponse.Validate(out ResultModel addApplicationSurveyResult).ConfigureAwait(false))
				return Json(addApplicationSurveyResult);

			return Json(new ResultModel { Message = ResultMessage.OperationIsSuccessful.ToDescription(), ResultType = ResultType.Success });
		}

        #endregion

        [HttpPost]
        public async Task<IActionResult> AddIhbOrder([DataSourceRequest] DataSourceRequest request,
            [Bind(Prefix = "models")] IEnumerable<ApplicationViewModel> applications)
        {
            if (applications != null)
            {
				//validate parameters

                foreach (var app in applications)
                {
                    // set default values if not set
                    app.IhbDocumentNumber = "1";

                    if (app.IhbNumber.IsNullOrWhitespace())
                    {
                        applications.ToList()[0].IhbOrderErrorMessage = $"{SiteResources.IHBNumberNotFound} {app.PassportNumber} / {app.Name} {app.Surname} ";
                        var result1 = applications.ToDataSourceResult(request, ModelState);

                        return Json(new
                        {
                            Data = result1.Data,
                            Total = result1.Total,
                            AggregateResults = result1.AggregateResults,
                            Errors = result1.Errors,
                            IhbOrderErrorMessage = applications.ToList()[0].IhbOrderErrorMessage
                        });
                    }

                    if (app.IhbDocumentNumber.IsNullOrWhitespace())
                    {
                        applications.ToList()[0].IhbOrderErrorMessage = $"{SiteResources.IHBDocumentNumberNotFound} {app.PassportNumber} / {app.Name} {app.Surname} ";
                        var result2 = applications.ToDataSourceResult(request, ModelState);

                        return Json(new
                        {
                            Data = result2.Data,
                            Total = result2.Total,
                            AggregateResults = result2.AggregateResults,
                            Errors = result2.Errors,
                            IhbOrderErrorMessage = applications.ToList()[0].IhbOrderErrorMessage
                        });
                    }

                    if (!app.IhbStatus.Id.IsNumericAndGreaterThenZero())
                    {
                        applications.ToList()[0].IhbOrderErrorMessage = $"{SiteResources.IHBStatusNotFound} {app.PassportNumber} / {app.Name} {app.Surname} ";
                        var result3 = applications.ToDataSourceResult(request, ModelState);

                        return Json(new
                        {
                            Data = result3.Data,
                            Total = result3.Total,
                            AggregateResults = result3.AggregateResults,
                            Errors = result3.Errors,
                            IhbOrderErrorMessage = applications.ToList()[0].IhbOrderErrorMessage
                        });
                    }
                }

                var apiRequest = new AddIhbOrderApiRequest()
                {
                    IhbOrders = applications.Select(s => new IhbOrderDto()
                    {
                        CreatedBy = UserSession.UserId,
                        ApplicationId = s.EncryptedId.ToDecryptInt(),
                        IhbDocumentNumber = s.IhbDocumentNumber,
                        IhbNumber = s.IhbNumber,
                        IsSuitable = s.IhbStatus.Id is (int)IhbStatus.Suitable ? true : false,
                    }).ToList()
                };

                var apiResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiRequest, ApiMethodName.Appointment.AddIhbOrder, AppSettings.PortalGatewayApiUrl,
                        PortalGatewayApiDefaultRequestHeaders)
                    .ConfigureAwait(false);

                if (!await apiResponse.Validate(out ResultModel result).ConfigureAwait(false))
                    return Json(result);

                return Json(applications.ToDataSourceResult(request, ModelState));
            }

            return Json(applications.ToDataSourceResult(request, ModelState));
        }

        #region Private Methods

        private string GetInsuranceRefundMessageForScanSycle(int typeId, DateTime? date)
        {
			var existInsuranceMessage = SiteResources.InsuranceRefundCheckMessageScanSycle;

			if(date != null)
                existInsuranceMessage = existInsuranceMessage.Replace("[DATE]", LanguageId == 1 ? date.Value.ToString("dd.MM.yyyy") : date.Value.ToString("dd.MM.yyyy").ConvertToFormattedDate());
			else
                existInsuranceMessage = existInsuranceMessage.Replace("[DATE]", string.Empty);

			existInsuranceMessage = existInsuranceMessage.Replace("[TYPE]", GetInsurancePolicyVisaMessage(typeId));

			return existInsuranceMessage;
        }

        private int? CheckRejectionFeePolicyTypeByPeriod(int? policyPeriod)
        {
            return policyPeriod switch
            {
                88 or 89 or 90 => (int)RejectedApplicationFeePolicyType.ThreeMonth,
                178 or 179 or 180 => (int)RejectedApplicationFeePolicyType.SixMonth,
                364 or 365 or 366 => (int)RejectedApplicationFeePolicyType.OneYear,
                _ => null
            };
        }

        public string GetInsurancePolicyVisaMessage(int typeId)
		{
			switch (typeId)
			{
				case (int)BranchInsuranceRefundSettingType.NoRestrictionPolicyRefund:
					return EnumResources.NoRestrictionPolicyRefund;
                case (int)BranchInsuranceRefundSettingType.ThreeMonthInsurance:
                    return EnumResources.ThreeMonthInsuranceRefund;
                case (int)BranchInsuranceRefundSettingType.SixMonthInsurance:
                    return EnumResources.SixMonthInsuranceRefund;
                case (int)BranchInsuranceRefundSettingType.OneYearInsurance:
                    return EnumResources.OneYearInsuranceRefund;
                default:
                    return string.Empty;
			}
		}

        #endregion

    }
}
